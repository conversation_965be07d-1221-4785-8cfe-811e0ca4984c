export interface ChatTableQuery extends PageQuery {
  keyword?: string;
  startTime: string;
  endTime: string;
  timeArray: string[];
}
// 问答qa
export interface UserChatResult {
  id: number;
  name: string;
  totalTime: number | string;
  totalCount: number;
  maxScore: number;
}
export interface ScriptChatResult {
  id: number;
  name: string;
  type: number;
  scoringStandardId: number;
  scoringStandardName: string;
  usedCount: number;
  createTime: string;
}

export interface DataAnalyseVo {
  averageMinTime?: number | string;
  uv: number;
  averageScore: number;
  averageTime: number | string;
  averageCount: number;
}
export interface scriptDataAnalyseVo {
  refDate?: string;
  chatPersonNum: number;
  averageScore: number;
  averageTime: number;
  averageNum: number;
}
/**
 * UserDataDetailAnalyseVo
 */
export interface UserDataDetailAnalyseVo {
  /**
   * 平均分
   */
  averageScore: number;
  /**
   * 平均时长
   */
  averageTime: number;
  /**
   * 最高分
   */
  maxScore: number;
  /**
   * 总次数
   */
  totalCount: number;
  /**
   * 总时长
   */
  totalTime: number;
  avatar: string;
  name: string;
}
/**
 * ChatReportVo，报告vo
 */
export interface ChatReportVo {
  /**
   * 时间
   */
  createTime: Date;
  id: number;
  status: number;
  /**
   * 得分
   */
  score: number;
  /**
   * 脚本名称
   */
  scriptName: string;
  /**
   * 脚本类型
   */
  scriptType: number;
  /**
   * 时长（秒）
   */
  timeLong: number;
  emanName: string;
  occupation: string;
  department: string;
}
/**
 * ChatReportDetailVo
 */
export interface ChatReportDetailVo {
  /**
   * 用户头像
   */
  avatar: string;
  chatId: number;
  status: number;
  /**
   * 会话开始时间
   */
  createTime: Date;
  /**
   * 维度
   */
  dimension: string;
  /**
   * 会话完成时间
   */
  finishTime: Date;
  /**
   * 用户名称
   */
  name: string;
  /**
   * 问答详情
   */
  qaList: ChatQADetailVo[];
  /**
   * 是否随机 0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目
   */
  randomFlag: number;
  /**
   * 随机数量
   */
  randomNum: number;
  /**
   * 场景
   */
  scene: string;
  /**
   * 综合得分
   */
  score: number;
  /**
   * 脚本名称
   */
  scriptName: string;
  /**
   * 整体评价
   */
  sumup: string;
  /**
   * 脚本类型，1-技巧类，2-答题类
   */
  type: number;
  reGenerateReport: boolean;
}

/**
 * ChatQADetailVo
 */
export interface ChatQADetailVo {
  referenceAnswer?: string;
  referenceAnswerShow?: boolean;
  /**
   * 回答
   */
  answer: string;
  /**
   * 点评
   */
  comment: string;
  /**
   * 问题id
   */
  qaId: number;
  /**
   * 问题
   */
  question: string;
  /**
   * 分数
   */
  score: number;
  /**
   * 建议
   */
  suggest: string;
}
/**
 * ChatHistoryDetailVo
 */
export interface ChatHistoryDetailVo {
  avatar: string;
  content: string;
  createTime: Date;
  audioFlag: boolean;
  audioUrl: string;
  id: number;
  role: string;
  updateTime: Date;
}

/**
 * ScriptAnalyseVo
 */
export interface ScriptAnalyseVo {
  averageNum: number;
  averageScore: number;
  averageTime: number;
  coreDatalist: scriptDataAnalyseVo[];
  questionAverageScoreList: QuestionAverageScoreVo[];
  scoreList: number[];
  skillAverageScoreList: SkillAverageScoreVo[];
  type: number;
  usePerson: number;
  [property: string]: any;
}

/**
 * QuestionAverageScoreVo
 */
export interface QuestionAverageScoreVo {
  averageScore: number;
  num: number;
  qaId: number;
  question: string;
  [property: string]: any;
}

/**
 * SkillAverageScoreVo
 */
export interface SkillAverageScoreVo {
  averageScore: number;
  fullScore: number;
  name: string;
  [property: string]: any;
}
/**
 * ScriptDataListAnalyseVo
 */
export interface ScriptDataListAnalyseVo {
  /**
   * 得分分布
   */
  scoreList: number[];
  /**
   * 核心数据
   */
  scriptRangeData: scriptDataAnalyseVo[];
  scriptRangeDataSummary: scriptDataAnalyseVo;
  [property: string]: any;
}

export interface PPTSummaryVo {
  id: number;
  answer?: string;
  audioUrl?: string;
}
