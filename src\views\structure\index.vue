<template>
  <component
    :is="componentList.get(currentCom)"
    :id="currentId"
    :is-edit="isEdit"
    @reload="isReload?getList:''"
    v-model:flag="currentType"
    :listType="listType"
    @change-type="handleChangeType"
    :handleId="handleId"
  />
</template>

<script setup>
import List from './list.vue'
import Add from './add.vue'
import Detail from './detail.vue'


/** 当前显示组件切换判断 */
//  组件列表
// const componentList = {
//   List,
//   Add
// }

const componentList = new Map([
  ['list',List],
  ['add', Add],
  ['detail', Detail]
])

const handleId=ref() //冻住的Id
const currentCom = ref('list') // 当前组件
const currentId = ref() // 当前行数据id
const currentType = ref()
const isReload = ref()
const listType = ref(null)

const isEdit=ref(false)
// 监听组件切换事件-改变当前显示
const handleChangeType = payload => {
  currentId.value = payload.id
  currentType.value = payload.flagType
  currentCom.value= payload?.flagType
  isReload.value=payload?.isReload
  isEdit.value=payload?.isEdit
  listType.value=payload?.listType
  handleId.value=payload?.handleId
}
</script>

<style lang="scss" scoped></style>
