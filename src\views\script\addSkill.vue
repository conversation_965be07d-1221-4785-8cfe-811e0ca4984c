<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            创建脚本</span
          >
          <div class="right-button">
            <el-button plain type="primary" @click="handleGenerate">AI生成</el-button>
            <el-button plain type="info" @click="back()">取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" ref="scrollDiv">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="100px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="脚本名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="产品" prop="product">
                <el-select clearable v-model="formParams.product" placeholder="请选择或者输入" filterable allow-create>
                  <el-option v-for="item in productList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="评分标准" prop="scoringStandardId">
                <el-select v-model="formParams.scoringStandardId" placeholder="请选择">
                  <el-option v-for="item in scoringStandardArray" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="applicableObject">
                <template #label>
                  <span class="label-text">适用对象</span>
                  <el-tooltip content="此脚本是给谁用的，比如：医药代表、药店职员等。" placement="top">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-select clearable v-model="formParams.applicableObject" placeholder="请选择">
                  <el-option v-for="item in targetList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="visitObject">
                <template #label>
                  <span class="label-text">拜访对象</span>
                  <el-tooltip content="此次拜访假设的拜访对象是谁，比如：医生、店员等。" placement="top">
                    <el-icon class="label-tip">
                      <!--  <img src="@/assets/icons/svg/help1.svg" class="help" /> -->
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-select clearable v-model="formParams.visitObject" placeholder="请选择" @change="visitorChange">
                  <el-option v-for="item in visitorList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="departmentReactive.show">
            <el-col :span="16">
              <el-form-item :label="departmentReactive.label" prop="department">
                <el-cascader
                  v-if="departmentReactive.toggleSelect === 1"
                  v-model="formParams.department"
                  :options="departmentList"
                  :props="{
              value: 'name',
              label: 'name',
              children: 'subName',
              multiple: true
            }"
                  clearable
                  multiple
                  filterable
                  @change="departmentChange"
                />
                <el-select
                  v-if="departmentReactive.toggleSelect === 2"
                  clearable
                  filterable
                  v-model="formParams.department"
                  @change="departmentChange"
                  placeholder="请选择"
                >
                  <el-option v-for="item in shopLevelList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select
                  v-if="departmentReactive.toggleSelect === 3"
                  clearable
                  filterable
                  v-model="formParams.department"
                  @change="departmentChange"
                  placeholder="请选择"
                >
                  <el-option v-for="item in levelExcutiveList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16">
              <el-form-item prop="backdrop">
                <template #label>
                  <span class="label-text">背景</span>
                  <el-tooltip content="本次拜访的聊天背景是怎么样的" placement="top">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <!-- <div class="select-way" v-show="selectWayShow">
                  <el-button type="primary" @click="showInputBackdrop('ai')">AI改写</el-button>
                  <el-button @click="showInputBackdrop('input')">直接输入</el-button>
                </div> -->
                <div class="backdrop-text">
                  <el-input
                    v-model="formParams.backdrop"
                    :autosize="{ minRows: 6 }"
                    type="textarea"
                    placeholder="请输入拜访背景，人物称呼使用变量【拜访对象】代替"
                  />
                  <span class="ai" @click="showInputBackdrop('ai')">AI改写</span>
                </div>

                <div class="tips">
                  <img src="@/assets/icons/png/info.png" class="help" />
                  为了保持E人称呼的一致性，达到更好的对话效果，请确保背景里的人物称呼使用【拜访对象】代替，不要出现人物姓名或科室职称信息，比如张三、张医生、张主任等。参考：【拜访对象】是一位医生，针对儿童青少年的近视治疗，他尚未形成早防早控的治疗理念...
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16">
              <el-form-item prop="goal">
                <template #label>
                  <span class="label-text">目标</span>
                  <el-tooltip content="本次拜访期望达成的目标" placement="top">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>

                <!-- <div class="select-way" v-show="selectWayGoalShow">
                  <el-button type="primary" @click="showInputGoal('ai')">AI改写</el-button>
                  <el-button @click="showInputGoal('input')">直接输入</el-button>
                </div> -->
                <div class="backdrop-text">
                  <el-input
                    v-model="formParams.goal"
                    :autosize="{ minRows: 2 }"
                    type="textarea"
                    placeholder="请输入拜访目标，人物称呼使用变量【拜访对象】代替"
                  />
                  <span class="ai" @click="showInputGoal('ai')">AI改写</span>
                </div>

                <div class="tips">
                  <img src="@/assets/icons/png/info.png" class="help" />
                  为了保持E人称呼的一致性，达到更好的对话效果，请确保目标里的人物称呼使用变量【拜访对象】代替，不要出现人物姓名或科室职称信息，比如张三、张医生、张主任等。参考：期望【拜访对象】针对xx患者首诊处方xx药品...
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16">
              <el-form-item prop="keyIssue" class="key-issus">
                <template #label>
                  <span class="label-text">关键问题</span>
                  <el-tooltip content="设定关键问题后，AI将在适当的时候提出这些关键问题，请用关键字的形式书写。" placement="top">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <tag-select v-model="formParams.keyIssue" :tags="issueTags" :new-tags="newTags" />
                <div class="tips">请选择2-10个关键问题</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="keyInfo">
                <template #label>
                  <span class="label-text">关键信息</span>
                  <el-tooltip content="在这个产品、背景、目标下，销售需要传递的关键信息。可以理解为关键问题的答案。" placement="top">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-input placeholder="请输入" v-model="formParams.keyInfo" :autosize="{ minRows: 4 }" type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16">
              <el-form-item label="拜访地点" prop="location">
                <tag-select v-model="formParams.location" :multi="false" :tags="locationTags" :new-tags="newLocationTags" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24" class="flex">
            <el-col :span="9">
              <el-form-item label="时限" prop="timeLimit"><el-input v-model="formParams.timeLimit" placeholder="请输入" /> </el-form-item>
            </el-col>
            <span style="margin-bottom: 18px;">分钟</span>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <BackgroundDialog
      v-model:visible="backgroundDialogShow"
      :dialog-type="dialogType"
      @sendBackdrop="getAIText"
      :pre-text="preText"
    ></BackgroundDialog>

    <ScriptCreateDialog
      v-model:visible="scriptCreateDialogShow"
      dialog-type="edit"
      :params="generateScriptForm"
      @on-success="onCreateScriptSuccess"
    ></ScriptCreateDialog>

    <ScriptCreateStatusDialog
      v-model:visible="scriptCreateStatusDialogShow"
      :scriptId="generateScriptId"
      :params="generateScriptForm"
      @on-success="onScriptStatusScuess"
    />
  </div>
</template>

<script setup name="AddSkill" lang="ts">
import { reactive, ref } from 'vue'
import { getAllProduct, getChatReportScoringStandard, saveOrUpdateScript, getDepartmentList, getGenerateResult } from '@/api/script'
import { ChatReportScoringStandardVo, scriptFormVo } from '@/api/script/types'
import type { FormRules } from 'element-plus'
import TagSelect from './components/TagSelect.vue'
import BackgroundDialog from './components/background.vue'
import ScriptCreateDialog from './components/scriptCreateDialog.vue'
import ScriptCreateStatusDialog from './components/scriptCreateStatusDialog.vue'

import { visitorList, targetList, shopLevelList, levelExcutiveList, issueTags, locationTags, convertArrayToObject } from './components/data';
import { ElMessageBox, ElLoading } from 'element-plus'
import { moveToError } from '@/utils'

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  extra: {
    type: Object,
  }
})

const scrollDiv = ref()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
const btnLoading = ref(false)
const scoringStandardArray = ref<ChatReportScoringStandardVo[]>([])
const productList = ref<string[]>([])
const dialogType = ref('')
const departmentList = ref<any[]>();
const formParams = ref<scriptFormVo>({
  type: 1,
  name: '',
  timeLimit: null,
  scoringStandardId: null,
  product: '',
  applicableObject: null,
  visitObject: '',
  department: null,
  backdrop: '',
  goal: '',
  keyIssue: [],
  keyInfo: '',
  location: '',
})
const inputBackdropShow = ref(false)
const selectWayShow = ref(true)
const backgroundDialogShow = ref(false)

const selectWayGoalShow = ref(true)
const inputGoalShow = ref(false)
const preText = ref<any>('')
const newTags = ref<{ inputVisible: boolean, value: string }[]>([])
const newLocationTags = ref<{ inputVisible: boolean, value: string }[]>([])

const scriptCreateDialogShow = ref(false)
const scriptCreateStatusDialogShow = ref(false)

const generateScriptId = ref('')
const generateScriptForm = ref<{
  visitObject: string
  visitStage: string
  keyInfo: string
}>()

interface RuleForm {
  name: string
  timeLimit: string | null,
  type: number,
  scoringStandardId: string | number | null,
  product: string
  applicableObject: string | null,
  visitObject: string,
  department?: string | string[] | null,
  backdrop: string
  goal: string,
  keyIssue: string[]
  keyInfo: string
}

const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 2000) {
    callback(new Error('最多2000个字符'))
  } else {
    callback()
  }
}

const checkTimeLimit = (rule: any, value: any, callback: any) => {
  if (value > 60) {
    callback(new Error('最多60分钟'))
  } else {
    callback()
  }
}


const checkProductLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('最多64个字符'))
  } else {
    callback()
  }
}

// 定义自定义规则函数
const checkArrayLength = (rule: any, value: any, callback: any) => {
  if (value.length < 2 || value.length === 0) {
    callback(new Error('至少选择2个关键词'));
  } else if (value.length > 10) {
    callback(new Error('最多选择10个关键词'));
  } else {
    callback();
  }
};

const checkDepartment = (rule: any, value: any, callback: any) => {
  switch (formParams.value.visitObject) {
    case visitorList[0]:
    case visitorList[1]:
    case visitorList[2]:
    case visitorList[3]:
      if (!value || value.length === 0) {
        callback(new Error('请选择科室'))
      } else {
        callback();
      }
      break;

    case visitorList[5]:
      if (!value) {
        callback(new Error('请选择级别'))
      } else {
        callback();
      }
      break;
    default:
      callback();
  }
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入脚本名称', trigger: 'blur' },
    {
      validator: checkProductLimit,
      trigger: ["blur"]
    }
  ],
  timeLimit: [
    { required: true, message: '请输入时限', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkTimeLimit,
      trigger: 'blur'
    }
  ],
  scoringStandardId: [
    { required: true, message: '请选择评分标准', trigger: ["blur", "change"] },
  ],
  product: [
    { required: true, message: '请输入产品', trigger: ["blur", "change"] },
    {
      validator: checkProductLimit,
      trigger: ["blur", "change"]
    }
  ],
  applicableObject: [
    { required: true, message: '请选择适用对象', trigger: ["blur", "change"] },
  ],
  visitObject: [
    { required: true, message: '请选择拜访对象', trigger: ["blur", "change"] },
  ],
  department: [{ required: true, validator: checkDepartment, trigger: ["change"] }],
  backdrop: [
    { required: true, message: '请输入背景', trigger: 'blur' },
    {
      max: 1500,
      message: "最多1500个字符",
      trigger: ["blur"]
    }
  ],
  goal: [
    { required: true, message: '请输入目标', trigger: 'blur' },
    {
      max: 240,
      message: "最多240个字符",
      trigger: ["blur"]
    }
  ],
  keyIssue: [
    { required: true, validator: checkArrayLength, trigger: ['blur', 'change'] }
  ],
  keyInfo: [
    { required: true, message: '请输入关键信息', trigger: 'blur' },
    {
      validator: checkFontLengthLimit,
      trigger: ["blur"]
    }
  ]
})

const departmentReactive = reactive({
  label: '',
  show: false,
  toggleSelect: 0, // 1医疗卫生专业人士 2药店职员 3药企高管
  lastDepartment: convertArrayToObject(visitorList),
  lastLevel: '',
  lastExecutiveLevel: ''
})

const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (
      key !== 'type' &&
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]'
    ) {
      bool = true;
      return;
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '' })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '' })
  }
}

// 背景
const showInputBackdrop = (type: string) => {
  if (type === 'ai') {
    preText.value = formParams.value.backdrop
    dialogType.value = 'backdrop'
    backgroundDialogShow.value = true
  } else {
    inputBackdropShow.value = true
    selectWayShow.value = false
  }
}

// 目标
const showInputGoal = (type: string) => {
  if (type === 'ai') {
    preText.value = formParams.value.goal
    dialogType.value = 'goal'
    backgroundDialogShow.value = true
  } else {
    inputGoalShow.value = true
    selectWayGoalShow.value = false
  }

}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
        scriptGenerateInfoId: generateScriptId.value ? generateScriptId.value : props.id
      };


      params.keyIssue = formParams.value.keyIssue.join(',');
      if (visitorList.slice(0, 4).includes(formParams.value.visitObject)) {
        const array: string[] = []
        formParams.value.department.forEach(element => {
          array.push(element.join('-'))
        });
        params.department = array.join(',')
      }
      await saveOrUpdateScript(params).then((res) => {
        emits('change-type', { flagType: 'list', id: '', listType: 1 })
        proxy.$modal.msgSuccess('保存成功');

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
        moveToError();
      console.log('error submit!!');
      return false;
    }
  });
}

const getAIText = (val: string) => {
  if (dialogType.value === 'backdrop') {
    inputBackdropShow.value = true
    selectWayShow.value = false
    formParams.value.backdrop = val
  } else if (dialogType.value === 'goal') {
    inputGoalShow.value = true
    selectWayGoalShow.value = false
    formParams.value.goal = val
  }
}

const getProductList = async () => {
  const res = await getAllProduct(2);
  productList.value = res.data
}

const handleDepartment = (data) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].subName && data[i].subName.length > 0) {
      data[i].subName = data[i].subName.map((item) => ({ name: item }))
    }
  }
}

const requestDepartmentList = async () => {
  const res: any = await getDepartmentList();
  handleDepartment(res.data)
  departmentList.value = res.data
}

// 选择类型
const changeType = () => {
  getProductList()
  _getChatReportScoringStandard()
  requestDepartmentList()
}
const _getChatReportScoringStandard = async () => {
  const res = await getChatReportScoringStandard({ type: formParams.value.type, pageNum: 1, pageSize: 99 });
  scoringStandardArray.value = res.rows
  if(props.id) {
    formParams.value.scoringStandardId = res.rows[0].id
  }
}
// _getChatReportScoringStandard()


const departmentChange = (val: string) => {
  departmentReactive.lastDepartment[formParams.value.visitObject] = val
  console.log(val, departmentReactive.lastDepartment)
}

const visitorChange = (val: string) => {
  let label = '';
  switch (val) {
    case visitorList[0]:
    case visitorList[1]:
    case visitorList[2]:
    case visitorList[3]:
      label = '科室'
      departmentReactive.toggleSelect = 1
      departmentReactive.show = true
      if (departmentReactive.lastDepartment[val]) {
        formParams.value.department = departmentReactive.lastDepartment[val]
      } else {
        formParams.value.department = []
      }
      break;
    /* case visitorList[4]:
      label = '级别'
      departmentReactive.toggleSelect = 2
      departmentReactive.show = true
      if (departmentReactive.lastDepartment[val]) {
        formParams.value.department = departmentReactive.lastDepartment[val]
      } else {
        formParams.value.department = ''
      }
      break; */
    case visitorList[5]:
      label = '级别'
      departmentReactive.toggleSelect = 3
      departmentReactive.show = true
      if (departmentReactive.lastDepartment[val]) {
        formParams.value.department = departmentReactive.lastDepartment[val]
      } else {
        formParams.value.department = ''
      }
      break;
      case visitorList[4]:
      case visitorList[6]:
      departmentReactive.show = false
      formParams.value.department = ''
      break;
  }
  proxy.$refs['formParamsRef'].clearValidate('department')
  departmentReactive.label = label
}
const filterEmptyElements=(arr:any)=> {
  return arr.filter((item:any)=> {
    // 检查元素是否有实际内容
    return item || item === 0 || item === false; // 根据需要调整条件
  });
}
const getGenerateScriptDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    // background: 'rgba(0, 0, 0, 0.7)',
  })
  changeType()
  const res = await getGenerateResult(generateScriptId.value ? generateScriptId.value : props.id)
  const {name, backdrop, goal, product, location, applicableObject, visitObject, department, keyIssue, keyInfo} = res.data
  formParams.value.name = name
  formParams.value.backdrop = backdrop
  formParams.value.goal = goal
  formParams.value.product = product
  formParams.value.applicableObject = applicableObject
  formParams.value.visitObject = visitObject
  formParams.value.keyInfo = keyInfo
  formParams.value.location = location

  try {

    let keyArray=  JSON.parse(keyIssue)
    // 判断是否是数组
    if (!Array.isArray(keyArray)) {
      keyArray = keyIssue.split(',')||[]
    }

    formParams.value.keyIssue  = filterEmptyElements(keyArray);
    newTags.value = []
    if (formParams.value.keyIssue && formParams.value.keyIssue.length > 0) {
      (formParams.value.keyIssue as string[]).forEach((item: string) => {
        if (!issueTags.includes(item)) {
          newTags.value.push({ inputVisible: false, value: item })
        }

      })
    }
  } catch (error) {
    console.log(error);
  }

  if (formParams.value.location && formParams.value.location.length > 0) {
    if (!locationTags.includes(formParams.value.location)) {
      newLocationTags.value.push({ inputVisible: false, value: formParams.value.location })
    }
  }
  let newArray: any = []
  if (visitorList.slice(0, 4).includes(formParams.value.visitObject)) {
    const array = (department as string).split(',')
    array.forEach((element: any) => {
      if(departmentList!.value?.findIndex(item=>item.name===element.split('-')[0])!==-1){
        newArray.push(element.split('-'))
        // console.log(element.split('-'))
      }
    });
  }
  else {
    newArray = formParams.value!.department
  }
  console.log(newArray)
  formParams.value.department = newArray
  departmentReactive.lastDepartment[formParams.value.visitObject] = formParams.value.department

  visitorChange(formParams.value.visitObject)

  inputBackdropShow.value = true
  inputGoalShow.value = true
  selectWayShow.value = false
  selectWayGoalShow.value = false

  loading.close()
}

const handleGenerate = () => {
  scriptCreateDialogShow.value = true
  // 显示ai生成
}

const onCreateScriptSuccess = (id:string, params: {
  visitObject: string
  visitStage: string
  keyInfo: string
}) => {
  scriptCreateDialogShow.value = false
  // 显示结果弹窗
  scriptCreateStatusDialogShow.value = true
  generateScriptId.value = id
  generateScriptForm.value = params
}

const onScriptStatusScuess = () => {
  scriptCreateStatusDialogShow.value = false
  proxy?.$modal.msgSuccess("生成成功");
  getGenerateScriptDetail()
}

onMounted(() => {
  if (props.id) {
    proxy?.$modal.msgSuccess("生成成功");
    getGenerateScriptDetail()
    generateScriptForm.value = {...props.extra}
    formParams.value.timeLimit = 10
  } else {
    changeType()
  }
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;
  }
}

.select-way {
  display: flex;
  align-items: center;
}

.backdrop-text {
  position: relative;
  width: 100%;

  :deep(.el-textarea__inner) {
    padding-bottom: 40px;
  }
}

.ai {
  background-color: rgb(246, 247, 255);
  color: #4F66FF;
  // padding: 5px;
  position: absolute;
  border-radius: 4px;
  bottom: 10px;
  left: 10px;
  padding: 4px 8px;
  line-height: 20px;
  cursor: pointer;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-start;
  margin: 0 auto;
  height: 100%;
  overflow-y: auto;
  width: 100%;


  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 2px  - 162px);
    padding: 24px 0 0 0 !important;
    overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-radio) {
  height: auto;
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}



.label-tip {
  align-self: center;
  color: #999999;
}

.key-issus {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px #000000 inset
  }

}

.help {
  width: 16px;
  margin-left: 8px;
}

.tips {
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
  margin-top: 10px;
  .help {
    margin-left: 0;
    width: 14px;
    margin-bottom: -2px;
  }

}

:deep(.el-cascader) {
  width: 100%;
}
</style>
