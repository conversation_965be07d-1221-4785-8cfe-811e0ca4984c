<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <template #title>
        编辑关键词
        <div style="font-size: 14px;color: #999999;margin-top: 12px;">可设定当前页需要讲解的关键词，且每个关键词可添加同义词</div>
      </template>
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" v-loading="loading">
          <div v-for="(item, index) in formParams.keywords" :key="index">
            <el-row :gutter="24">
              <el-col :span="18">
                <div class="flex" style="width: 100%;">
                  <img src="@/assets/icons/svg/keyword.svg" style="margin-bottom: 18px;" class="keyword-icon" />
                  <el-form-item :prop="'keywords.' + index + '.name'" :rules="setRules.name" style="width: 100%;">
                    <el-input style="width: 100%;" v-model="item.name" placeholder="请输入关键词"></el-input>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex btn-container">
                  <el-button type="primary" link @click="addNewSynonyms(index)" text>同义词</el-button>
                  <el-button type="primary" v-if="pptScoringType === '2'" link text :disabled="item.remarkFlag" @click="deleteRemark(item)"
                    >备注</el-button
                  >
                  <el-button type="primary" link text @click="removeKeyword(index)">删除</el-button>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24" :key="index" v-if="item.remarkFlag && pptScoringType === '2'">
              <el-col :span="18">
                <el-form-item class="synonyms" :prop="'keywords.' + index + '.remark'" :rules="setRules.remark">
                  <el-input v-model="item.remark" placeholder="AI将综合备注内容进行打分，仅对关键词+语义识别生效"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <div class="flex btn-container">
                  <el-button type="primary" link text @click="deleteRemark(item)">删除</el-button>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24" v-for="(it, i) in item.synonymsList" :key="i">
              <el-col :span="18">
                <el-form-item class="synonyms" :prop="'keywords.' + index + '.synonymsList.' + i + '.synonymsName'" :rules="setRules.synonymsName">
                  <el-input v-model="it.synonymsName" placeholder="请输入同义词"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <div class="flex btn-container">
                  <el-button type="primary" link text @click="removeSynonyms(index, i)">删除</el-button>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="flex" style="margin-top: 12px;">
            <el-button type="primary" @click="addkeywords">添加关键词</el-button>
            <el-button type="primary" plain @click="showQuickImport = true">快速导入</el-button>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" v-loading="btnLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <QuickImport v-model:visible="showQuickImport" @success="initKeywords"></QuickImport>
  </div>
</template>

<script setup name="user" lang="ts">
import { ElMessageBox } from 'element-plus'
const emits = defineEmits(['update:visible', 'success'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const formParamsRef = ref()
import { deepClone } from "@/utils/index"
import QuickImport from './quickImport.vue';
const btnLoading = ref(false)
const loading = ref(false)
const showQuickImport = ref(false)

interface keywordForm {
    name: string
    remark: string
    remarkFlag: boolean
    synonymsList: { synonymsName: string }[]
}

const initForm = {
    keywords: [{
        name: '',
        remark: '',
        remarkFlag: false,
        synonymsList: []
    }] as unknown as keywordForm[]
}

const oldFromInfo = ref({
    keywords: [{
        name: '',
        remark: '',
        remarkFlag: false,
        synonymsList: []
    }] as unknown as keywordForm[]
})



const formParams = ref(deepClone(initForm))
// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新增'
    },
    currentKeywords: {
        type: String,
        default: ''
    },
    scriptId: {
        type: String,
        default: ''
    },
    pptScoringType: {
        type: String,
        default: '1'
    }
})


watch(() => props.visible, val => {
    if (val) {
        if (props.currentKeywords && props.currentKeywords !== '[]') {
            console.log(123)
            const array = JSON.parse(props.currentKeywords)
            formParams.value.keywords = array.map((item: any) => {
                return {
                    name: item.name || '',
                    remark: item.remark || '',
                    remarkFlag: item.remark ? true : false,
                    synonymsList: item.synonyms ? item.synonyms.map((it: any) => {
                        return {
                            synonymsName: it
                        }
                    }) : []
                }
            })
            oldFromInfo.value.keywords = deepClone(formParams.value.keywords)
        } else {
            console.log(initForm)
            formParams.value = deepClone(initForm)
            oldFromInfo.value = deepClone(formParams.value)
        }
    }
})

const deleteRemark = (item: keywordForm) => {
    // 如果显示了 删除 置空
    if (item.remarkFlag) {
        item.remark = ''
    }
    item.remarkFlag = !item.remarkFlag

}

const checkSameNameLimit = (rule: any, value: any, callback: any) => {
    const questionArray = formParams.value.keywords?.filter(item => item.name === value) || []
    // console.log(value)
    if (questionArray.length > 1) {
        callback(new Error('关键词不可重复'))
    } else {
        callback()
    }
}
const checkSameSynonymsLimit = (rule: any, value: any, callback: any) => {
    // console.log(rule.field.includes('synonymsList'))
    if (rule.field.includes('synonymsList')) {
        // 获取当前层数
        const currentLevelArray = rule.field.match(/\d+/g)
        console.log(formParams.value.keywords[currentLevelArray[0]].synonymsList.map((item: any) => item.synonymsName), value)
        const bool = formParams.value.keywords[currentLevelArray[0]].synonymsList.map((item: any) => item.synonymsName).filter(item => item === value).length > 1
        if (bool) {
            callback(new Error('同义词不可重复'))
        } else {
            callback()
        }
    } else {
        callback()
    }
}
const checkWordLimit = (rule: any, value: any, callback: any) => {
    const bool = /[,?!;，。？！；：、]/.test(value);
    if (bool) {
        callback(new Error('输入内容不能包含中英文的常见标点，。？！'))
    } else {
        callback()
    }
}


const setRules = reactive({
    name: [
        {
            required: true,
            message: '请输入关键词',
            trigger: ['blur', ',submit']
        },
        {
            validator: checkSameNameLimit,
            trigger: ['blur']
        },
        {
            validator: checkWordLimit,
            trigger: ['blur']
        }
    ],
    remark: [
        {
            required: true,
            message: '请输入备注',
            trigger: ['blur']
        }
    ],
    synonymsName: [
        {
            required: true,
            message: '请输入同义词',
            trigger: ['blur']
        },
        {
            validator: checkSameSynonymsLimit,
            trigger: ['blur']
        },
        {
            validator: checkWordLimit,
            trigger: ['blur']
        }
    ]

})

const removeKeyword = (index: number) => {
    formParams.value.keywords.splice(index, 1)
}

const removeSynonyms = (index: number, i: number) => {
    formParams.value.keywords[index].synonymsList.splice(i, 1)
}

// 添加同义词
const addNewSynonyms = (index: number) => {
    formParams.value.keywords[index].synonymsList.push({
        synonymsName: ''
    })
}
// 添加关键词
const addkeywords = () => {
    formParams.value.keywords.push({
        name: '',
        remark: '',
        remarkFlag: false,
        synonymsList: []
    })
}



// 弹窗组件显示隐藏
const visibleAdd = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})




const back = () => {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
        if (typeof formParams.value[key] !== 'object') {
            if (
                JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
            ) {
                bool = true;
                return;
            }
        } else {
            if (
                JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
            ) {
                bool = true;
                return;
            }
        }
    });
    if (bool) {
        ElMessageBox.confirm(
            '未保存的内容将丢失',
            '确定要返回吗？',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async () => {
                closeDialog()
            })
            .catch(() => {
            })
    } else {
        closeDialog()
    }
}

// TNF-α的作用通路/TNF-α的作用途径/TNF-α 、单抗作用、单抗作用、单抗作用


const initKeywords = (arrayText: string) => {
    const array = arrayText.split('、')
    array.forEach((element: string) => {
        const array2 = element.split('/') // 关键词+同义词
        const synonymsArray = array2.slice(1).map((item: string) => {
            return {
                synonymsName: item
            }
        });//去除关键词
        console.log(synonymsArray)
        formParams.value.keywords.push({
            name: array2[0],
            remark: '',
            remarkFlag: false,
            synonymsList: synonymsArray
        })
    })

}



const closeDialog = () => {
    loading.value = true
    formParamsRef.value.resetFields()
    formParams.value = { ...initForm }
    loading.value = false
    visibleAdd.value = false
}


// //检查权限方法
// const _scriptUnlockPermissionCheck=async(levelStage:LevelStageBo)=>{
//   formLoading.value=true
//   const unlockConditionScriptIds=levelStage.condition?.map(item=>item.scriptId)||[]

//   formLoading.value=false
//   if(!res.data){

//   }
// }

const success = (array: any) => {
    console.log(array)
    emits('success', array)
    closeDialog()
}


const handleSubmit = () => {
    proxy.$refs['formParamsRef'].validate(async (valid: any) => {
        if (valid) {
            const array = deepClone(formParams.value.keywords)
            console.log(array)
            array.forEach((element: any) => {
                delete element.remarkFlag
                element.synonyms = element.synonymsList.map((item: any) => item.synonymsName)
            });
            success(array)
        } else {
            console.log('error submit!!');
            return false;
        }
    });
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;

    .box-title {
        color: #272C47;
        font-size: 20px;
        margin: 18px 0;
        font-weight: bold;

    }
}

.tips {
    color: #999999;
    margin-left: 16px;
}

:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}


.flex {
    display: flex;
    gap: 0 10px;
    align-items: center;
}

.btn-container {
    margin-top: 6px;
    gap: 0 4px;
}

.synonyms {
    margin-left: 100px;
}

.el-form-item--default {
    margin-bottom: 16px;
}
</style>
