<template>
  <div class="p-2">
    <el-dialog
      ref="formDialogRef"
      :title="title"
      v-model="visibleAdd"
      width="550px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
      :destroy-on-close="true"
    >
      <div class="box-container">
        <el-form
          ref="formParamsRef"
          @submit.native.prevent
          :rules="rules"
          :model="formParams"
          label-width="120px"
          v-loading="loading"
          label-position="left"
        >
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="清除范围" prop="type">
                <div style="display: flex;flex-direction: column;">
                  <el-radio-group v-model="formParams.type">
                    <el-radio :value="0" :label="0">
                      <div class="flex">
                        <span>部分数据</span>
                      </div>
                    </el-radio>
                    <!-- <el-radio :value="1" :label="1">
                      <div class="flex">
                        <span>全部数据</span>
                      </div>
                    </el-radio> -->
                  </el-radio-group>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="选择成员" prop="userIds">
                <el-button
                  @click="handleSelectMember"
                  :icon="formParams.userIds.length > 0 ? 'refresh' : 'plus'"
                  >{{formParams.userIds.length > 0 ? '重选': '选择' }}</el-button
                >
                <span class="tips ml-5" v-if="formParams.userIds.length > 0">已选择{{formParams.userIds.length}}名成员</span>
                <el-input v-model="formParams.userIds" type="hidden" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="时间范围" prop="time" v-if="formParams.type === 0">
                <el-date-picker
                  value-format="YYYY-MM-DD"
                  v-model="formParams.time"
                  type="date"
                  :clearable="false"
                  placeholder="请选择"
                  style="width: 250px;"
                  :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="清除确认" prop="text">
                <el-input v-model="formParams.text" placeholder="请输入文字“确认清除”" style="width: 250px;" />
                <span
                  class="tips"
                  v-if="formParams.type === 0 && formParams.time && formParams.userIds.length > 0"
                  >{{`将清除${Dayjs(formParams.time).format('YYYY年MM月DD日')}及以前的${formParams.userIds.length}名人员的积分数据`}}</span
                >
                <span class="tips" v-if="formParams.type === 1 && formParams.userIds.length > 0"
                  >将清除截止当前时间{{formParams.userIds.length}}名人员的所有积分数据</span
                >
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <div class="tips" style="margin-bottom: 22px; ">设为白名单后将不参与练习数据统计，且不占用席位数。</div> -->
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <SelectMember v-if="visible" v-model:visible="selectShow" :selectedData="formParams.userIds" @success="onSelectMember" />
  </div>
</template>

<script setup name="user" lang="ts">
import { clearPoints } from "@/api/point/index"
import { deepClone } from "@/utils/index";
import { ElMessageBox } from 'element-plus'
import Dayjs from "dayjs";
import SelectMember from './selectMember.vue'
import {userResult } from '@/api/user/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;


const emits = defineEmits(['update:visible', 'success'])
const formParamsRef = ref()
const btnLoading = ref(false)
const loading = ref(false)

const selectShow = ref(false)

let initForm = {
  type: 0,
  time: '',
  text: '',
  userIds: []
}
const formParams = ref(deepClone(initForm))
const rules: ElFormRules = {
  type: [
    { required: true, trigger: "change", message: "请选择清除范围" }
  ],
  time: [
    { required: true, trigger: "change", message: "请选择时间" },
  ],
  text: [
    { required: true, trigger: "blur", message: "请输入文字“确认清除”" },
  ],
  userIds: [
    { required: true, trigger: "change", message: "请选择成员" }
  ]
};

// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '积分清除'
  },
})

const disabledDate = (val: any) => {
  // 获取当前日期
  const today = Dayjs();
  // 获取前一天
  const yesterday = today.subtract(1, 'day');
  return val > yesterday;
};
// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const handleSelectMember = () => {
  selectShow.value = true
}

const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(initForm[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(initForm[key])
      ) {
        bool = true;
        return;
      }
    }

  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}

const onSelectMember = (selectData: userResult[]) => {
  formParams.value.userIds = selectData;
}


const closeDialog = () => {
  loading.value = true
  formParamsRef.value.resetFields()
  formParams.value = deepClone(initForm)
  loading.value = false
  visibleAdd.value = false
}

const handleSubmit = () => {

  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      if (formParams.value.text !== '确认清除') {
        proxy.$modal.msgError('请输入文字“确认清除”');
        formParams.value.text = ''
        return
      }
      const params = {
        date: formParams.value.type === 1 ? '' : formParams.value.time,
        clearAll: formParams.value.type === 1,
        userIds: formParams.value.userIds.map((item) => item.id),
      };

      console.log(params)
      ElMessageBox.confirm(
        '此操作不可撤销，请谨慎操作。',
        '确定执行积分清除吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'none',
        }
      )
        .then(async () => {
          btnLoading.value = true
          await clearPoints(params).then((res) => {
            proxy.$modal.msgSuccess('操作成功');
            closeDialog()
            emits('success')
          }).finally(() => {
            btnLoading.value = false;
          });
        })
        .catch(() => {
        })
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.tips {
  color: #999999;
}

:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}

.dept-container {
  // margin: 30px;
  // box-sizing: border-box;
  width: 70%;
  margin: 0 auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  min-height: 30vh;
  // position: relative;
  display: flex;
  padding: 8px 0;

  .left {
    width: 100%;

  }
}

.divider {
  // height: 100%;
  // width: 1px;
  // background-color: var(--el-border-color);
}
</style>
