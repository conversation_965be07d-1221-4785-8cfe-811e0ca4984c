export interface PointQuery extends PageQuery {
  /**
   * 部门id
   */
  deptIds?: string[];
  /**
   * 结束时间
   */
  endTime?: string;
  id?: number;
  /**
   * 排序的方向desc或者asc
   */
  isAsc?: string;
  orderByColumn: string;
  /**
   * 开始时间
   */
  startTime?: string;
  userName?: string;
}

export interface PointRecordQuery extends PageQuery {
  userId?: string | number;
}

/**
 * UserPointVo，用户积分VO
 */
export interface UserPointVo {
  /**
   * 部门ID
   */
  deptId: number;
  /**
   * 部门名称
   */
  deptName: string;
  /**
   * 筛选积分
   */
  filterPoints: number;
  /**
   * 用户ID
   */
  id: number;
  /**
   * 用户姓名
   */
  name: string;
  /**
   * 总分
   */
  points: number;
  [property: string]: any;
}

/**
 * PointRecordVo，积分记录列表
 */
export interface PointRecordVo {
  id: number;
  /**
   * 任务名称
   */
  missionName: string;
  /**
   * 完成任务所获得的积分
   */
  points: number;
  /**
   * 积分时间
   */
  pointTime: Date;
  /**
   * 完成任务后的总积分
   */
  totalPointsAfter: number;
  /**
   * 完成任务前的总积分
   */
  totalPointsBefore: number;
  /**
   * 用户id
   */
  userId: number;
  /**
   * 用户名称
   */
  userName: string;
  [property: string]: any;
}
