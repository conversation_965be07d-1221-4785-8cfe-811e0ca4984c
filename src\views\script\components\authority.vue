<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px" v-loading="loading">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="脚本可见范围" prop="deptId">
                <el-radio-group v-model="formParams.type" style="display: flex;flex-direction: column;align-items: flex-start;">
                  <el-radio :value="0" :label="0">
                    <div class="flex">
                      <span>全部成员</span>
                      <span class="tips">所有组织架构成员可见</span>
                    </div>
                  </el-radio>
                  <el-radio :value="1" :label="1">
                    <div class="flex">
                      <span>部门可见</span>
                      <span class="tips">指定部门可见</span>
                    </div>
                  </el-radio>
                </el-radio-group>

                <!-- <el-tree-select
                  style="width: 100%;"
                  v-model="formParams.deptId"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  placeholder="请选择部门"
                  check-strictly
                  :default-expand-all="true"
                /> -->
              </el-form-item>
              <div class="dept-container" v-if="formParams.type === 1">
                <div class="left">
                  <!-- <el-input v-model="filterText" placeholder="搜索部门" /> -->
                  <el-tree
                    show-checkbox
                    @check="checkChange"
                    ref="treeRef"
                    node-key="id"
                    :data="deptOptions"
                    :default-expanded-keys="defaultExpandedKeys"
                    :default-checked-keys="formParams.deptIds"
                    :props="defaultProps"
                    :default-expand-all="false"
                  />
                </div>
              </div>

              <el-form-item prop="onlyEnableInEventLevel">
                <template #label>
                  <span class="label-text flex">仅闯关可见</span>
                  <el-tooltip content="开启后，脚本上线后不会显示在日常练习脚本列表中，只会显示在闯关活动中。" placement="top" popper-class="popper">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;  margin-left: 8px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="formParams.onlyEnableInEventLevel" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :disabled="formParams.deptIds.length === 0 && formParams.type === 1">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { getDeptTreeList, changePermission } from "@/api/department/index"
import { getScriptDetail } from '@/api/script'

import { TreeLong } from "@/api/department/types";
import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import type { ElTree } from 'element-plus'
const defaultExpandedKeys=ref<string[]>([])
const emits = defineEmits(['update:visible', 'success'])
const formParamsRef = ref()
const btnLoading = ref(false)
const loading = ref(false)
const deptOptions = ref<TreeLong[]>([])

const initForm = {
    type: 0,
    deptIds: [] as string[],
    onlyEnableInEventLevel: false
}
const defaultProps = {
    children: 'children',
    label: 'label',
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()
const formParams = ref(initForm)
const oldFromInfo = ref({})
import type { FormRules } from 'element-plus'
interface RuleForm {
    type: number
}
interface Tree {
    [key: string]: any
}

const rules = reactive<FormRules<RuleForm>>({
    type: [
        { required: true, message: '请选择部门', trigger: 'change' },
    ],
})

watch(() => props.visible, val => {
    if (val) {
        _getDeptTreeList()
        console.log(props.scriptIds.length)
        if (props.scriptIds.length === 1) {
            _getScriptDetail()
        }
    }
})


const _getScriptDetail = async () => {
    loading.value = true
    const res = await getScriptDetail({ id: props.scriptIds[0] as string });
    loading.value = false
    formParams.value = {
        type: res.data.deptIds ? 1 : 0,
        onlyEnableInEventLevel: res.data.onlyEnableInEventLevel,
        deptIds: res.data.deptIds ? res.data.deptIds.split(',') : [],
    }
    oldFromInfo.value = {
        ...formParams.value
    }

}

const _getDeptTreeList = async () => {
    const res = await getDeptTreeList()
    if (res.data) {
        deptOptions.value = res.data
        defaultExpandedKeys.value=res.data.length>0?[res.data[0].id]:[]
    }
    // deptOptions.value = res.data
}

// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新增'
    },
    scriptIds: {
        type: Array,
        default: () => []
    },

})


// 弹窗组件显示隐藏
const visibleAdd = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})
watch(filterText, (val) => {
    treeRef.value!.filter(val)
})
// 筛选部门
// const filterNode = (value: string, data: Tree) => {
//     if (!value) return true
//     return data.label.includes(value)
// }

const checkChange = () => {
    const array = treeRef.value!.getCheckedNodes()
    formParams.value.deptIds = array.map((item: any) => item.id)
}


const back = () => {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
        if (typeof formParams.value[key] !== 'object') {
            console.log(JSON.stringify(String(formParams.value[key])), JSON.stringify(String(oldFromInfo.value[key])))
            if (
                JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
            ) {
                bool = true;
                return;
            }
        } else {
            if (
                JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
            ) {
                bool = true;
                return;
            }
        }

    });
    if (bool) {
        ElMessageBox.confirm(
            '未保存的内容将丢失',
            '确定要返回吗？',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async () => {
                closeDialog()
            })
            .catch(() => {
            })
    } else {
        closeDialog()
    }
}



const closeDialog = () => {
    loading.value = true
    formParamsRef.value.resetFields()
    formParams.value = initForm
    loading.value = false
    visibleAdd.value = false
}

const handleSubmit = () => {
    proxy.$refs['formParamsRef'].validate(async (valid: any) => {
        if (valid) {
            const params = {
                deptIds: formParams.value.type === 1 ? formParams.value.deptIds.join(',') : null,
                ids: props.scriptIds as string[],
                onlyEnableInEventLevel: formParams.value.onlyEnableInEventLevel
            };
            btnLoading.value = true
            await changePermission(params).then((res) => {
                proxy.$modal.msgSuccess('保存成功');
                closeDialog()
                emits('success')
            }).finally(() => {
                btnLoading.value = false;
            });
        } else {
            console.log('error submit!!');
            return false;
        }
    });
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;

    .box-title {
        color: #272C47;
        font-size: 20px;
        margin: 18px 0;
        font-weight: bold;

    }
}

.tips {
    color: #999999;
    margin-left: 16px;
}

:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}

.flex {
    display: flex;
    align-items: center;
    gap: 0 10px;
    justify-content: flex-start;
}

.label-tip {
    align-self: center;
    color: #999999;
}

.dept-container {
    // margin: 30px;
    // box-sizing: border-box;
    width: 70%;
    margin: 0 auto;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    min-height: 30vh;
    // position: relative;
    display: flex;
    padding: 8px 0;
    overflow: auto;

    .left {
        width: 100%;

    }
}

.divider {
    // height: 100%;
    // width: 1px;
    // background-color: var(--el-border-color);
}

:deep(.el-tree) {
  // .el-tree-node__content {
  //   display: block !important;
  // }
  display: inline-block;
  min-width: 100%;

  .el-tree-node__children {
    overflow-x: visible !important;
  }
}
</style>
