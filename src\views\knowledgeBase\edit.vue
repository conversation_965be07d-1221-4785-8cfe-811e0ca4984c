<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="100px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="name" label="名称">
                <el-input v-model="formParams.name" placeholder="请输入名称" />
                <!-- <div class="tips">
                  知识库名称仅支持中文、英文、数字、下划线（_）、中划线（-）、英文点（.）（1～50字符）
                </div> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="desc" label="知识库描述">
                <el-input
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  :autosize="{ minRows: 4 }"
                  v-model="formParams.description"
                  placeholder="请描述这个知识库的内容和用途"
                />
                <!-- <div class="tips">
                
                  1~100字符，请描述这个知识库的内容和用途
                </div> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { updateKnowledgeBase } from '@/api/knowledgeBase';
import { KnowledgeBaseBo } from '@/api/knowledgeBase/types';
import { ComponentInternalInstance } from 'vue'
import type { FormRules } from 'element-plus'
import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success', 'freezeUser'])

const formParamsRef = ref()
const btnLoading = ref(false)

const initForm = {
  name: '',
  description: '',
  id:''
}
const formParams = ref<KnowledgeBaseBo>(initForm)
  const oldFromInfo = ref<KnowledgeBaseBo>(initForm)
interface RuleForm {
  name: string
  description: string
}

const initData=(val:KnowledgeBaseBo)=>{
  oldFromInfo.value={...val}
  formParams.value={...val}
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]+$/,
      message: "仅支持中文、英文、数字、下划线（_）、中划线（-）、英文点（.）",
      trigger: 'blur'
    },
    {
      min: 1,
      max: 50,
      message: '最多50字符',
      trigger: ["blur"]
    },
  ],
})


// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '编辑知识库'
  },
  id: {
    type: String,
    default: ''
  },
})

// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})




const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
      ) {
        bool = true;
        return;
      }
    }

  });

  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}

const closeDialog = () => {
  formParamsRef.value.resetFields()
  formParams.value = initForm
  visibleAdd.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
      };
      await updateKnowledgeBase(params).then(() => {
        proxy.$modal.msgSuccess('保存成功');
        closeDialog()
        emits('success')

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
defineExpose({
 initData
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}
.tips {
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
  margin-top: 10px;
}
</style>
