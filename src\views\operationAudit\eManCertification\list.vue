<template>
  <div class="p-2">
    <el-card>
      <div class="header">
        <el-tabs v-model="queryParams.status" class="demo-tabs" @tab-click="switchTab">
          <el-tab-pane label="待审核" name="待审核"></el-tab-pane>
          <el-tab-pane label="已完成" name="已完成"></el-tab-pane>
        </el-tabs>

        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
          <el-form-item prop="companyName">
            <el-input
              v-model="queryParams.keyword"
              @clear="resetQuery"
              placeholder="请输入搜索内容"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-form>
      </div>

      <el-table v-loading="loading" :data="professionalCertificationList">
        <el-table-column label="编号" align="center" type="index" width="150" />
        <el-table-column label="E主" align="center" prop="userName" />
        <el-table-column label="E人" align="center" prop="name" />
        <el-table-column label="职业认证状态" align="center" prop="professionalCertificationStatus">
          <template #default="scope">
            <span v-if="scope.row.professionalCertificationStatus===1">未提交审核</span>
            <span v-if="scope.row.professionalCertificationStatus===2"><svg-icon icon-class="help" class="status-wait" /> 待审核</span>
            <span v-else-if="scope.row.professionalCertificationStatus===3"><svg-icon icon-class="check-one" class="status-success" /> 已通过</span>
            <span v-else-if="scope.row.professionalCertificationStatus===4"><svg-icon icon-class="close-one" class="status-error" /> 已驳回</span>
          </template>
        </el-table-column>
        <el-table-column label="E人状态" align="center" prop="status">
          <template #default="scope">
            <span v-if="scope.row.status===2"><svg-icon icon-class="help" class="status-wait" /> 待审核</span>
            <span v-else-if="scope.row.status===3"><svg-icon icon-class="check-one" class="status-success" /> 已通过</span>
            <span v-else-if="scope.row.status===4"><svg-icon icon-class="close-one" class="status-error" /> 已驳回</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="queryParams.status==='待审核'?'提审时间':'审核时间'"
          align="center"
          :prop="queryParams.status==='待审核'?'submitTime':'auditTime'"
        >
          <template #default="scope">
            <span v-formatTime="scope.row[queryParams.status==='待审核'?'submitTime':'auditTime']"></span>
          </template>
        </el-table-column>
        <el-table-column width="150" label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link v-if="queryParams.status==='待审核'" type="primary" @click="handleAudit(scope)" text>审核</el-button>
            <el-button link v-if="queryParams.status==='已完成'" type="primary" @click="handleAudit(scope)" text>详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { eManList} from '@/api/operationAudit/eMan';
import { eManQuery, eManListResult } from '@/api/operationAudit/eMan/types';
import router from '@/router';
import type { TabsPaneContext } from 'element-plus'
// import router from '@/router';
// const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const professionalCertificationList = ref<eManListResult[]>([]);
const loading = ref(true);
const total = ref(0);
const queryFormRef = ref<ElFormInstance>();
const emits = defineEmits(['change-type']);

const data = reactive<PageQueryData<eManQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword:'',
    status:'待审核'
  },
});

const { queryParams } = toRefs(data);
/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await eManList(queryParams.value);
  professionalCertificationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
/** 搜索 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}
/**切换tab状态 */

const switchTab=(tab:TabsPaneContext)=>{
  queryParams.value.status=tab.props.name as string
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}
/**审核详情 */
const handleAudit=(scope:any)=>{
  // router.push({ path: './detail', query: {id:scope.row.id} });
  emits('change-type',{flagType:'isDetail',id:scope.row.id})
}
onMounted(() => {
  getList();
})
</script>
<style scoped lang="scss">
.header{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.el-tabs){
  .el-tabs__nav-wrap::after{
    display: none
  }
}
.status-success {
  color: var(--el-color-success);
}
.status-error {
  color: var(--el-color-error);
}
.status-wait {
  color: var(--el-color-primary);
}
</style>
