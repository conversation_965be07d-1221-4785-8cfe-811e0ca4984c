<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <template #title>
        快速导入
        <div style="font-size: 14px;color: #999999;margin-top: 12px;">请将每个关键词之间用“、”隔开，关键词与同义词之间用“/”隔开</div>
      </template>
      <div class="box-container">
        <el-form ref="formParamsRef" :rules="setRules" :model="formParams" v-loading="loading">
          <el-form-item prop="keywordsText">
            <el-input v-model="formParams.keywordsText" type="textarea" placeholder="请输入" resize="none"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" v-loading="btnLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
const emits = defineEmits(['update:visible', 'success'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const formParamsRef = ref()
import { deepClone } from "@/utils/index"
const btnLoading = ref(false)
const loading = ref(false)



const initForm = {
    keywordsText: ''
}



const formParams = ref(deepClone(initForm))
// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
})


const setRules = reactive({
    keywordsText: [
        {
            required: true,
            message: '请输入',
            trigger: ['blur', ',submit']
        },

    ]
})


// 弹窗组件显示隐藏
const visibleAdd = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})



const closeDialog = () => {
    loading.value = true
    formParamsRef.value.resetFields()
    formParams.value = { ...initForm }
    loading.value = false
    visibleAdd.value = false
}





const handleSubmit = () => {
    proxy.$refs['formParamsRef'].validate(async (valid: any) => {
        if (valid) {
             emits('success', formParams.value.keywordsText)
             closeDialog()
        } else {
            console.log('error submit!!');
            return false;
        }
    });
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;

    .box-title {
        color: #272C47;
        font-size: 20px;
        margin: 18px 0;
        font-weight: bold;

    }
}

.tips {
    color: #999999;
    margin-left: 16px;
}

:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}


.flex {
    display: flex;
    gap: 0 10px;
    align-items: center;
}

.btn-container {
    margin-top: 6px;
    gap: 0 4px;
}

.synonyms {
    margin-left: 100px;
}

.el-form-item--default {
    margin-bottom: 16px;
}

:deep(.el-textarea__inner) {
    height: 300px;
}
</style>
