<template>
  <el-dialog
    title="选择成员"
    v-model="_visible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :show-close="false"
    @open="onOpen"
    @close="onClose"
  >
    <div class="box-container">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="dept-container">
            <el-tree
              class="mt-2"
              node-key="id"
              :data="deptOptions"
              :props="{ label: 'deptName', children: 'children', isLeaf: 'isLeaf' }"
              :expand-on-click-node="false"
              highlight-current
              :load="loadNode"
              lazy
              ref="deptRef"
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  {{ data.deptName }}
                </span>
              </template>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="10">
          <el-table v-loading="loading" :data="userList" ref="tableRef" style="width: 100%;" row-key="id" @selection-change="handleSelectionChange">
            <el-table-column type="selection" :reserve-selection="true" width="55" align="left" />
            <!-- <el-table-column label="编号" align="left" type="index" width="150" /> -->
            <el-table-column label="姓名" align="left" prop="name" min-width="130"></el-table-column>
          </el-table>
          <div class="pagination">
            <pagination
              layout="prev, pager, next"
              :pager-count="4"
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div class="chosen-container">
            <div class="chosen-tip">已选择{{ multipleSelection.length }}人</div>
            <div class="chosen-user-box">
              <div class="chosen-user" v-for="item in multipleSelection" :key="item.id">
                <span>{{ item.name }}</span>
                <!-- <el-icon class="chosen-close" @click="handleDelete(item)" :size="18"><Close /></el-icon> -->
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getUserList } from '@/api/user';
import { getDeptList } from "@/api/department";
import { DeptVo } from "@/api/department/types";
import { userListQuery, userResult } from '@/api/user/types';
import { deepClone } from '@/utils';
const rootResolve = ref()
const rootNode = ref()

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success'])
const deptRef = ref()
const deptOptions = ref<DeptVo[]>([])
const loading = ref(false);
const total = ref(0);
const userList = ref<userResult[]>([]);
const tableRef = ref()
const treeLoading = ref(false)
const multipleSelection = ref<userResult[]>([]);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedData: {
    type: Array,
    default: () => []
  }
})

const data = reactive<PageQueryData<userListQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 12,
    keyword: '',
    deptIds: []
  },
});
const { queryParams } = toRefs(data);
// 弹窗组件显示隐藏
const _visible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const loadNode = async (
  node: any,
  resolve: (data: DeptVo[]) => void
) => {
  // console.log(node)
  let res: any = [];
  if (node.level === 0) {
    // deptRef.value.store.root.doCreateChildren(null);
    rootResolve.value = resolve
    rootNode.value = node
    treeLoading.value = true
    res = await getDeptList({ parentId: '0' });
    deptOptions.value = res.data
    nextTick(() => {
      deptRef.value?.setCurrentKey(res.data[0].id);
      let nodeData = node.childNodes[0];
      nodeData.expanded = true;
      nodeData.loadData();
      treeLoading.value = false
    })
  } else {
    res = await getDeptList({ parentId: node.data.id });
  }

  res.data.forEach((item: DeptVo) => {
    item.isLeaf = !item.hasChildren
  })
  resolve(res.data);
}


const _getDeptTreeList = async () => {
  const res = await getDeptList()
  if (res.data) {
    const data = proxy?.handleTree<DeptVo>(res.data, "id")
    if (data) {
      deptOptions.value = data
      nextTick(() => {
        deptRef.value?.setCurrentKey(deptOptions.value[0].id);
      })
    }
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  let params: any = deepClone(queryParams.value)
  const res = await getUserList(params);
  userList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 搜索 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}


/** 节点单击事件 */
const handleNodeClick = (data: any) => {
  queryParams.value.deptIds = data.id;
  handleQuery()
}



/**表格多选 */
const handleSelectionChange = (val: userResult[]) => {
  multipleSelection.value = val

}

const handleDelete = (user: userResult) => {
  multipleSelection.value = multipleSelection.value.filter((item) => item.id !== user.id)
  nextTick(() => {
    tableRef.value?.toggleRowSelection(user, true)
  })
}


const handleCancel = () => {
  _visible.value = false

}

const handleSubmit = () => {
  emits('success', multipleSelection.value)
  _visible.value = false
}

const onOpen = async () => {
  console.log(props.selectedData)
  multipleSelection.value = props.selectedData
  multipleSelection.value.forEach((item) => {
    tableRef.value?.toggleRowSelection(item, true)
  })
  // await _getDeptTreeList()
  await getList()
}
const onClose = () => {
  queryParams.value.deptIds = []
  multipleSelection.value = []
  tableRef.value?.clearSelection()
}
</script>

<style lang="scss" scoped>
.box-container {
  margin-top: -20px;
}

.dept-container {
  height: 600px;
  overflow-y: auto;
}

.chosen-tip {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.chosen-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 15px;
}

.chosen-close {
  cursor: pointer;
  margin-right: 10px;
}

.chosen-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.chosen-user-box {
  flex: 1;
  overflow-y: auto;
}
:deep(.el-tree) {
  // .el-tree-node__content {
  //   display: block !important;
  // }
  display: inline-block;
  min-width: 100%;

  .el-tree-node__children {
    overflow-x: visible !important;
  }
}
</style>
