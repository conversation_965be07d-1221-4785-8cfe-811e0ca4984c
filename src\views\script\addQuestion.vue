<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            创建脚本</span
          >
          <div class="right-button">
            <el-button plain type="info" @click="back()">取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" ref="scrollDiv">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="130px" label-position="left">
          <!-- <el-row :gutter="24">
            <el-col :span="9">
              <el-form-item label="脚本类型" prop="type">
                <el-select @change="changeType" v-model="formParams.type" placeholder="请选择">
                  <el-option v-for="item in scriptTypeArray" :key="item.key" :disabled="item.disabled" :label="item.name" :value="item.key" />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="tips">{{ scriptTypeArray.find((item) => item.key === formParams.type)?.description }}</div>
          </el-row> -->

          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="脚本名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="产品" prop="product" v-if="formParams.type">
                <el-select clearable v-model="formParams.product" placeholder="请选择或者输入" filterable allow-create>
                  <el-option v-for="item in productList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="评分标准" prop="scoringStandardId" v-if="formParams.type">
                <el-select v-model="formParams.scoringStandardId" placeholder="请选择">
                  <el-option v-for="item in scoringStandardArray" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="applicableObject">
                <template #label>
                  <span class="label-text">适用对象</span>
                  <!-- <el-tooltip content="此脚本是给谁用的，比如：医药代表、药店职员等。" placement="top">
                    <el-icon class="label-tip">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip> -->
                </template>
                <el-select clearable v-model="formParams.applicableObject" placeholder="请选择">
                  <el-option v-for="item in targetList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="9">
              <el-form-item label="提问方式" prop="randomFlag">
                <el-select v-model="formParams.randomFlag" placeholder="请选择" @change="initRandomNum">
                  <el-option v-for="item in questionType" :key="item.flag" :label="item.name" :value="item.flag" />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="flex">
              <div class="random-num" v-if="formParams.randomFlag === 2">
                <el-form-item label="数量：" prop="randomNum" label-width="70px">
                  <el-input style="width: 100px;" v-model="formParams.randomNum" placeholder="请输入"
                /></el-form-item>
              </div>
              <div class="random-num" v-else-if="formParams.randomFlag === 3">
                <el-form-item label="比例：" prop="randomNum" label-width="70px">
                  <el-input style="width: 100px;" v-model="formParams.randomNum" placeholder="请输入" />
                  <span style="margin-left: 8px">%</span></el-form-item
                >
              </div>
              <div class="tips flex" style="margin-bottom:24px;margin-left: 16px;">
                <span v-if="formParams.randomFlag === 2 && formParams.randomNum"
                  >{{questionType.find((item) =>
                  item.flag
                  === formParams.randomFlag)?.description}}
                  {{ formParams.randomNum }}题</span
                >
                <span v-if="formParams.randomFlag === 3 && formParams.randomNum">
                  {{questionType.find((item) =>
                    item.flag ===
                    formParams.randomFlag)
                    ?.description}}
                  {{ randomNum }}题</span
                >
              </div>
            </div>
          </el-row>

          <el-row :gutter="24" v-if="formParams.randomFlag === 2 || formParams.randomFlag === 3">
            <el-col :span="9">
              <!-- <div style="width: 100%;display: flex;align-items: center;"> -->
              <el-form-item label="随机概率" prop="randomType" v-if="formParams.type">
                <el-select v-model="formParams.randomType" placeholder="请选择">
                  <el-option v-for="item in randomRateArray" :key="item.key" :label="item.name" :value="item.key" />
                </el-select>
              </el-form-item>

              <!-- </div> -->
            </el-col>
            <div class="tips" style="margin-bottom: 22px; margin-left: 10px;">
              {{randomRateArray.find(item => item.key === formParams.randomType)?.description}}
            </div>
          </el-row>
          <el-row :gutter="24" style="align-items: flex-start;">
            <el-col :span="24">
              <el-form-item label="时长限制方式" prop="timeLimitType">
                <div class="flex" style="flex-direction: column;gap:12px 0;align-items: flex-start;">
                  <div class="flex" style="justify-content: flex-start;">
                    <div class="radio-box flex" @click=" initLimitTimeType(1)" :class="{ checked: formParams.timeLimitType === 1 }">限制整体时长</div>
                    <div class="radio-box flex" @click="initLimitTimeType(2)" :class="{ checked: formParams.timeLimitType === 2 }">限制单题时长</div>
                  </div>
                  <div class="limit-type" v-if="formParams.timeLimitType === 2">
                    <el-radio-group v-model="formParams.limitSingleQaType" @change="updateLimitSingleQaType">
                      <div class="flex">
                        <el-radio :label="1">
                          <div class="flex">
                            自动设置
                            <el-tooltip content="将根据字数自动计算时限，最少20秒。" placement="top">
                              <el-icon class="label-tip">
                                <img src="@/assets/icons/svg/help1.svg" class="help" />
                              </el-icon>
                            </el-tooltip>
                          </div>
                        </el-radio>
                        <el-radio :label="2">
                          <div class="flex">
                            统一设置
                            <el-tooltip content="所有题目的时限一致。" placement="top">
                              <el-icon class="label-tip">
                                <img src="@/assets/icons/svg/help1.svg" class="help" />
                              </el-icon>
                            </el-tooltip>
                          </div>
                        </el-radio>
                        <el-radio :label="3">
                          <div class="flex">
                            手动设置
                            <el-tooltip content="手动设置各题时限。" placement="top">
                              <el-icon class="label-tip">
                                <img src="@/assets/icons/svg/help1.svg" class="help" />
                              </el-icon>
                            </el-tooltip>
                          </div>
                        </el-radio>
                      </div>
                    </el-radio-group>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="formParams.timeLimitType === 1" style="align-items: flex-start;">
            <el-col :span="9">
              <el-form-item label="整体时限" prop="timeLimit"><el-input v-model="formParams.timeLimit" placeholder="请输入" /> </el-form-item>
            </el-col>
            <div class="tips" style="margin-bottom: 22px;color: #333333; margin-left: 10px;">分钟</div>
          </el-row>
          <el-row :gutter="24" v-if="formParams.timeLimitType === 2 && formParams.limitSingleQaType === 2" style="align-items: flex-start;">
            <el-col :span="9">
              <el-form-item label="单题时限" prop="singleQaTimeLimit"
                ><el-input v-model="formParams.singleQaTimeLimit" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <div class="tips" style="margin-bottom: 22px;color: #333333; margin-left: 10px;">秒</div>
          </el-row>
          <el-divider />
          <div class="qa">
            <el-row :gutter="24" class="flex qa-item" v-for="(item, index) in formParams.qaList" :key="index">
              <el-col :span="23">
                <el-form-item :label="`问题${index + 1}`" :prop="'qaList.' + index + '.question'" :rules="setRules.question">
                  <el-input placeholder="请输入问题" @blur="setTimeLimit(index)" v-model="item.question" />
                </el-form-item>
                <el-form-item :prop="'qaList.' + index + '.answer'" :rules="setRules.answer">
                  <el-input
                    placeholder="请输入参考答案"
                    @blur.capture="setTimeLimit(index)"
                    v-model="item.answer"
                    :autosize="{ minRows: 4 }"
                    class="mt8"
                    type="textarea"
                  />
                </el-form-item>
                <el-form-item
                  :prop="'qaList.' + index + '.timeLimit'"
                  :rules="setRules.qaTimeLimit"
                  v-if="formParams.timeLimitType === 2 && formParams.limitSingleQaType === 3"
                >
                  <div class="flex"><el-input placeholder="时限" style="width: 100px;" v-model="item.timeLimit" />秒</div>
                </el-form-item>
                <el-form-item
                  :prop="'qaList.' + index + '.timeLimit'"
                  :rules="setRules.qaTimeLimit"
                  v-else-if="formParams.timeLimitType === 2 && formParams.limitSingleQaType === 2"
                >
                  <div class="flex" style="color: #cccccc;">时限：{{ item.timeLimit }}秒</div>
                </el-form-item>
                <el-form-item
                  :prop="'qaList.' + index + '.timeLimit'"
                  :rules="setRules.qaTimeLimit"
                  v-else-if="formParams.timeLimitType === 2 && formParams.limitSingleQaType === 1"
                >
                  <div class="flex" style="color: #cccccc;">时限：{{ item.timeLimit }}秒</div>
                </el-form-item>
              </el-col>

              <el-icon color="#dddddd" v-if="index === 0 && formParams.qaList.length === 1">
                <Delete />
              </el-icon>
              <div class="delete" v-else>
                <el-popconfirm
                  width="220"
                  v-if="!(item.answer === '' && item.question === '')"
                  placement="top"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  @confirm="removeQuesetion(index)"
                  title="确定删除吗?"
                >
                  <template #reference>
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </template>
                </el-popconfirm>
                <el-icon v-else @click="removeQuesetion(index)">
                  <Delete />
                </el-icon>
              </div>
            </el-row>
            <div class="button" id="bottom">
              <el-button type="primary" @click="addQuestion()">添加题目</el-button>
              <el-button type="primary" @click="showImportDialog = true">批量导入</el-button>
              <el-button type="primary" @click="showAiDialog = true">AI生成</el-button>
            </div>
          </div>
        </el-form>
      </div>

      <FileImport v-model:visible="showImportDialog" title="批量导入" @sendQA="getQA"></FileImport>
      <AiImport v-model:visible="showAiDialog" @setAiGenerateData="getAiGenerateData"></AiImport>
      <QuestionLoading v-model:visible="showLoadingDialog" :file-result="fileResult" @sendAiQA="getQA"> </QuestionLoading>
    </el-card>
  </div>
</template>

<script setup name="AddQuestion" lang="ts">
import { reactive, ref } from 'vue'
import { getAllProduct, getChatReportScoringStandard, saveOrUpdateScript } from '@/api/script'
import { ChatReportScoringStandardVo, scriptFormVo, ScriptQABo } from '@/api/script/types'
import { ElMessageBox } from 'element-plus'
import { targetList } from './components/data';
import AiImport from "./components/aiImport.vue";
import FileImport from "./components/fileImport.vue";
import QuestionLoading from "./components/questionLoading.vue";


// import { FormValidators } from '@/utils/validate.js'
const scrollDiv = ref()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
const btnLoading = ref(false)
const scoringStandardArray = ref<ChatReportScoringStandardVo[]>([])
const productList = ref<string[]>([])
const showAiDialog = ref(false)
const showImportDialog = ref(false)
const showLoadingDialog = ref(false)
const fileResult = ref<any>()
const minSingleQaTimeLimit = 20
//检测比例数量
const randomNum = computed(() => {
  let num = 0
  if (formParams.value.randomFlag as number === 3 && formParams.value.randomNum) {
    num = Math.ceil(formParams.value.randomNum * 0.01 * formParams.value.qaList.length)
  }

  return num
});
import type { FormRules } from 'element-plus'
// import { set } from 'nprogress';

const randomRateArray = [{
  name: '概率一致',
  key: 1,
  description: '所有题目出现的概率都一样'
},
{
  name: '低分优先',
  key: 2,
  description: '历史得分低的题目出现概率高'
}]

const questionType = [{
  name: '按顺序提问',
  flag: 0,
  description: '按顺序提问所有问题'
},
{
  name: '随机提问顺序',
  flag: 1,
  description: '随机提问所有题目'
},
{
  name: '随机提问指定数量题目',
  flag: 2,
  description: '随机提问'
},
{
  name: '随机提问指定比例题目',
  flag: 3,
  description: '随机提问'
}]

const initLimitTimeType = (type: number) => {
  formParams.value.timeLimitType = type
  formParams.value.limitSingleQaType = 1
  formParams.value.qaList.forEach((element, index) => {
    if (type === 2) {
      element.timeLimit = computedTimeLimit(element.question as string, element.answer as string)
    } else {
      element.timeLimit = null
    }
  })
}


const formParams = ref<scriptFormVo>({
  type: 2,
  timeLimitType: 1,
  limitSingleQaType: 1,
  name: '',
  randomNum: null,
  timeLimit: null,
  singleQaTimeLimit: minSingleQaTimeLimit,
  scoringStandardId: null,
  product: '',
  randomType: 1,
  randomFlag: null,
  applicableObject: null,
  qaList: [
    {
      question: '',
      answer: '',
      timeLimit: minSingleQaTimeLimit
    }
  ]
})
interface RuleForm {
  qaTimeLimit: number,//单题时限
  singleQaTimeLimit: number,//统一设置
  name: string
  timeLimit: string,//整体时限
  type: number,
  randomFlag: boolean | null,
  randomType: number | null;
  randomNum: number | null,
  limitSingleQaType: number,
  scoringStandardId: string | number,
  product: string,
  timeLimitType: number,
  applicableObject: string | null,
}

//监听统一设置 单题时长
watch(() => formParams.value.singleQaTimeLimit, () => {
  if (formParams.value.singleQaTimeLimit) {
    formParams.value.qaList.forEach((element, index) => {
      element.timeLimit = formParams.value.singleQaTimeLimit
    })
  }
})


const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 1500) {
    callback(new Error('最多1500个字符'))
  } else {
    callback()
  }
}



const checkSameQuestionLimit = (rule: any, value: any, callback: any) => {
  const questionArray = formParams.value.qaList?.filter(item => item.question === value) || []
  // console.log(value)
  if (questionArray.length > 1) {
    callback(new Error('题目重复'))
  } else {
    callback()
  }
}

const checkSameAnswerLimit = (rule: any, value: any, callback: any) => {
  const answerArray = formParams.value.qaList?.filter(item => item.answer === value) || []
  // console.log(value)
  if (answerArray.length > 1) {
    callback(new Error('答案重复'))
  } else {
    callback()
  }
}
const checkSingleQaTimeLimit = (rule: any, value: any, callback: any) => {
  if (value > 600) {
    callback(new Error('最多600秒'))
  } else if (value < minSingleQaTimeLimit) {
    callback(new Error('最少' + minSingleQaTimeLimit + '秒'))
  } else {
    callback()
  }
}
const setRules = reactive({
  question: [
    {
      required: true,
      message: '请输入问题',
      trigger: ['blur']
    },
    {
      min: 1,
      max: 130,
      message: '问题字数不能超过130个字符',
      trigger: ['blur']
    },
    {
      validator: checkSameQuestionLimit,
      trigger: ['blur']
    },

  ],
  answer: [
    {
      required: true,
      message: '请输入参考答案',
      trigger: ['blur']
    },
    {
      validator: checkSameAnswerLimit,
      trigger: ['blur']
    },
    {
      validator: checkFontLengthLimit,
      trigger: ['blur']
    },
  ],
  qaTimeLimit: [
    { required: true, message: '请输入时限', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkSingleQaTimeLimit,
      trigger: 'blur'
    }
  ],
})
const checkTimeLimit = (rule: any, value: any, callback: any) => {
  if (value > 60) {
    callback(new Error('最多60分钟'))
  } else {
    callback()
  }
}


const setTimeLimit = (index: number) => {
  if(formParams.value.timeLimitType === 2&&formParams.value.limitSingleQaType === 1){
      setTimeout(() => {
    if (formParams.value.qaList[index].question && formParams.value.qaList[index].answer) {
      formParams.value.qaList[index].timeLimit = computedTimeLimit(formParams.value.qaList[index].question, formParams.value.qaList[index].answer)
    }
  }, 100)
  }
}


const checkRateRandomNum = (rule: any, value: any, callback: any) => {
  if (Number(formParams.value.randomFlag) === 3) {
    if (Number(value) > 100) {
      callback(new Error('最大比例100%'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}


const checkProductLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('最多64个字符'))
  } else {
    callback()
  }
}





const rules = reactive<FormRules<RuleForm>>({
  singleQaTimeLimit: [
    { required: true, message: '请输入单题时限', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkSingleQaTimeLimit,
      trigger: 'blur'
    }
  ],
  timeLimitType: [
    { required: true, message: '请选择时长限制方式', trigger: ["blur", "change"] },
  ],
  name: [
    { required: true, message: '请输入脚本名称', trigger: 'blur' },


    {
      validator: checkProductLimit,
      trigger: ["blur"]
    }
  ],
  applicableObject: [
    { required: true, message: '请选择适用对象', trigger: ["blur", "change"] },
  ],
  timeLimit: [
    { required: true, message: '请输入时限', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkTimeLimit,
      trigger: 'blur'
    }
  ],

  randomFlag: [
    { required: true, message: '请选择提问方式', trigger: ["blur", "change"] },
  ],
  randomType: [
    { required: true, message: '请选择随机概率', trigger: ["blur", "change"] },
  ],
  randomNum: [
    { required: true, message: '请输入', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkRateRandomNum,
      trigger: 'blur'
    },

  ],
  scoringStandardId: [
    { required: true, message: '请选择评分标准', trigger: ["blur", "change"] },
  ],
  product: [
    { required: true, message: '请输入产品', trigger: ["blur", "change"] },
    {
      validator: checkProductLimit,
      trigger: ["blur", "change"]
    }
  ],
})

//获取文件ID
const getAiGenerateData = (data: any) => {
  if (JSON.stringify(data) !== '{}') {
    //显示题目生成中
    fileResult.value = data
    showLoadingDialog.value = true
  }
}

const updateLimitSingleQaType = () => {
  formParams.value.qaList.forEach((element, index) => {
    if (formParams.value.limitSingleQaType === 1) {
      element.timeLimit = computedTimeLimit(element.question as string, element.answer as string)
    } else if (formParams.value.limitSingleQaType === 2) {
      element.timeLimit = formParams.value.singleQaTimeLimit
    }
  })

}

// 计算自动的时间
const computedTimeLimit = (question: string, answer: string) => {
  if (question && answer) {
    const cleanedText = (question + answer).replace(/[.,\/#!$%\^&\*;:{}=\-_`~()，。、；：'"「」【】《》？！…]/g, '')
    // const text = cleanedText.match(/[\w\u4e00-\u9fa5]+/g) || [];
    // console.log(text)
    // console.log(123)
    let length = 0;
    let inEnglishWord = false;
    for (let i = 0; i < cleanedText.length; i++) {
      const char = cleanedText[i];
      // 统计长度：英文单词算1，中文单字算1

      // 检查是否是英文字符（字母或数字）
      if (/[a-zA-Z0-9]/.test(char)) {
        if (!inEnglishWord) {
          length++;
          inEnglishWord = true;
        }
      } else {
        // 中文字符或其他非标点符号字符
        length++;
        inEnglishWord = false;
      }
    }
    // console.log(length)
    return Math.ceil(length / 2.5) < minSingleQaTimeLimit ? minSingleQaTimeLimit : Math.ceil((length / 2.5) / 10) * 10
  }
}


const getQA = (val: ScriptQABo[]) => {
  // 处理时间
  val.forEach((element, index) => {
    if (formParams.value.timeLimitType === 2 && formParams.value.limitSingleQaType === 2) {
      element.timeLimit = formParams.value.singleQaTimeLimit
    } else if (formParams.value.timeLimitType === 2 && formParams.value.limitSingleQaType === 1) {
      // 自动计算
      element.timeLimit = computedTimeLimit(element.question as string, element.answer as string)
    } else if (formParams.value.timeLimitType === 2 && formParams.value.limitSingleQaType === 3) {
      element.timeLimit = null
    }
  })

  const qaList = formParams.value!.qaList.concat(val)
  //去除空数组
  const arr = qaList.filter(function (item) {
    return item.question !== "" || item.answer !== "";
  });

  formParams.value.qaList = [...arr]
  nextTick(() => {
    // const element = document.querySelector('#bottom');
    // element?.scrollIntoView({ behavior: "smooth" });
    setTimeout(() => {
      let scrollElem = scrollDiv.value;
      scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
      formParams.value.qaList.forEach((element, index) => {
        proxy.$refs['formParamsRef'].validateField('qaList.' + index + '.question')
        proxy.$refs['formParamsRef'].validateField('qaList.' + index + '.answer')
        proxy.$refs['formParamsRef'].validateField('qaList.' + index + '.timeLimit')

      });
    }, 100);
  });
}





const addQuestion = () => {
  formParams.value.qaList.push({ question: '', answer: '', timeLimit: formParams.value.timeLimitType === 2 && formParams.value.limitSingleQaType === 2 ? formParams.value.singleQaTimeLimit : 0 })
  // 滚动到底部
  nextTick(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
  });
}


const removeQuesetion = (index: any) => {
  formParams.value.qaList.splice(index, 1)
  formParams.value.qaList.forEach((element, index) => {
    proxy.$refs['formParamsRef'].validateField('qaList.' + index + '.question')
    proxy.$refs['formParamsRef'].validateField('qaList.' + index + '.answer')
    proxy.$refs['formParamsRef'].validateField('qaList.' + index + '.timeLimit')

  });
}

const initRandomNum = () => {
  formParams.value.randomNum = null
}


const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (
      key !== 'type' &&
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]'
    ) {
      if (key === 'qaList') {
        formParams.value[key].forEach(element => {
          console.log()
          if (JSON.stringify(element.question) !== JSON.stringify('') && JSON.stringify(element.question) !== JSON.stringify('')) {
            bool = true;
            return;
          }
        });
      } else {
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '' })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '' })
  }
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      // 单题时限

      if (formParams.value.randomFlag === 2) {
        if (formParams.value.randomNum as number > formParams.value.qaList.length) {
          ElMessage.error('提问方式设置的题目数不能大于总题目数');
          return;
        }
      }
      const params = {
        ...formParams.value,
      };
      if (params.timeLimitType === 2) {
        params.singleQaTimeLimit = minSingleQaTimeLimit
        params.timeLimit = 0
      }
      params.qaList?.forEach((item, index) => {
        item.orderNum = index
      })
      btnLoading.value = true;
      await saveOrUpdateScript(params).then((res) => {
        emits('change-type', { flagType: 'list', id: '', listType: 1 })
        proxy.$modal.msgSuccess('保存成功');

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}


const getProductList = async () => {
  const res = await getAllProduct(2);
  productList.value = res.data
}

// 选择类型
const changeType = () => {
  getProductList()
  _getChatReportScoringStandard()
}
const _getChatReportScoringStandard = async () => {
  const res = await getChatReportScoringStandard({ type: formParams.value.type, pageNum: 1, pageSize: 99 });
  scoringStandardArray.value = res.rows
}
// _getChatReportScoringStandard()
changeType()
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.qa-item {
  margin-bottom: 20px;
}

.radio-box {
  padding: 3px 24px;
  background-color: #f1f1f1;
  color: #333333;
  cursor: pointer;
  border-radius: 2px;
}

.checked {
  background-color: var(--el-color-primary);
  color: #ffffff;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;
  }
}

.box-container {
  // display: flex;
  // flex-direction: column;
  // gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  box-sizing: border-box;
  height: calc(100vh - 84px - 32px - 100px);
  overflow-y: auto;
  width: 100%;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  .el-card__body {

    // overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-radio) {
  height: auto;
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qa {
  .el-form-item--default {
    margin-bottom: 8px;
  }
}

:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 84px - 32px - 100px);
  }
}

.delete {
  cursor: pointer;
}

.tips {
  color: #999999;
  margin-top: 4px;
  font-size: 14px;
}

.random-num {
  color: #333;
}

.help {
  width: 16px;
}
</style>
