<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex" v-if="editFlag">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            {{ title }}</span
          >
          <span class="card-title flex" v-else>
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            {{ title }}</span
          >
          <div class="right-button" v-if="editFlag">
            <el-button plain type="info" @click="props.isEdit ? back() : checkSave()"> 取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
          <div class="right-button" v-else>
            <el-button type="primary" plain @click="emits('change-type', { flagType: 'list', id: '', listType: listType })">返回</el-button>
            <el-button type="primary" :disabled="form.system" @click="editFlag = true">编辑</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" v-if="editFlag">
        <el-form ref="formParamsRef" :model="form" :rules="rules" label-position="left">
          <div class="form-title">评分标准名称</div>
          <el-row style="margin-top: 15px;">
            <el-col :span="16">
              <el-form-item prop="name">
                <el-input v-model="form.name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="form-title">评分标准维度设计</div>
          <div class="tips">请确保所有评分主维度总分和为100分，评分主维度数量限制在3-6个，每个主维度下子维度数量限制在2-5个。</div>
          <div class="dimension-list">
            <div class="dimension-item dimension-head">
              <div class="dimension-type">评分维度</div>
              <div class="dimension-score">分数</div>
              <div class="dimension-standard">评分细则</div>
            </div>
            <template v-for="(item, index) in form.dimensions" :key="index">
              <!-- 主维度 -->
              <div class="dimension-item">
                <div class="dimension-type dimension-type-main">
                  <img src="@/assets/images/icon_star.svg" class="star" />
                  <el-form-item :prop="`dimensions.${index}.name`" :rules="setRules.name">
                    <el-input v-model="item.name" placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-score">
                  <el-form-item :prop="`dimensions.${index}.fullScore`" :rules="setRules.fullScore">
                    <el-input v-model="item.fullScore" placeholder="0"><template #suffix>分</template></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-standard">
                  <el-form-item :prop="`dimensions.${index}.description`" :rules="setRules.description">
                    <el-input type="textarea" resize="none" autosize v-model="item.description" placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-actions">
                  <el-button link type="primary" @click.stop="addNewSubDimension(index)" text>新增子维度</el-button>
                  <el-popconfirm
                    width="220"
                    @confirm.stop="deleteMainDimension(index)"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定删除吗?"
                    v-if="item.description||item.fullScore||item.name||item.children.length>0"
                  >
                    <template #reference>
                      <el-button link type="primary" text>删除</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button v-else link type="primary" @click.stop="deleteMainDimension(index)" text>删除</el-button>
                </div>
              </div>
              <!-- 子维度 -->
              <div class="dimension-item" v-for="(child, indexChild) in item.children" :key="indexChild">
                <div class="dimension-type">
                  <el-form-item :prop="`dimensions.${index}.children.${indexChild}.name`" :rules="setRules.name">
                    <el-input style="width: 160px;" v-model="child.name" placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-score">
                  <el-form-item :prop="`dimensions.${index}.children.${indexChild}.fullScore`" :rules="setRules.fullScore">
                    <el-input style="width: 80px;" v-model="child.fullScore" placeholder="0"><template #suffix>分</template></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-standard">
                  <el-form-item :prop="`dimensions.${index}.children.${indexChild}.description`" :rules="setRules.description">
                    <el-input v-model="child.description" resize="none" type="textarea" autosize placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-actions">
                  <el-popconfirm
                    v-if="child.description||child.fullScore||child.name"
                    width="220"
                    @confirm.stop="deleteSubDimension(index, indexChild)"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定删除吗?"
                  >
                    <template #reference>
                      <el-button link type="primary" text>删除</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button v-else link type="primary" @click.stop="deleteSubDimension(index, indexChild)" text>删除</el-button>
                </div>
              </div>
              <el-divider />
            </template>

            <el-button type="primary" @click="addDimension">新增主维度</el-button>
          </div>
        </el-form>
      </div>
      <div class="box-container" v-else>
        <div class="dimension-list">
          <div class="dimension-item dimension-head">
            <div class="dimension-type">评分维度</div>
            <div class="dimension-score">分数</div>
            <div class="dimension-standard">评分细则</div>
          </div>
          <template v-for="(item, index) in form.dimensions" :key="index">
            <!-- 主维度 -->
            <div class="dimension-item">
              <div class="dimension-type dimension-type-main">
                <img src="@/assets/images/icon_star.svg" class="star" />{{
                  item.name }}
              </div>
              <div class="dimension-score">{{ item.fullScore }}</div>
              <div class="dimension-standard">{{ item.description }}</div>
            </div>
            <!-- 子维度 -->
            <div class="dimension-item" v-for="(child, index) in item.children" :key="index">
              <div class="dimension-type">{{ child.name }}</div>
              <div class="dimension-score">{{ child.fullScore }}</div>
              <div class="dimension-standard">{{ child.description }}</div>
            </div>
            <el-divider />
          </template>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="ScoreStandardDetail" lang="ts">
import { queryDetail, handleSave } from '@/api/scoreStandard';
import { ElMessageBox, ElLoading } from 'element-plus'

import type { FormRules } from 'element-plus'
// import { it } from 'element-plus/es/locale';

const btnLoading = ref(false)
const formParamsRef = ref()
// const visibleFlag = ref(false)
const form = ref({
  name: '',
  type: undefined,
  dimensions: [],
  children: [],
  system:true
})

const oldDimension = ref()
const oldName = ref()

const emits = defineEmits(['change-type'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  scriptType: {
    type: Number
  },
  listType: {
    type: Number
  }
})
const editFlag = ref(props.isEdit)


const title = computed(() => {
  return editFlag.value ? '编辑评分标准' : form.value.name
})


const rules = reactive<FormRules<any>>({
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    },
    {
      max: 16,
      message: '最多16个字符',
      trigger: ['blur']
    },
  ],
})
const checkScoreLimit = (rule: any, value: number, callback: any) => {
  // console.log(rule, value)
  if (!rule.field.includes('children')) {
    // 获取当前层数
    const currentLevel = rule.field.match(/\d+/g).join(',')
    console.log(currentLevel)
    //子层的总分
    const subTotal = form.value.dimensions[currentLevel].children.reduce((total: number, item: any) => {
      return total + Number(item.fullScore)
    }, 0)
    console.log(value, subTotal)
    if (Number(value) !== Number(subTotal)) {
      callback(new Error('请确保所有子维度总分和等于主维度'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

const setRules = reactive({
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    },
    {
      max: 16,
      message: '最多16个字符',
      trigger: ['blur']
    },
  ],

  fullScore: [
    {
      required: true,
      message: '请输入分数',
      trigger: ['blur']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur']
    },
    {
      validator: checkScoreLimit,
      trigger: ['submit']
    },
  ],
  description: [
    { required: true, message: '请输入评分标准的描述', trigger: 'blur' },
    { max: 500, message: '最多500个字符', trigger: 'blur' }
  ]
})

const back = () => {
  // console.log(oldDimension.value, form.value.dimensions)
  let bool = JSON.stringify(JSON.parse(oldDimension.value).dimensions) !== JSON.stringify(form.value.dimensions)
  let bool1 = oldName.value !== form.value.name
  if (bool || bool1) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}

const checkSave = () => {
  if (editFlag.value) {
    let bool = JSON.stringify(JSON.parse(oldDimension.value).dimensions) !== JSON.stringify(form.value.dimensions)
    let bool1 = oldName.value !== form.value.name
    if (bool || bool1) {
      ElMessageBox.confirm(
        '未保存的内容将丢失',
        '确定要返回吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          editFlag.value = false
          _getDetail()
        })
        .catch(() => {
        })
    } else {
      editFlag.value = false
    }
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}

const addDimension = () => {
  if (form.value.dimensions.length >= 6) {
    ElMessage({
      message: '评分主维度数量限制在3-6个',
      type: 'warning'
    })
    return
  }
  form.value.dimensions.push({
    name: '',
    fullScore: undefined,
    description: '',
    children: []
  })
}

// 新增子维度
const addNewSubDimension = (index: number) => {
  if (form.value.dimensions[index].children.length >= 5) {
    ElMessage({
      message: '子维度的数量为2-5个',
      type: 'warning'
    })
    return
  }
  // proxy.$refs['formParamsRef'].resetFields('dimensions.' + index + '.fullScore')

  form.value.dimensions[index].children.push({
    name: '',
    fullScore: undefined,
    description: ''
  })

  // console.log(form.value.dimensions[index].children.length)
}

const deleteMainDimension = (index: number) => {
  if (form.value.dimensions.length <= 3) {
    ElMessage({
      message: '评分主维度数量限制在3-6个',
      type: 'warning'
    })
    return
  }
  form.value.dimensions.splice(index, 1)
}

const deleteSubDimension = (index: number, indexChild: number) => {
  form.value.dimensions[index].children.splice(indexChild, 1)
  proxy.$refs['formParamsRef'].validateField('dimensions.' + index + '.fullScore')
}

const _getDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
  })
  const res = await queryDetail(props.id);
  loading.close()
  oldDimension.value = res.data.dimensions
  oldName.value = res.data.name
  form.value.dimensions = JSON.parse(res.data.dimensions).dimensions
  form.value.name = res.data.name
  form.value.system = res.data.system
  form.value.type = res.data.type
  // oldFromInfo.value = deepClone(res.data)
}


const handleSubmit = async () => {

  if (form.value.dimensions.length > 6 || form.value.dimensions.length < 3) {
    ElMessage({
      message: '评分主维度数量限制在3-6个',
      type: 'warning'
    })
    return
  }
  for (let i = 0; i < form.value.dimensions.length; i++) {
    const item = form.value.dimensions[i]
    if (item?.children.length > 5 || item?.children.length < 2) {
      ElMessage({
        message: '请确保每个主维度下，子维度数量为2-5个',
        type: 'warning'
      })
      return
    }
  }
  const totalScore = form.value.dimensions.reduce((total: number, item: any) => {
    return total + Number(item.fullScore)
  },
    0)
  if (totalScore !== 100) {
    ElMessage({
      message: '请确保所有维度总和为100分',
      type: 'warning'
    })
    return
  }

  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const data = {
        ...form.value,
        id: props.id,
      }
      data.dimensions = JSON.stringify({
        dimensions: data.dimensions
      })
      btnLoading.value = true

      await handleSave(data).then((res) => {
        proxy.$modal.msgSuccess('保存成功');
        emits('change-type', { flagType: 'list', })

      }).finally(() => {
        btnLoading.value = false;
      });



    } else {
      console.log('error submit!!');
      return false;
    }
  })


}

_getDetail()
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  // margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}


@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;

  }
}

.box-container {
  display: flex;
  flex-direction: column;
  padding: 0 30px 30px 30px;
  gap: 12px 0;
  width: 100%;
  align-items: flex-start;
  margin: 0 auto;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;
  }
}



:deep(.el-card) {
  // height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 4px - 162px);
    overflow-y: auto;
  }
}



:deep(.el-form) {
  width: 100%;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}


.delete {
  cursor: pointer;
}


.tips {
  color: #999999;
  font-size: 12px;
  padding: 12px 0 4px;
}
:deep(.el-form-item--default) {
  margin-bottom: 0;
}

.form-title {
  font-size: 16px;
  font-weight: bold;
  margin-top: 20px;
}

.dimension-list {
  width: 100%;
}

.dimension-item {
  display: flex;
  padding: 8px 0;
  gap: 0 15px;
}

.name {
  :deep(.el-form-item__error) {
    position: absolute;
    width: 300px !important;
    left: 0 !important;
    font-weight: normal;
  }
}

.dimension-type {
  width: 200px;
  padding-left: 34px;
  position: relative;

  &-main {
    font-weight: bold;
  }

  :deep(.el-form-item__error) {
    position: absolute;
    width: 300px !important;
    left: 0 !important;
    font-weight: normal;
  }
}

.dimension-score {
  width: 80px;

  :deep( .el-form-item__error) {
    position: absolute;
    width: 300px !important;
    right: 0;
    text-align: right;
    left: auto !important;
  }
}

.dimension-standard {
  flex: 1;
}

:deep(.el-textarea) {
  .el-textarea__inner {
    min-height: 34px !important;
    line-height: 34px !important;
    padding: 0 11px;
  }
}

.dimension-actions {
  flex: 0 0 180px;
  display: flex;
  align-items: center;
}

.dimension-head {
  margin-top: 4px;
  font-size: 14px;
  color: #999999;
}

.star {
  width: 24px;
  height: 24px;
  position: absolute;
  left: 0;
}

.el-divider--horizontal {
  border-top: 1px #f3f3f3 var(--el-border-style);
  margin: 8px 0;
}
</style>
