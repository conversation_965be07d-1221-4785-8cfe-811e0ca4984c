
# 开发环境配置
VITE_APP_ENV = 'development'
VITE_APP_PORT = 81
VITE_APP_COMPANY = 'kangyuan'
# 开发环境
VITE_APP_BASE_API = '/dev-api'

VITE_APP_PROXY = 'https://qingnang.kanion.com/dev/prod-api/'

# 监控地址
VITE_APP_MONITRO_ADMIN = 'http://localhost:9090/admin/applications'

# powerjob 控制台地址
VITE_APP_POWERJOB_ADMIN = 'http://localhost:7700/'

# websocket 开关(开发环境默认关闭ws 因vite的bug导致如ws无法连接则会崩溃)
VITE_APP_WEBSOCKET = false

# OSS存储前缀
VITE_APP_OSS_PREFIX = 'https://obsqingnang.guoyaoplat.com'

VITE_APP_TITLE = 'AI陪练管理后台'

VITE_APP_ICO = '/qnqfavicon.ico'
