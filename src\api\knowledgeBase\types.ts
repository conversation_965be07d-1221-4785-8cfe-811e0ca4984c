export interface knowledgeListQuery extends PageQuery {
  orderByColumn?: string;
  isAsc?: string;
}
// export interface knowledgeItemListQuery extends PageQuery {
//   orderByColumn?: string;
//   isAsc?: string;
//   kbId?: string;
//   /**
//    * 状态 1-处理中 2-可用 3-失败
//    */
//   status?: number;
// }

export interface knowledgeItemListQuery {
  orderByColumn?: string;
  isAsc?: string;
  kbId?: string;
  /**
   * 状态 1-处理中 2-可用 3-失败
   */
  status?: number;
  pageNum?: number;
  pageSize?: number;
}

/**
 * KnowledgeBaseVo，知识库列表
 */
export interface KnowledgeBaseVo {
  /**
   * 描述
   */
  description: string;
  /**
   * 文件数量
   */
  fileNum: number;
  id: number;
  /**
   * 名称
   */
  name: string;
  [property: string]: any;
}
/**
 * KnowledgeBaseBo
 */
export interface KnowledgeBaseBo {
  id?: number | string;
  /**
   * 描述
   */
  description: string;
  type: number;

  /**
   * 名称
   */
  name: string;
  /**
   * oss对象存储ids
   */
  ossIds?: any;
}
/**
 * KnowledgeBaseItemVo，知识库内容列表
 */
export interface KnowledgeBaseItemVo {
  /**
   * 上传时间
   */
  createTime: Date;
  /**
   * 文件后缀名
   */
  fileSuffix: string;
  id: string;
  /**
   * 知识库id
   */
  kbId: number;
  /**
   * OSS对象存储表id
   */
  ossId: number;
  /**
   * 文件大小
   */
  size: number | string;
  /**
   * 状态 1-处理中 2-可用 3-失败
   */
  status: number;
  type: number;
  url: string;
  labelInfo: KnowledgeBaseItemLabelInfoVo | null;
  [property: string]: any;
  voiceInfo: KnowledgeBaseItemVoiceVo;
}

/**
 * KnowledgeBaseItemVoiceVo
 */
export interface KnowledgeBaseItemVoiceVo {
  /**
   * 内容
   */
  content: string;
  /**
   * 知识库内容id
   */
  kbItemId: number;
  /**
   * 合并方式 1-添加在末尾 2-替换原内容
   */
  mergeMethod: number;
  /**
   * 文件名
   */
  name: string;
  /**
   * 对象存储主键
   */
  ossId: number;
  /**
   * URL地址
   */
  url: string;
  [property: string]: any;
}

/**
 * KnowledgeBaseItemLabelInfoVo
 */
export interface KnowledgeBaseItemLabelInfoVo {
  /**
   * 内容
   */
  content?: string;
  /**
   * 知识库内容id
   */
  kbItemId?: number;
  /**
   * 标题
   */
  title?: string;
  [property: string]: any;
}
/**
 * KnowledgeBaseItemLabelInfoBo
 */
export interface KnowledgeBaseItemLabelInfoBo {
  /**
   * 内容
   */
  content?: string;
  /**
   * 知识库内容id
   */
  kbItemId?: number;
  /**
   * 标题
   */
  title?: string;
  [property: string]: any;
}
/**
 * KnowledgeBaseItemVoiceVo
 */
export interface KnowledgeBaseItemVoiceVo {
  /**
   * 内容
   */
  content: string;
  /**
   * 知识库内容id
   */
  kbItemId: number;
  /**
   * 合并方式 1-添加在末尾 2-替换原内容
   */
  mergeMethod: number;
  /**
   * 文件名
   */
  name: string;
  /**
   * 对象存储主键
   */
  ossId: number;
  /**
   * URL地址
   */
  url: string;
}
