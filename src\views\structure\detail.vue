<template>
  <div>
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" :close-on-click-modal="false" append-to-body :show-close="false">
      <div class="box-container" v-if="editFlag" v-loading="loading">
        <el-form ref="formParamsRef" :model="formParams" :rules="editFlag ? rules : ''" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="姓名" prop="name">
                <el-input :readonly="!editFlag" v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="手机号" prop="phoneNumber">
                <el-input :readonly="!editFlag" v-model="formParams.phoneNumber" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="部门" prop="deptId">
                <el-tree-select
                  style="width: 100%;"
                  v-model="formParams.deptId"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  placeholder="请选择部门"
                  check-strictly
                  filterable
                  clearable
                  :default-expand-all="true"
                  popper-class="dept-popper"
                >
                  <template #empty> <span>部门不存在</span></template>
                </el-tree-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="性别" prop="gender">
                <el-radio-group :disabled="!editFlag" v-model="formParams.gender">
                  <el-radio :value="0" :label="0">男</el-radio>
                  <el-radio :value="1" :label="1">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="box-container" v-else>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">姓名</div>
              <div class="right">
                <span>{{ oldFromInfo.name }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">手机号</div>
              <div class="right">
                <span>{{ oldFromInfo.phoneNumber }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">部门</div>
              <div class="right">
                <span>{{ oldFromInfo.deptName }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">性别</div>
              <div class="right">
                <span>{{ oldFromInfo.gender === null ? '暂无' : oldFromInfo.gender === 1 ? '女' : '男' }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">账号开通时间</div>
              <div class="right">
                <span v-formatTime="oldFromInfo.createTime"></span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">关闭</el-button>
          <el-button type="primary" v-if="editFlag" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { reactive, ref } from 'vue'
import { saveOrUpdateUser, getUserDetail } from '@/api/user'
import { userResult, userFormVo } from '@/api/user/types'
import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success'])
import { getDeptTreeList} from "@/api/department/index"
import { TreeLong } from "@/api/department/types";
import { departmentTreeUserDisable } from '@/utils';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '查看'
  },
})
const initForm = {
  gender: null,
  name: '',
  phoneNumber: '',
  id: ''
}
const editFlag = ref(props.isEdit)
const deptOptions = ref<TreeLong[]>([])
const btnLoading = ref(false)
const loading = ref(false)

const formParams = ref<userFormVo>(initForm)
const oldFromInfo = ref<userResult>({} as userResult)
import type { FormRules } from 'element-plus'
interface RuleForm {
  name: string
  phoneNumber: string,
  deptId:string
}

const formParamsRef = ref()

// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
  ],
  deptId:[
    { required: true, message: '请选择部门', trigger: 'change' },
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern:
        /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
      message: '请填写正确的手机号',
      trigger: 'blur'
    },
  ],
})
const back = () => {
  if (!editFlag.value) {
    //查看
    oldFromInfo.value = {} as userResult
    visibleAdd.value = false
    return
  }
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
      ) {
        bool = true;
        return;
      }
    }

  });

  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }



}



const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
      };
      if (oldFromInfo.value.phoneNumber !== formParams.value.phoneNumber) {
        ElMessageBox.confirm(
          '修改后原手机号将无法登录，原手机号的账号信息将绑定至新手机号',
          '确定要修改手机号吗',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(async () => {

            await saveOrUpdateUser(params).finally(() => {
              btnLoading.value = false;
            });

            proxy.$modal.msgSuccess('保存成功');
            closeDialog()
            emits('success')
          })
          .catch(() => {

          })
      } else {
        await saveOrUpdateUser(params).finally(() => {
          btnLoading.value = false;
        });

        proxy.$modal.msgSuccess('保存成功');
        closeDialog()
        emits('success')
      }
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}

const closeDialog = () => {
  formParamsRef.value.resetFields()
  formParams.value = initForm
  visibleAdd.value = false
}
watch(() => props.visible,
  val => {
    if (val) {
      editFlag.value = props.isEdit
      _getUserDetail()

    }
  }
)
const _getUserDetail = async () => {
  const res = await getUserDetail({ id: props.id });
  formParams.value = {
    name: res.data.name,
    gender: res.data.gender,
    phoneNumber: res.data.phoneNumber,
    id: res.data.id,
    deptId:res.data.deptId,
  }
  oldFromInfo.value = res.data
  _getDeptTreeList()
}
const _getDeptTreeList = async () => {
  loading.value = true
  const res = await getDeptTreeList()
  if(userStore.cpFlag) {
      deptOptions.value = departmentTreeUserDisable(res.data)
    } else {
      deptOptions.value = res.data
      // formParams.value.deptId = res.data[0].id
    }

  loading.value = false
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.user-msg {
  margin-left: 32px;
  display: flex;
  align-items: flex-start;
  // margin-bottom: 24px;

  .left {
    width: 100px;
    margin-right: 16px;
    font-weight: bold;
  }

  .right {
    flex: 1;
  }
}


:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}
</style>
