<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />
          <div class="box-title flex">
            <div class="user-info flex">
              <img :src="analyseData.avatar || avatar_default_man" />
              <div>{{ analyseData.name }}</div>
            </div>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="任务名称" align="center" prop="missionName" />
        <el-table-column label="时间" align="center" prop="pointTime" />
        <el-table-column label="积分" align="center" prop="points">
          <template #default="scope">
            {{ scope.row.points > 0 ? '+' + scope.row.points : scope.row.points }}
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="user" lang="ts">
import dayjs from 'dayjs'
// import { onBeforeRouteUpdate } from "vue-router";
const router = useRouter();
import { getUserDetail } from '@/api/user'
import avatar_default_man from "@/assets/images/avatar_default_man.jpg";
import { getPointRecordList } from "@/api/point";
import { PointRecordVo } from "@/api/point/types";
// 1-未生成，2-已生成，3-生成失败，4-生成中
const route = useRoute();
const dataList = ref<PointRecordVo[]>([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: (route.query.userId || '') as string | number,
  },
});
const { queryParams } = toRefs(data);
const analyseData = ref<any>({
  avatar: '',
  name: '',
})


//查询列表
const getList = async () => {
  loading.value = true;
  const res = await getPointRecordList(queryParams.value);
  dataList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
const _getUserDetail = async () => {
  const res = await getUserDetail({ id: queryParams.value.userId });
  analyseData.value = {
    avatar: res.data.avatar,
    name: res.data.name
  }
}

getUserDetail
onMounted(() => {
  getList()
  _getUserDetail()
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}



.card-title {
  font-size: 24px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0 12px;

  img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-left: 10px;
  }


}


:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 84px);
  }
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}
</style>
