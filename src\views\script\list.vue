<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">脚本管理</span>
          <div class="search-container">
            <el-form ref="queryFormRef" :model="queryParams" @submit.prevent>
              <div class="flex">
                <el-form-item prop="products">
                  <el-select
                    @change="handleQuery"
                    clearable
                    style="min-width: 180px"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="1"
                    multiple
                    v-model="queryParams.products"
                    placeholder="请选择产品"
                    filterable
                  >
                    <el-option v-for="item in productList" :key="item" :label="item" :value="item" />
                  </el-select>
                </el-form-item>
                <el-form-item prop="queryDeptTemIds">
                  <el-tree-select
                    v-model="queryParams.queryDeptTemIds"
                    @change="handleQuery"
                    clearable
                    :data="deptOptions"
                    popper-class="dept-popper"
                    :props="{ value: 'id', label: 'label', children: 'children', disabled: 'selected' }"
                    node-key="id"
                    check-strictly
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="1"
                    multiple
                    show-checkbox
                    style="width: 180px"
                    placeholder="请选择部门"
                    :default-expanded-keys="defaultExpandedKeys"
                    :default-expand-all="false"
                  />
                </el-form-item>
                <el-dropdown v-if="queryParams.status === 2">
                  <el-button>
                    <!-- <el-icon>
                    <SetUp />
                  </el-icon> -->
                    <img src="@/assets/icons/png/more.png" style="width:20px;height: 20px;" />
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="_handleBatchAuthority" :disabled="multipleSelection.length === 0">权限设置</el-dropdown-item>
                    </el-dropdown-menu>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="_handleBatchInput" :disabled="multipleSelection.length === 0">练习设置</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button type="primary" icon="Plus" @click="handleAdd">创建脚本</el-button>
              </div>
            </el-form>
          </div>
        </div>
      </template>
      <div class="header">
        <el-tabs v-model="queryParams.status" class="demo-tabs" @tab-click="switchTab">
          <el-tab-pane label="已上线" :name="2"></el-tab-pane>
          <el-tab-pane label="待上线" :name="1"></el-tab-pane>
          <el-tab-pane label="已作废" :name="3"></el-tab-pane>
          <!-- <el-tab-pane label="闯关设置" :name="4"></el-tab-pane> -->
        </el-tabs>
      </div>

      <el-table
        v-loading="loading"
        :data="scrptList"
        @row-click="handleDetail"
        v-if="queryParams.status !== 4"
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
        :tooltip-options="{ popperClass: 'tooltip-table' }"
      >
        <!-- <el-table-column label="编号" align="center" type="index" width="150" /> -->
        <el-table-column type="selection" width="55" align="center" reserve-selection />
        <el-table-column label="名称" align="center" prop="name" min-width="240">
          <template #default="scope">
            <div class="flex ">
              <span class="script-name"> {{ scope.row.name }}</span>
              <img
                src="@/assets/icons/png/level.png"
                v-if="scope.row.onlyEnableInEventLevel"
                style="width: 16px; height: 16px;margin-left: 8px;margin-bottom: 2px;"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="产品" align="center" prop="product" min-width="150">
          <template #default="scope">
            <span :style="{ color: scope.row.product ? '' : '#ff0000' }"> {{ scope.row.product || '待完善' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="类型" align="center" prop="type" width="100px">
          <template #default="scope">
            <span v-if="scope.row.type === 1">技巧类</span>
            <span v-else-if="scope.row.type === 2">答题类</span>
            <span v-else-if="scope.row.type === 3">幻灯片演练</span>
          </template>
        </el-table-column>
        <el-table-column label="评分标准" align="center" prop="scoringStandardName" width="220px" />

        <!-- <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span v-formatTime="scope.row.createTime"></span>
          </template>
        </el-table-column> -->
        <el-table-column label="权限" align="center" prop="deptIds" min-width="240" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.deptNames ? scope.row.deptNames : '全部成员' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="left" prop="status" width="150px">
          <template #default="scope">
            <template v-if="scope.row.type === 3 && queryParams.status === 1">
              <div class="flex">
                <div class="dot" :style="{ backgroundColor: colorMap.get(scope.row.parseStatus) }"></div>
                <span v-if="scope.row.status === 1 && scope.row.parseStatus === 0">待上线</span>
                <span v-if="scope.row.status === 1 && scope.row.parseStatus === 2">处理失败</span>
                <el-button
                  link
                  type="primary"
                  v-if="scope.row.status === 1 && scope.row.parseStatus === 2"
                  @click.stop="handleRegenerate(scope.row.id)"
                  >重试</el-button
                >
                <span v-if="scope.row.status === 1 && scope.row.parseStatus === 3">待上线</span>
                <span v-if="scope.row.status === 1 && scope.row.parseStatus === 1">解析中</span>
                <span v-if="scope.row.status === 1 && scope.row.parseStatus === 4">AI处理中</span>
              </div>
            </template>
            <template v-else>
              <div class="flex">
                <div class="dot" v-if="scope.row.status === 1" style="background-color: #FFA500;"></div>
                <span v-if="scope.row.status === 1">待上线</span>
              </div>
            </template>

            <!-- <span v-if="scope.row.status === 1">待上线</span> -->
            <div class="flex" v-if="queryParams.status === 2 || queryParams.status === 3">
              <div class="dot" :style="{ backgroundColor: colorStatusMap.get(scope.row.status) }"></div>
              <span v-if="scope.row.status === 2">已上线</span>
              <span v-else-if="scope.row.status === 3">已作废</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column width="200" label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="gap-12">
              <template v-if="scope.row.parseStatus !== 2 && scope.row.parseStatus !== 1">
                <el-button
                  link
                  type="primary"
                  v-if="scope.row.status !== 3"
                  :disabled="scope.row.parseStatus === 4"
                  @click.stop="handleEdit(scope)"
                  text
                  >编辑</el-button
                >
                <!-- <el-button link type="primary" v-if="scope.row.status === 2" text disabled>复制</el-button> -->
                <span @click.stop v-if="scope.row.status === 2 || scope.row.status === 1" style="display: flex;align-items: center;">
                  <el-popconfirm
                    width="220"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="confirm(scope)"
                    title="确定上线吗？"
                  >
                    <template #reference>
                      <el-button :disabled="!scope.row.product || scope.row.parseStatus === 4" link type="primary" text v-if="scope.row.status === 1"
                        >上线</el-button
                      >
                    </template>
                  </el-popconfirm>

                  <el-popover
                    trigger="click"
                    placement="top"
                    :ref="(el: refItem) => handleSetDeletePopMap(el, scope.row.id)"
                    :width="300"
                    :hide-after="0"
                    popper-class="popover"
                  >
                    <div style="display: flex;flex-direction: column;gap:8px 0;">
                      <span class="title">确定下线吗？</span>
                      <span class="message">下线后用户无法查看、使用此脚本</span>
                    </div>
                    <div style="text-align: right; margin: 0;margin-top: 30px;">
                      <el-button @click="cancelOffLine(scope.row.id)">取消</el-button>
                      <el-button type="primary" @click.stop="confirm(scope)">确定</el-button>
                    </div>
                    <template #reference>
                      <el-button link type="primary" text v-if="scope.row.status === 2">下线</el-button>
                    </template>
                  </el-popover>
                </span>
                <span @click.stop v-if="scope.row.status === 3 || scope.row.status === 1" style="display: flex;align-items: center;">
                  <el-popconfirm
                    width="220"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="handleInvalidOrReCover(scope)"
                    :title="`确定${scope.row.status === 3 ? '恢复' : '作废'}吗?`"
                  >
                    <template #reference>
                      <el-button link type="primary" v-if="scope.row.status === 3" text>恢复</el-button>
                      <el-button link type="primary" v-else-if="scope.row.status === 1" text>作废</el-button>
                    </template>
                  </el-popconfirm>
                </span>
                <el-button
                  link
                  type="primary"
                  :disabled="scope.row.parseStatus === 4"
                  v-if="scope.row.status !== 3"
                  @click.stop="showClick(scope.row.id)"
                  text
                  >更多</el-button
                >

                <el-dropdown
                  style="margin-top: 8px;margin-left: -18px;"
                  :ref="(el: refItem) => handleSetInputMap(el, scope.row.id)"
                  :trigger="'contextmenu' + scope.row.id"
                >
                  <el-button link type="primary" text></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleAuth(scope)">权限设置</el-dropdown-item>
                      <el-dropdown-item @click="handleInputDisabled(scope)">练习设置</el-dropdown-item>
                      <el-dropdown-item @click="handleNotify(scope)" v-if="scope.row.status === 2 && !scope.row.notifyFlag && !userStore.cpFlag"
                        >发送通知</el-dropdown-item
                      >
                      <el-dropdown-item v-if="scope.row.status === 2 && scope.row.notifyFlag && !userStore.cpFlag">已发送通知</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
              <template v-else-if="scope.row.status === 1 && scope.row.parseStatus === 2">
                <span @click.stop>
                  <el-popconfirm
                    width="220"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="handleDelete(scope)"
                    title="确定删除吗？"
                  >
                    <template #reference>
                      <el-button link type="primary" text v-if="scope.row.status === 1">删除</el-button>
                    </template>
                  </el-popconfirm>
                </span>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-if="queryParams.status !== 4"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      <LevelGroup v-if="queryParams.status === 4" @change-type="handleChangeType"></LevelGroup>
    </el-card>
    <DeptAuthority :script-ids="scriptIds" title="权限设置" v-model:visible="showDept" @success="getList"></DeptAuthority>
    <Notification :script-id="scriptId" :script-name="scriptName" title="发送通知" v-model:visible="showNotify" @success="getList"></Notification>
    <DisabledInput
      title="练习设置"
      :ids="currentIds"
      :input-flag="currentInputFlag"
      :typo-correct-flag="currentTypoCorrectFlag"
      :aiHelpFlag="currentAiHelpFlag"
      v-model:visible="showDisabledInput"
      :requirementShowFlag="requirementShowFlag"
      @success="getList"
    >
    </DisabledInput>
  </div>
  <el-dialog class="script-type-dialog" v-model="chooseScriptDialogVisible" width="100%" align-center :show-close="false">
    <template #title>
      <div class="script-type-head">请选择脚本类型</div>
    </template>
    <div class="script-type-container">
      <div class="script-type-box" @click="showCreateDialog">
        <img class="script-type-icon" src="@/assets/images/script_skill.png" />
        <el-button type="primary" size="large" class="script-type-title">技巧类</el-button>
        <div class="script-type-tip" type="info">沉浸式RolePlay脚本，适合全面提升销售的拜访技巧</div>
      </div>

      <div class="script-type-box" @click="chooseScriptType('question')">
        <img class="script-type-icon" src="@/assets/images/script_question.png" />
        <el-button type="primary" size="large" class="script-type-title">答题类</el-button>
        <div class="script-type-tip" type="info">智能问答型脚本，适合提升销售的产品知识</div>
      </div>

      <div class="script-type-box" @click="chooseScriptType('ppt')">
        <img class="script-type-icon script-type-icon-ppt" src="@/assets/images/script_ppt.svg" />
        <el-button type="primary" size="large" class="script-type-title">幻灯片演练</el-button>
        <div class="script-type-tip" type="info">PPT练习脚本，适合提升销售的科室会讲解能力</div>
      </div>
    </div>
    <img class="script-type-close" @click="closeScriptTypeDialog" src="@/assets/images/icon_cancel.svg" />
  </el-dialog>

  <ScriptCreateDialog
    v-model:visible="scriptCreateDialogShow"
    dialog-type="create"
    @on-manual="chooseScriptType('skill')"
    @on-success="onCreateScriptSuccess"
  ></ScriptCreateDialog>
  <ScriptCreateStatusDialog
    v-model:visible="scriptCreateStatusDialogShow"
    :scriptId="generateScriptId"
    :params="generateScriptForm"
    @on-success="onScriptStatusScuess"
  ></ScriptCreateStatusDialog>
  <PdfScriptPage ref="PdfScriptPageRef" @onRest="switchPageTab" @sendPDF="_sendPDF" />
</template>

<script setup lang="ts">
import { onBeforeRouteLeave } from "vue-router";
import { getScriptList, onlineScript, offlineScript, recoverScript, invalidScript, getAllProduct, pptParseRetry, deleteScript } from '@/api/script';
import { scriptQuery, scriptListResult } from '@/api/script/types';
import { TreeLong } from "@/api/department/types";
import { useUserStore } from '@/store/modules/user';
import DeptAuthority from "./components/authority.vue";
import Notification from "./components/notification.vue";
import LevelGroup from "./components/levelGroup.vue";
import DisabledInput from "./components/disabledInput.vue";
import ScriptCreateDialog from './components/scriptCreateDialog.vue';
import ScriptCreateStatusDialog from './components/scriptCreateStatusDialog.vue';
import PdfScriptPage from "./pdfScriptPage.vue";

import cache from '@/plugins/cache';
// import router from '@/router';
import type { TabsPaneContext, TableInstance } from 'element-plus'
import { ComponentPublicInstance } from "vue";
import { deepClone } from "@/utils";
import { login } from "@/api/login";
type refItem = Element | ComponentPublicInstance | null;
const defaultExpandedKeys = ref<string[]>([])
const userStore = useUserStore();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const scrptList = ref<scriptListResult[]>([]);
const loading = ref(true);
const total = ref(0);
const chooseScriptDialogVisible = ref(false);
const emits = defineEmits(['change-type']);
const currentIds = ref<string[]>([])
const showDept = ref(false)
const showNotify = ref(false)
const showDisabledInput = ref(false)
const requirementShowFlag = ref(false)

const scriptId = ref('')
const scriptIds = ref<string[]>([])
const scriptName = ref('')
const currentInputFlag = ref(false)
const currentTypoCorrectFlag = ref(false)
const currentAiHelpFlag = ref(false)
const multipleSelection = ref<scriptListResult[]>([]);
const deptOptions = ref<TreeLong[]>([])
const onOrOffLinePopverRefMap = ref({});
const scriptCreateDialogShow = ref(false)
const scriptCreateStatusDialogShow = ref(false)
const timer = ref<any>(null)
const unfinishFlag = ref(false)
const tableRef = ref<TableInstance>()

// ppt
// const PdfScriptPageShow = ref(false)
const PdfScriptPageRef = ref()
const colorMap = new Map([
  [0, '#FFA500'],
  [1, '#3C83FF'],
  [3, '#FFA500'],
  [2, '#FF1C1C'],
  [4, '#3C83FF'],
])

const colorStatusMap = new Map([
  [2, '#32CD32'],
  [3, '#D9D9D9'],
])

const handleRegenerate = async (id) => {
  try {
    try {
      await pptParseRetry({ id: id })
      // requestResult()
      // number = 0 //重新刷新
      // unfinishFlag.value = false
      getList()
    } catch (error) {
      console.log(error);
    }

  } catch (error) {
    console.log(error);
  }
}


const generateScriptId = ref('')
const generateScriptForm = ref<{
  visitObject: string
  visitStage: string
  keyInfo: string
}>()

const props = defineProps({
  listType: {
    type: Number,
    default: null
  }
})
const inputRefMap = ref({});
const productList = ref<string[]>([]);
// const dropdown1 = ref<DropdownInstance>()
const data = reactive<PageQueryData<scriptQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    products: [],
    queryDeptIds: [],
    queryDeptTemIds: [],
    keyword: '',
    status: props.listType as unknown as number || 2,
    type: null
  },
});

const { queryParams } = toRefs(data);
/**表格多选 */
const handleSelectionChange = (val: userResult[]) => {
  multipleSelection.value = val
}
const showClick = (id: string) => {
  if (!inputRefMap.value[`Input_Ref_${id}`]) return
  inputRefMap.value[`Input_Ref_${id}`].handleOpen()
}

const handleChangeType = (query: any) => {
  emits('change-type', query)
}

const handleSetInputMap = (el: refItem, item: string) => {
  if (el) {
    inputRefMap.value[`Input_Ref_${item}`] = el;
  }
}

const switchPageTab = () => {
  console.log("成功3")
  queryParams.value.status = 1
  queryParams.value.pageNum = 1

  getList()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  queryParams.value.queryDeptIds = queryParams.value.queryDeptTemIds![0] === deptOptions.value[0].id ? null : queryParams.value.queryDeptTemIds
  getList();
}


const handleAdd = () => {
  chooseScriptDialogVisible.value = true
  // emits('change-type', { flagType: 'add' ,listType:queryParams.value.status})
}
/**详情 */
const handleDetail = (row: scriptListResult) => {
  if (row.parseStatus === 1 || row.parseStatus === 4) return
  cache.session.setJSON('scriptListQuery', queryParams.value)
  let flagType;
  let extra;
  switch (row.type) {
    case 1:
      flagType = 'detail-skill'
      break;
    case 2:
      flagType = 'detail'
      break;
    case 3:
      flagType = 'detail-ppt';
      extra = {
        deptNames: row.deptNames
      }
      break;
  }
  emits('change-type', { flagType, isEdit: false, id: row.id, listType: queryParams.value.status, extra })
}

/**编辑 */
const handleEdit = (scope: any) => {
  cache.session.setJSON('scriptListQuery', queryParams.value)
  let flagType;
  switch (scope.row.type) {
    case 1:
      flagType = 'detail-skill'
      break;
    case 2:
      flagType = 'detail'
      break;
    case 3:
      flagType = 'detail-ppt';
      break;
  }
  emits('change-type',
    {
      flagType,
      isEdit: true,
      id: scope.row.id,
      listType: queryParams.value.status
    })
}

const chooseScriptType = (type: string) => {
  chooseScriptDialogVisible.value = false
  if (type === 'ppt') {
    PdfScriptPageRef.value.setShowPDFUpload(true)
  } else {
    emits('change-type', { flagType: 'add-' + type, listType: queryParams.value.status })
  }
}
const _sendPDF = (fileList: any, product: string) => {
  emits('change-type', { flagType: 'add-ppt', listType: queryParams.value.status, extra: { fileList, product } })
}
// 删除
const handleDelete = async (scope: any) => {
  await deleteScript(scope.row.id)
  proxy.$modal.msgSuccess('删除成功');
  getList()
}

let pageLoading = null
let number = 0 //请求次数
// 待上线处理
const waitOnline = () => {
  // unfinishFlag.value = false
  getScriptList(queryParams.value).then(res => {
    pageLoading?.close()
    scrptList.value = res.rows;
    if (JSON.stringify(scrptList.value) !== JSON.stringify(res.rows)) {
      scrptList.value = deepClone(res.rows)
    }

    total.value = res.total;
    loading.value = false;
    const index = res.rows.findIndex(item => (item.parseStatus === 1 || item.parseStatus === 4))
    console.log(index)
    if (index !== -1) {
      unfinishFlag.value = true // 有未解析
    } else {
      // console.log(unfinishFlag.value)
      if (unfinishFlag.value) {
        proxy.$modal.msgSuccess('幻灯片处理完成')
      }
      console.log(timer.value)
      if (timer.value) {
        console.log('清除')
        clearInterval(timer.value);
        timer.value = null
      }
    }
  })
}


/** 查询列表 */
const getList = async () => {
  pageLoading = ElLoading.service({
    lock: true,
    text: '正在加载',
  })
  number = 0 //重新刷新
  unfinishFlag.value = false
  try {
    if (queryParams.value.status === 1) {
      if (number === 0) {
        waitOnline()
      }
      if (!unfinishFlag.value) {
        timer.value = setInterval(async () => {
          number++
          waitOnline()
        }, 5000);
      }

    } else {
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null
      }
      const res = await getScriptList(queryParams.value);

      pageLoading.close()
      scrptList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    }
  } catch (error) {
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null
    }
    console.log(error)
    pageLoading.close()
  }
}
const _getDeptTreeList = async () => {
  const res = await userStore.getDeptTreeSelectedListFn()
  if (res.length > 0) {
    deptOptions.value = res
    defaultExpandedKeys.value = res.length > 0 ? [res[0].id] : []
  }
}
const _getProductiList = async () => {
  const res = await getAllProduct(queryParams.value.status)
  productList.value = res.data.length > 0 ? res.data : []
}

const confirm = async (scope: any) => {
  if (scope.row.status === 1) {
    await onlineScript({ id: scope.row.id })
    proxy.$modal.msgSuccess('上线成功');
    getList()
  } else {
    // const res = await checkScriptInLevel({ scriptId: scope.row.id })
    // if (res.data) {
    //   ElMessageBox.confirm(
    //     '此脚本参与闯关中，可能会影响关卡任务的完成，请谨慎操作。',
    //     '确定要继续下线吗？',
    //     {
    //       confirmButtonText: '确定',
    //       cancelButtonText: '取消',
    //       type: 'none',
    //     }
    //   )
    //     .then(async () => {
    //       await offlineScript({ id: scope.row.id })
    //       onOrOffLinePopverRefMap.value[`Pop_Ref_${scope.row.id}`].hide()
    //       proxy.$modal.msgSuccess('下线成功');
    //       getList()
    //     })
    //     .catch(() => {
    //     })
    // } else {
    await offlineScript({ id: scope.row.id })
    onOrOffLinePopverRefMap.value[`Pop_Ref_${scope.row.id}`].hide()
    proxy.$modal.msgSuccess('下线成功');
    getList()
    // }
  }
}



const handleSetDeletePopMap = (el: refItem, item: string) => {
  if (el) {
    onOrOffLinePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelOffLine = (id) => {
  onOrOffLinePopverRefMap.value[`Pop_Ref_${id}`].hide()
}



const handleInvalidOrReCover = async (scope: any) => {
  if (scope.row.status === 3) {
    await recoverScript({ id: scope.row.id })
    proxy.$modal.msgSuccess('恢复成功');
    getList()
  } else {
    await invalidScript({ id: scope.row.id })
    proxy.$modal.msgSuccess('作废成功');
    getList()
  }
}


/**权限设置 */
const handleAuth = async (scope: any) => {
  // const res = await checkScriptInLevel({ scriptId: scope.row.id })
  // if (res.data) {
  //   ElMessageBox.confirm(
  //     '此脚本为参与闯关中，可能会影响关卡任务的完成，请谨慎操作。',
  //     '提醒',
  //     {
  //       confirmButtonText: '好的',
  //       cancelButtonText: '取消',
  //       type: 'warning',
  //     }
  //   )
  //     .then(async () => {
  //       showDept.value = true
  //       scriptIds.value = [scope.row.id]
  //     })
  //     .catch(() => {
  //     })
  // } else {

  // }
  showDept.value = true
  scriptIds.value = [scope.row.id]

}


const handleInputDisabled = (scope: any) => {
  showDisabledInput.value = true
  currentInputFlag.value = scope.row.disableTextInputFlag
  currentTypoCorrectFlag.value = scope.row.typoCorrectFlag
  currentAiHelpFlag.value = scope.row.aiHelpFlag
  requirementShowFlag.value = scope.row.requirementShowFlag

  currentIds.value = [scope.row.id]
}


const _handleBatchInput = () => {
  const array = multipleSelection.value.map((item: any) => item.id)
  currentIds.value = array
  currentInputFlag.value = false
  currentTypoCorrectFlag.value = false
  showDisabledInput.value = true
}

const _handleBatchAuthority = () => {
  const array = multipleSelection.value.map((item: any) => item.id)
  scriptIds.value = array
  showDept.value = true
}


/**通知设置 */
const handleNotify = (scope: any) => {
  showNotify.value = true
  scriptId.value = scope.row.id
  scriptName.value = scope.row.name

}



/**切换tab状态 */
const switchTab = (tab: TabsPaneContext) => {
  if (tab.props.name as number === 4) return
  queryParams.value.status = tab.props.name as number
  queryParams.value.pageNum = 1;
  queryParams.value.products = [];
  multipleSelection.value = []
  tableRef.value?.clearSelection()
  if (tab.props.name as number === 1) {
    number = 0
    unfinishFlag.value = false
  }
  getList();
  _getProductiList();
}

const closeScriptTypeDialog = () => {
  chooseScriptDialogVisible.value = false;
}

const showCreateDialog = () => {
  closeScriptTypeDialog()
  scriptCreateDialogShow.value = true
}

const onCreateScriptSuccess = (id: string, params: {
  visitObject: string
  visitStage: string
  keyInfo: string
}) => {
  scriptCreateDialogShow.value = false
  // 显示结果弹窗
  scriptCreateStatusDialogShow.value = true
  generateScriptId.value = id
  generateScriptForm.value = params
}

const onScriptStatusScuess = (id: string) => {
  // 切换创建脚本
  scriptCreateStatusDialogShow.value = false
  emits('change-type', { flagType: 'add-skill', listType: queryParams.value.status, id, extra: generateScriptForm.value })
}


// ppt
const onAddSuccess = (fileLiet: any) => { }

onMounted(() => {
  if (queryParams.value.status === 4) return
  if (cache.session.getJSON('scriptListQuery')) {
    queryParams.value = cache.session.getJSON('scriptListQuery')
    cache.session.remove('scriptListQuery')
  }
  getList();
  _getDeptTreeList()
  _getProductiList()
})

onUnmounted(() => {
  // 清理定时器
  if (timer.value) {
    clearInterval(timer.value);
  }
});

onBeforeRouteLeave((to, from) => {
  const query = cache.session.getJSON('pointQuery')
  if (query) {
    cache.session.remove('pointQuery')
  }
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}

:global(.script-type-dialog) {
  background: none;
  box-shadow: none;
}

.script-type {
  &-head {
    color: #fff;
    text-align: center;
    font-size: 24px;
  }

  &-content {
    cursor: pointer;
  }

  &-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    gap: 46px;
  }

  &-box {
    width: 280px;
    height: 310px;
    border-radius: 9px;
    text-align: center;
    background-color: #fff;
    overflow: hidden;
  }

  &-icon {
    width: 93px;
    height: 93px;
    display: block;
    margin: 40px auto 17px;

    &-ppt {
      width: 120px;
      height: 89px;
      margin: 43px auto 20px;
    }
  }

  &-title {
    font-size: 20px;
    font-weight: 500;
    width: 187px;
  }

  &-tip {
    font-size: 15px;
    margin-top: 20px;
    margin-left: 46px;
    margin-right: 46px;
  }

  &-close {
    display: block;
    width: 32px;
    height: 32px;
    margin: 68px auto 0;
    cursor: pointer;
  }
}

:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 90px - 100px);
  }
}

.script-name {
  justify-content: flex-start;
  white-space: pre-wrap;
  text-align: left;
}

.flex {
  display: flex;
  align-items: center;
  gap: 0 8px;
}

:deep(.el-table__row) {
  cursor: pointer;
}

.gap-12 {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 0 12px;
}

.dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
}
</style>
