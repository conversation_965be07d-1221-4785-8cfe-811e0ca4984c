import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TencentVoiceData } from '../types';

/**职称分类 */
export function getProfessionalList(): AxiosPromise<any> {
  return request({
    url: '/biz/common/professional-title',
    method: 'get'
  });
}
/**男头像 */
export function getMaleAvatarList(): AxiosPromise<any> {
  return request({
    url: '/biz/common/male-avatar',
    method: 'get'
  });
}
/**女头像 */
export function getFemaleAvatarList(): AxiosPromise<any> {
  return request({
    url: '/biz/common/female-avatar',
    method: 'get'
  });
}
/**男声音 */
export function getMenVoiceList(): AxiosPromise<any> {
  return request({
    url: '/biz/common/men-voice',
    method: 'get'
  });
}
/**女声音 */
export function getWomenVoiceList(): AxiosPromise<any> {
  return request({
    url: '/biz/common/women-voice',
    method: 'get'
  });
}
/**昵称 */
export function getPersonName(): AxiosPromise<any> {
  return request({
    url: '/biz/common/person-name',
    method: 'get'
  });
}
/**性格 */
export function getPersonNature(): AxiosPromise<any> {
  return request({
    url: '/biz/common/person-nature',
    method: 'get'
  });
}

export function getTencentVoice(data: TencentVoiceData): AxiosPromise<any> {
  return request({
    url: '/biz/common/tencent-voice',
    method: 'get',
    params: data
  });
}
