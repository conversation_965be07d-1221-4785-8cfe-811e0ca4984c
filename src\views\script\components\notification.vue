<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="800px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px" v-loading="loading">
          <div class="box-title">通知内容</div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="任务名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="任务描述" prop="description"> <el-input v-model="formParams.description" placeholder="请输入" /> </el-form-item>
            </el-col>
          </el-row>
          <div class="box-title">通知范围</div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="通知脚本" prop="scriptName">{{ scriptName }} </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="通知对象" prop="type">
                <el-radio-group v-model="formParams.type" style="display: flex;flex-direction: column;align-items: flex-start;">
                  <el-radio :value="0" :label="0">
                    <div class="flex">
                      <span>全部成员</span>
                      <span class="tips" v-if="formParams.type === 0">将通知有脚本权限的全部成员</span>
                    </div>
                  </el-radio>
                  <el-radio :value="1" :label="1">
                    <div class="flex">
                      <span>指定成员</span>
                      <div class="left" v-if="formParams.type === 1">
                        <el-button
                          :icon="formParams.userResultList.length > 0 ? 'RefreshRight' : 'Plus'"
                          size="small"
                          @click="showChooseUserFn"
                          >{{ formParams.userResultList.length > 0 ? '重选' : '选择' }}</el-button
                        >
                        <span class="tips" v-if="formParams.type === 1 && formParams.userResultList.length > 0"
                          >将通知{{ formParams.userResultList.length }}名成员</span
                        >
                      </div>
                    </div>
                  </el-radio>
                </el-radio-group>
                <span class="tips" style="color: #bbbbbb;">每个脚本每天可发送一次通知，24点刷新。仅开启了小程序消息通知的成员可收到通知。</span>
              </el-form-item>
              <!-- <div class="dept-container" v-if="formParams.type === 1">
                <div class="left"></div>
              </div> -->
            </el-col>
          </el-row>
        </el-form>
        <choose-user v-model="formParams.userResultList" :scriptId="scriptId" v-model:visible="showChooseUser" />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" :loading="btnLoading" :disabled="buttonDisabled" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="notification" lang="ts">
import { scriptNotify } from '@/api/script'
import { ScriptNotifyBo } from "@/api/script/types";
import { ElMessageBox } from 'element-plus'
import ChooseUser from "./chooseUser.vue";
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success'])
const formParamsRef = ref()
const btnLoading = ref(false)
const loading = ref(false)
const showChooseUser = ref(false)
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '发送通知'
  },
  scriptId: {
    type: String,
    default: ''
  },
  scriptName: {
    type: String,
    default: ''
  },

})
const initNotifyForm = {
  type: 0,
  name: '',
  description: '',
  userIdList: [],
  userResultList: [],
  scriptId: ''
}
const formParams = ref<ScriptNotifyBo>({ ...initNotifyForm })
import type { FormRules } from 'element-plus'

const checkAppUserLimit = (rule: any, value: any, callback: any) => {
  if (formParams.value.userResultList.length === 0 && value === 1) {
    callback(new Error('请选择指定用户'))
  } else {
    callback()
  }
}

//确定按钮
const buttonDisabled = computed(() => {
  return !formParams.value.name || !formParams.value.description || (formParams.value.type === 1 && formParams.value.userResultList.length === 0)
})

const rules = reactive<FormRules<ScriptNotifyBo>>({
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    {
      min: 1,
      max: 20,
      message: '最多20个字符',
      trigger: ["blur"]
    }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' },
    {
      min: 1,
      max: 20,
      message: '最多20个字符',
      trigger: ["blur"]
    }
  ],
  type: [
    { required: true, message: '请输入任务描述', trigger: 'blur' },
    {
      min: 1,
      max: 20,
      validator: checkAppUserLimit,
      trigger: ["submit"]
    }
  ],
})

// watch(() => formParams.value.type, val => {
//   if (val === 1 && formParams.value.userResultList.length === 0) {
//     proxy.$refs['formParamsRef'].clearValidate('type')
//   }
// })


// props



// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const showChooseUserFn = () => {
  showChooseUser.value = true
}



const back = () => {
  let bool = false;
  Object.keys(initNotifyForm).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(initNotifyForm[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(initNotifyForm[key])
      ) {
        bool = true;
        return;
      }
    }

  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}


/**
 * 关闭窗口重置字段
 */
const closeDialog = () => {
  loading.value = true
  formParamsRef.value.resetFields()
  formParams.value = { ...initNotifyForm }
  loading.value = false
  visibleAdd.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      btnLoading.value = true
      formParams.value.userIdList = formParams.value.type === 0 ? [] : formParams.value.userResultList.map(item => item.id)
      formParams.value.scriptId = props.scriptId
      await scriptNotify(formParams.value).then(() => {
        proxy.$modal.msgSuccess('已发送通知');
        closeDialog()
        emits('success')
      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #666666;
    font-size: 16px;
    margin: 18px 0;
    font-weight: bold;
    margin-left: 12px;
  }
}

.tips {
  color: #999999;
  margin-left: 16px;
}

:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}

.left {
  // width: 100%;
  display: flex;
  align-items: center;
  margin-left: 32px;
}

.divider {
  // height: 100%;
  // width: 1px;
  // background-color: var(--el-border-color);
}
</style>
