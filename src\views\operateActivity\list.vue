<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">闯关</span>
          <div class="search-container">
            <el-button type="primary" icon="Plus" v-if="userId !== 1" @click="addLevelGroup">创建闯关</el-button>
            <el-button type="primary" icon="Plus" v-else-if="dynamicFlag === '1' && userId === 1" @click="addLevelGroup">创建闯关</el-button>
          </div>
        </div>
      </template>
      <div class="header">
        <el-tabs v-model="queryParams.status" class="demo-tabs" @tab-click="switchTab">
          <el-tab-pane label="已上线" :name="2"></el-tab-pane>
          <el-tab-pane label="待上线" :name="1"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="level-container">
        <div
          class="list-wrapper"
          ref="scrollElem"
          v-if="LevelList.length > 0"
          style="overflow: auto;"
          :infinite-scroll-distance="1"
          v-infinite-scroll="getList"
          :infinite-scroll-disabled="noMore"
        >
          <div class="list">
            <div class="level-item column box" @click="detailLevelGroup(item)" v-for="(item, index) in LevelList" :key="index">
              <div class="flex title_container">
                <div class="title">{{ item.name }}</div>
                <div class="progress" :style="{ backgroundColor: statusMap.get(item.progress) }">
                  <div class="progress_text">{{ progressMap.get(item.progress) }}</div>
                </div>
              </div>

              <div class="num">{{ item.scriptList.length }}个关卡</div>
              <div class="level-detail flex" v-for="(it, i) in item.scriptList.slice(0, 6)" :key="i">
                <div class="dot" :style="{ backgroundColor: colorMap.get(it.status) }"></div>
                <span>{{ it.name }}</span>
              </div>

              <!-- <div class="level-detail flex">
          <div class="dot" :style="{ backgroundColor: '#34E831' }"></div>
          脚本名称1
        </div> -->
              <div class="level-detail flex" v-if="item.scriptList.length > 6">
                <!-- <div class="dot" :style="{ backgroundColor: '#34E831' }"></div> -->
                ……
              </div>
              <!-- <div class="level-detail"></div>
              <div class="level-detail"></div>
              <div class="level-detail"></div> -->
              <div class="bottom flex">
                <div class="left flex" style="color: #999999;">{{ item.startDate + '-' + item.endDate }}</div>
                <div class="right flex">
                  <span @click.stop>
                    <el-popover
                      trigger="click"
                      placement="top"
                      :ref="(el: refItem) => handleSetPopMap(el, item.id)"
                      :width="300"
                      popper-class="popover"
                    >
                      <div style="display: flex;flex-direction: column;gap:8px 0;">
                        <span class="title">{{ item.status === 1 ? '确定上线吗？' : '确定下线吗？' }}</span>
                        <span class="message">{{ item.status === 1 ? '上线后，活动时间内学员可以进行闯关。' :
                          '下线后，用户将无法参与该闯关活动。' }}</span>
                      </div>
                      <div style="text-align: right; margin: 0;margin-top: 30px;">
                        <el-button @click="cancelRemove(item.id)">取消</el-button>
                        <el-button type="primary" @click.stop="confirm(item)">确定</el-button>
                      </div>
                      <template #reference>
                        <div v-if="item.status === 2">
                          <el-tooltip content="下线" placement="top">
                            <img src="@/assets/icons/png/online.png" class="icon" />
                          </el-tooltip>
                        </div>
                        <div v-else-if="item.status === 1">
                          <el-tooltip content="上线" placement="top">
                            <img src="@/assets/icons/png/downline.png" class="icon" />
                          </el-tooltip>
                        </div>
                      </template>
                    </el-popover>
                  </span>
                  <div @click.stop="editLevelGroup(item)">
                    <el-tooltip content="编辑" placement="top">
                      <img src="@/assets/icons/png/edit.png" class="icon" />
                    </el-tooltip>
                  </div>
                  <span @click.stop>
                    <el-popover
                      trigger="click"
                      placement="top"
                      :ref="(el: refItem) => handleSetDeletePopMap(el, item.id)"
                      :width="300"
                      popper-class="popover"
                    >
                      <div style="display: flex;flex-direction: column;gap:8px 0;">
                        <span class="title">确定要删除该闯关吗？</span>
                        <span class="message">该操作无法恢复，请谨慎操作。将同步删除该闯关的数据。</span>
                      </div>
                      <div style="text-align: right; margin: 0;margin-top: 30px;">
                        <el-button @click="cancelDelete(item.id)">取消</el-button>
                        <el-button type="primary" @click.stop="confirmDelete(item)">确定</el-button>
                      </div>
                      <template #reference>
                        <div v-show="item.status === 1">
                          <el-tooltip content="删除" placement="top">
                            <img src="@/assets/icons/png/del.png" class="icon" />
                          </el-tooltip>
                        </div>
                      </template>
                    </el-popover>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <!-- <p class="nomore" v-if="noMore">已加载全部内容</p> -->
        </div>
        <div class="empty flex" v-else>
          <el-image :src="emptyEman"></el-image>
          <span>暂无闯关</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// import router from '@/router';
import type { TabsPaneContext } from 'element-plus'
import { ComponentPublicInstance } from "vue";
import { getLevelList, onlineLevelScriptGroup, offlineLevelScriptGroup, getOfflineScript, deleteLevel } from '@/api/eventLevel';
import { LevelVo } from '@/api/script/types';
import { ElLoading } from 'element-plus'
import emptyEman from "@/assets/icons/png/empty-script.png";
import useUserStore from '@/store/modules/user';
const LevelList = ref<LevelVo[]>([]);
const loading = ref(true);
const total = ref(1);
const noMore = ref(false)
const requestLoading = ref(false)
type refItem = Element | ComponentPublicInstance | null;
const onOrOffLinePopverRefMap = ref({});
const deletePopverRefMap = ref({});
const dynamicFlag = ref('1')
const userStore = useUserStore();
const userId = ref(userStore.userId);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type']);
const props = defineProps({
  listType: {
    type: Number,
    default: null
  }
})

const colorMap = new Map([
  [2, '#5EEE3A'],
  [1, '#D9D9D9'],
])
const statusMap = new Map([
  [0, '#4F66FF'],
  [1, '#FF8336'],
  [2, '#CCCCCC'],
])
const progressMap = new Map([
  [0, '进行中'],
  [1, '待开始'],
  [2, '已结束']
])

const data = reactive<PageQueryData>({
  queryParams: {
    pageNum: 0,
    pageSize: 30,
    status: props.listType as unknown as number || 2,
  }
});

const { queryParams } = toRefs(data);

/**切换tab状态 */
const switchTab = (tab: TabsPaneContext) => {
  // if (tab.props.name as number === 4) return
  queryParams.value.status = tab.props.name as number
  initList();
}




const handleSetPopMap = (el: refItem, item: string) => {
  if (el) {
    onOrOffLinePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelRemove = (id) => {
  onOrOffLinePopverRefMap.value[`Pop_Ref_${id}`].hide()
}


const handleSetDeletePopMap = (el: refItem, item: string) => {
  if (el) {
    deletePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelDelete = (id) => {
  deletePopverRefMap.value[`Pop_Ref_${id}`].hide()
}




const initList = () => {
  queryParams.value.pageNum = 0
  total.value = 1
  // noMore.value = false
  requestLoading.value = false
  LevelList.value = []
  getList()
}




/** 查询列表 */
const getList = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    target: document.querySelector('.level-container') // 设置加载动画区域
  })
  if (requestLoading.value) return;
  if (LevelList.value.length < total.value) {
    requestLoading.value = true;
    queryParams.value.pageNum += 1;
    const res = await getLevelList(queryParams.value);
    total.value = res.total
    LevelList.value = [...LevelList.value, ...res.rows];
    if (LevelList.value.length === res.total) {
      noMore.value = true;
    }
    requestLoading.value = false;
  }
  loading.close()
}


const addLevelGroup = () => {
  emits('change-type', { flagType: 'add-level-group', listType: queryParams.value.status })
}

const detailLevelGroup = (item: any) => {
  emits('change-type', { flagType: 'edit-level-group', isEdit: false, id: item.id, listType: queryParams.value.status })
}


const editLevelGroup = (item: any) => {
  emits('change-type', { flagType: 'edit-level-group', isEdit: true, id: item.id, listType: queryParams.value.status })
}

const confirm = async (item: any) => {
  onOrOffLine(item)
}


const onOrOffLine = async (item: LevelVo) => {
  if (item.status === 1) {
    const res = await getOfflineScript({ levelId: item.id })
    if (res.data.length > 0) {
      let text = '可能会导致后续关卡无法完成。'
      res.data.forEach((element: string) => {
        text += `<li>${element}</li>`
      });
      ElMessageBox.confirm(
        text,
        '以下脚本未上线，确定上线闯关吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          showClose: false,
          type: 'none',
        }
      )
        .then(async () => {
          loading.value = true;
          await onlineLevelScriptGroup({ id: item.id })
          proxy.$modal.msgSuccess('上线成功');
          initList()

        })
        .catch(() => {
        })

    } else {
      await onlineLevelScriptGroup({ id: item.id })
      proxy.$modal.msgSuccess('上线成功');
      initList()
    }
  } else {
    await offlineLevelScriptGroup({ id: item.id })
    proxy.$modal.msgSuccess('下线成功');
    initList()
  }
}

onMounted(() => {
  dynamicFlag.value = localStorage.getItem('dynamicFlag') as string
  getList()
})


/**删除 */
const confirmDelete = async (item) => {
  await deleteLevel(item.id)
  proxy.$modal.msgSuccess('删除成功');
  initList()
}
// onMounted(() => {
//   getList();
// })
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}


:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 90px - 100px);
  }
}

.flex {
  display: flex;
}

.empty {
  height: calc(100vh - 84px - 110px - 100px);
  flex-direction: column;
  align-items: center;
  color: #999999;
  padding-top: 15vh;

  .el-image {
    width: 5%;
    margin-bottom: 20px;
  }
}

.column {
  flex-direction: column;
}

.level-container {
  width: 100%;
}

.list {
  display: flex;
  margin: 24px 0;
  width: 100%;
  gap: 1vw;
  padding: 0 16px;
  flex-wrap: wrap;

  .box {
    width: 32.4%;
    height: 300px;
    display: flex;
    border-radius: 8px;
    box-shadow: 0 0 10px 1px #dddddd;
    border: 2px solid #fff;
    cursor: pointer;

    &:hover {
      border: 2px solid #4F66FF;
      // background: #fbfcff;

    }
  }

  .add-group {
    align-items: center;
    gap: 20px 0;
    flex-direction: column;
    color: #4F66FF;
    justify-content: center;
    box-shadow: 0 0 10px 1px #dddddd;

    // &:hover {
    //   animation: float 1s 1;
    // }
  }

  .level-item {
    box-sizing: border-box;
    padding: 10px 16px 0 16px;
    position: relative;
  }

  .progress {
    width: 72px;
    height: 30px;
    right: -1px;
    top: -2px;
    font-size: 14px;
    color: #fff;
    text-align: center;
    line-height: 30px;
    border-radius: 8px;
    transform: skew(-8deg);
    &_text {
      transform: skew(8deg);
    }

  }
  .title_container{
    width: 100%;
    justify-content: space-between;
  }
  .title {
    font-size: 18px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .num {
    margin: 8px 0;
    color: #979797;
    font-size: 14px;
  }

  .level-detail {
    align-items: center;
    gap: 0 8px;
    color: #979797;
    font-size: 14px;

    span {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .bottom {
    border-top: 1px solid #dddddd;
    justify-content: space-between;
    position: absolute;
    width: calc(100% - 32px);
    bottom: 0;
    box-sizing: border-box;
    padding: 10px;
    left: 16px;

    .left {
      align-items: center;
      font-size: 14px;
    }

    .right {
      gap: 0 8px;
    }
  }

  .icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }

  .dot {
    width: 9px;
    height: 9px;
    border-radius: 50%;
  }
}

.list-wrapper {
  height: calc(100vh - 84px - 110px - 100px);
  overflow: auto;
}

.nomore {
  text-align: center;
  font-size: 12px;
  color: #999999;
}
</style>
