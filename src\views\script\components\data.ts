export const visitorList = ['执业医师', '执业药师', '技师', '护士', '药店职员', '企业高管', '患者'];
export const visitorCreateList = ['执业医师', '执业药师', '技师', '护士', '药店职员', '患者'];
export const targetList = ['医药代表', '药店职员', '企业职员'];
export const shopLevelList = ['店长', '普通店员'];
export const levelExcutiveList = ['营销负责人'];
export const issueTags = [
  '适应症',
  '作用',
  '安全性',
  '副作用',
  '禁忌症',
  '适用人群',
  '特殊人群用药',
  '作用机制',
  '相互作用',
  '主要成分',
  '用法用量',
  '临床数据',
  '临床反馈',
  '药品优势',
  '价格'
];
export const locationTags = [
  '医院',
  '门诊',
  '急诊',
  '会诊室',
  '住院部',
  '药店',
  '药房',
  '诊所',
  '办公室',
  '会议室',
  '咖啡厅',
  '实验室',
  '康复中心',
  '卫生站',
  '护理院'
];

export const visitStageList = ['首次拜访', '多次拜访'];

export function convertArrayToObject(selectedItems: string[]) {
  const newObj: any = {};
  selectedItems.forEach((item) => {
    newObj[item] = undefined;
  });
  return newObj;
}
