export interface ProfessionalCertificationQuery extends PageQuery {
  keyword: string;
  status: number | string;
}
/**职业列表返回 */
export interface ProfessionalCertificationListResult {
  id: number | string | undefined;
  username: string;
  auditTime: string;
  status: string | number;
  submitTime: string;
}
/**职业详情 */
export interface ProfessionalCertificationVo {
  id: number | string | undefined;
  name: string;
  department: string;
  title: string;
  status: number | string;
  userId: number | string;
  userName: string;
  phoneNumber: string;
  registerTime: string;
  organization: string;
  attachmentList: any;
}
/**审核明细返回 */
export interface AuditVo {
  id: number | string | undefined;
  operatorName: string;
  operateTime: string;
  operateName: string;
  remark: string;
}
/**审核明细请求 */
export interface AuditQueryVo extends PageQuery {
  mainId: string;
}
/**审核 */
export interface AuditFormVo {
  id: string;
  status: number | string;
  remark: string;
}
