<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back" class="back-icon" />
            编辑提示词</span
          >
          <div class="right-button">
            <el-button plain type="info" @click="back"> 取消</el-button>
            <el-button type="primary" @click="handleSubmit" v-loading="btnLoading">保存</el-button>
          </div>
        </div>
      </template>
      <div class="box-container">
        <el-form ref="formParamsRef" :model="form" :rules="rules" label-position="left">
          <div class="type-title">{{ promptTemplateObj.name }}</div>
          <div class="box-title">提示词</div>
          <div class="promptTem-content">
            <div class="right">
              <el-form-item prop="prompt" style="width: 100%;height:calc( 100% - 20px)">
                <el-input
                  type="textarea"
                  class="right"
                  resize="none"
                  v-model="form.prompt"
                  placeholder="请输入"
                  style="width: 100%; height: 100%;white-space: pre-wrap;"
                ></el-input>
              </el-form-item>
            </div>
            <div class="mid">
              <el-form-item
                prop="tenantId"
                style="width: 100%"
                v-if="[1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20].includes(promptTemplateObj.type)"
              >
                <el-select v-model="form.tenantId" placeholder="请选择租户">
                  <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="emanId" style="width: 100%" v-if="form.tenantId">
                <el-select placeholder="请选择E人" v-model="form.emanId">
                  <el-option v-for="it in emanList" :key="it.id" :label="it.nameText" :value="it.id" />
                </el-select>
              </el-form-item>
              <el-form-item prop="scriptId" style="width: 100%" v-if="form.tenantId">
                <el-select v-model="form.scriptId" placeholder="请选择脚本">
                  <el-option v-for="item in scriptList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="userInput" style="width: 100%" v-if="[5, 6, 7, 19, 21].includes(promptTemplateObj.type)">
                <el-input placeholder="请输入内容" v-model="form.userInput" type="textarea" :autosize="{ minRows: 5 }"></el-input>
              </el-form-item>
              <el-form-item prop="correctAnswer" style="width: 100%" v-if="[19].includes(promptTemplateObj.type)">
                <el-input placeholder="请输入参考文案" v-model="form.correctAnswer" type="textarea" :autosize="{ minRows: 5 }"></el-input>
              </el-form-item>
              <el-button
                type="primary"
                block
                size="large"
                v-loading="previewLoading"
                :disabled="disabledPreview"
                style="width: 100%;"
                @click="_handlePreview"
              >
                调试</el-button
              >
              <div class="bold">调试状态：{{ newPromptFlag === null ? '待调试' : newPromptFlag ? '正常' : '异常' }}</div>
              <div class="error-text" v-if="errorText">{{ errorText }}</div>
            </div>
            <div class="left">{{ newPromptText }}</div>
          </div>
        </el-form>
      </div>
      <div class="box-container">
        <div class="box-title">发布日志</div>
        <el-table :data="promptTemplateHistoryList">
          <el-table-column label="时间" align="left" prop="createTime" width="180px" />
          <el-table-column label="操作人" align="left" prop="createBy" width="180px" />
          <el-table-column label="更新内容" align="left" prop="content">
            <template #default="scope">
              <div style="white-space: pre-wrap;">
                {{ scope.row.content }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="left" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="showPrompt(scope.row.prompt)" text>提示词</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-dialog
        ref="formDialogRef"
        title="历史提示词"
        v-model="visibleFlag"
        width="600px"
        append-to-body
        :close-on-click-modal="false"
        :show-close="false"
      >
        <div class="contentText">{{ contentText }}</div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="visibleFlag = false">关 闭</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="user" lang="ts">
import { getPromptTemplateDetail, getScriptSelectList, getEmanSelectList, handlePreview, handleSavePromptPreview } from '@/api/prompt';
import { PromptTemplateVo, PromptTemplateHistoryVo } from '@/api/prompt/types';
import { ElMessageBox, ElLoading } from 'element-plus'
import { deepClone } from "@/utils/index";
import type { FormRules } from 'element-plus'
import { TenantVO } from '@/api/types';
import { getTenantLoginList } from "@/api/login";
// import { promptTemplateArray } from "./data";
const btnLoading = ref(false)
const formParamsRef = ref()
const visibleFlag = ref(false)
const form = ref({
  tenantId: '',
  emanId: '',
  scriptId: '',
  userInput: '',
  prompt: '',
  correctAnswer: ""
})
const newPromptText = ref<string | null>('')
const newPromptFlag = ref<null | boolean>(null)

const errorText = ref('')
const promptTemplateObj = ref<PromptTemplateVo>({})
const promptTemplateHistoryList = ref<PromptTemplateHistoryVo[]>([])
const tenantList = ref<TenantVO[]>([]);
const emanList = ref<{ id: string; name: string; department: string; title: string; nameText: string }[]>([]);
const scriptList = ref<{ id: string; name: string }[]>([]);
const contentText = ref('')
const oldPrompt = ref()

const emits = defineEmits(['change-type'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const rules = reactive<FormRules<any>>({
  prompt: [
    { required: true, message: '请输入提示词', trigger: 'blur' },
  ],
  tenantId: [
    { required: true, message: '请选择租户', trigger: 'change' },
  ],
  scriptId: [
    { required: true, message: '请选择脚本', trigger: 'change' },
  ],
  emanId: [
    { required: true, message: '请选择E人', trigger: 'change' },
  ],
})
const previewLoading = ref(false)

const showPrompt = (content: string) => {
  contentText.value = content
  visibleFlag.value = true
}

const _handlePreview = () => {
  const data = {
    ...form.value,
    type: promptTemplateObj.value.type
  }
  previewLoading.value = true
  handlePreview(data).then((res) => {
    newPromptText.value = res.data
    newPromptFlag.value = !(res.data === null)
  }).finally(() => {
    previewLoading.value = false
  });



}

const disabledPreview = computed(() => {
  if ([1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20].includes(promptTemplateObj.value.type)) {
    return !(form.value.tenantId && form.value.emanId && form.value.scriptId)
  }
  else if ([5, 6, 7, 21].includes(promptTemplateObj.value.type)) {
    return !form.value.userInput
  } else if ([19].includes(promptTemplateObj.value.type)) {
    return !(form.value.correctAnswer && form.value.userInput)
  } else {
    return false
  }

})


watch(() => form.value.tenantId, (val) => {
  if (val) {
    form.value.emanId = ''
    form.value.scriptId = ''
    initScriptList()
    initEmanList()
  }
})


const back = () => {
  let bool = oldPrompt.value !== form.value.prompt
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list' })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list' })
  }
}



const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  listType: {
    type: Number
  }
})

const _getScriptDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
  })
  const res = await getPromptTemplateDetail(props.id);
  loading.close()
  oldPrompt.value = res.data.promptTemplate.newPrompt ? res.data.promptTemplate.newPrompt : res.data.promptTemplate.prompt || ''
  form.value.prompt = res.data.promptTemplate.newPrompt ? res.data.promptTemplate.newPrompt : res.data.promptTemplate.prompt || ''
  promptTemplateObj.value = res.data.promptTemplate
  promptTemplateHistoryList.value = res.data.promptTemplateHistory || []

  // oldFromInfo.value = deepClone(res.data)
}
/** 租户列表 */
const initTenantList = async () => {
  const { data } = await getTenantLoginList();
  tenantList.value = data;
}

/** E人列表 */
const initEmanList = async () => {
  const { data } = await getEmanSelectList({ tenantId: form.value.tenantId });
  const array = deepClone(data)
  array.forEach(element => {
    if (!element.department && !element.title) {
      element.nameText = `${element.name}(${element.occupation})`
    } else {
      element.nameText = `${element.name}(${element.department || ''.includes('-') && element.department ? element.department.split('-')[0] : element.department || ''}${element.title && element.department ? '|' : ''}${element.title.includes('-') && element.title ? element.title.split('-')[1] : element.title || ''})`
    }
  })
  emanList.value = array;
}


const handleSubmit = async () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const data = {
        ...form.value,
        type: promptTemplateObj.value.type
      }
      btnLoading.value = true

      await handleSavePromptPreview(data).then((res) => {
        proxy.$modal.msgSuccess('保存成功');
        emits('change-type', { flagType: 'list', })

      }).finally(() => {
        btnLoading.value = false;
      });



    } else {
      console.log('error submit!!');
      return false;
    }
  })


}


/** E人列表 */
const initScriptList = async () => {
  const { data } = await getScriptSelectList({ tenantId: form.value.tenantId });
  scriptList.value = data;
}
_getScriptDetail()
initTenantList()
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  // margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// @media screen and (max-width: 1300px) {
//   .box-container {
//     padding: 0 30px 100px 30px;
//   }
// }


// @media screen and (min-width: 1301px) {
//   .box-container {
//     padding: 0 15% 100px 15%;

//   }
// }

.random-num {
  width: 200px;
}

.box-container {
  display: flex;
  flex-direction: column;
  padding: 0 30px 30px 30px;
  gap: 12px 0;
  width: 100%;
  align-items: flex-start;
  margin: 0 auto;
  // height: 100%;
  // overflow-y: auto;



  // padding: 0 30px 100px 30px;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;
  }

  .type-title {
    width: 100%;
    padding: 16px;
    box-sizing: border-box;
    // border: 2px solid #4F66FF;
    background: #f4f6ff;
    border-radius: 4px;
  }

  .promptTem-content {
    width: 100%;
    display: flex;
    height: 100%;

  }

  .mid {
    width: 400px;
    display: flex;
    flex-direction: column;
    gap: 24px 0;
    box-sizing: border-box;
    padding: 0 20px;
    align-items: flex-start;
  }

  .left,
  .right {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    border-radius: 4px;
    // border: 1px solid #4F66FF;
    overflow: auto;
    height: 600px;
    white-space: pre-wrap;
    box-sizing: border-box;
    padding: 12px 16px;

  }

  .left {
    background: #f4f6ff;

    // height: 600px;
  }

  .right {
    padding: 0;
    background-color: #ffffff;
    height: 620px;

    :deep(.el-textarea__inner) {
      width: 100%;
      height: 100%;
      border: none;
    }
  }
}

.script-msg {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  .left {
    width: 90px;
    font-weight: 500;
    margin-right: 16px;
  }

  .right {
    flex: 1;
  }
}


:deep(.el-card) {
  // height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 20px - 162px);
    overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-radio) {
  height: auto;
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qa {
  .el-form-item--default {
    // margin-bottom: 16px;
  }
}

.bold {
  font-weight: bold;
  font-size: 18px;
}


.delete {
  cursor: pointer;
}

.tips {
  color: #999999;
  margin-top: 4px;
  font-size: 14px;
}

.contentText {
  white-space: pre-wrap;
}

:deep(.el-form-item--default) {
  margin-bottom: 0;
}
</style>
