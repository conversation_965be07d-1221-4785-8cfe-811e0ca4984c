<template>
  <div class="p-2">
    <el-dialog
      ref="formDialogRef"
      :title="`编辑标注信息（${currentIndex + 1}/${currentImageArray.length}）`"
      v-model="visibleDialog"
      width="750px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
      :before-close="closeDialog"
    >
      <div class="box-container">
        <el-carousel arrow="never" :autoplay="false" @change="changeCarousel" ref="carouselRef" height="300" indicator-position="none">
          <el-carousel-item v-for="(item, index) in formData" :key="index">
            <div class="image-container">
              <el-image
                style="width:50%;min-height: 50px;"
                :src="item.image"
                fit="contain"
                :preview-teleported="true"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[item.image]"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><icon-picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
          </el-carousel-item>
        </el-carousel>
        <div class="arrow-container">
          <div class="left arrow" :class="aiCreateFlag ? 'disabled' : ''" @click="changeImg('prev')">
            <el-icon :color="aiCreateFlag ? '#dddddd' : '#4f66ff'" size="30">
              <ArrowLeft />
            </el-icon>
          </div>
          <div class="right arrow" :class="aiCreateFlag ? 'disabled' : ''" @click="changeImg('next')">
            <el-icon :color="aiCreateFlag ? '#dddddd' : '#4f66ff'" size="30">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
        <el-form :model="formData[currentIndex]" ref="formParamsRef">
          <el-form-item
            prop="title"
            :rules="[
            { required: true, message: '请输入图片标题' },
            { min: 1, max: 50, message: '最多输入50个字' },
          ]"
          >
            <el-input
              v-model="formData[currentIndex].title"
              placeholder="图片标题"
              type="text"
              maxlength="50"
              show-word-limit
              :disabled="[5, 1].includes(formData[currentIndex].imageStatus)"
            />
          </el-form-item>
          <el-form-item
            prop="content"
            :rules="[
            { min: 1, max: 1500, message: '最多输入1500个字' },
          ]"
          >
            <div class="backdrop-text">
              <el-input
                type="textarea"
                :disabled="[5, 1].includes(formData[currentIndex].imageStatus) || aiCreateFlag"
                v-model="formData[currentIndex].content"
                placeholder="请尽可能详尽地描述图片中的内容"
                id="textarea_id"
              />
              <span class="ai" @click="aiCreateFlag ? stopLabel() : confirmLabel()">{{ aiCreateFlag ? '停止标注' : 'AI标注'
              }}</span>
              <span class="tips">{{ formData[currentIndex].content.length + '/' + 1500 }}</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :disabled="aiCreateFlag" :loading="btnLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- AI标注选择 -->
    <el-dialog v-model="showBatchAiLabel" :close-on-click-modal="false" :show-close="false" width="400px" append-to-body>
      <template #title>
        <div style="font-size: 16px;margin-top: 12px;">请选择AI标注内容的合并方式</div>
      </template>
      <el-radio-group v-model="currentMergeMethod" style="display: flex;">
        <el-radio :value="1" :label="1">
          <span>添加在末尾，与现有标注内容合并</span>
        </el-radio>
        <el-radio :value="2" :label="2">
          <span>替换原内容，替换现有标注内容</span>
        </el-radio>
      </el-radio-group>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeLabelDialog">取 消</el-button>
          <el-button type="primary" :loading="labelLoading" @click="handleBatchAiLabelOrVoice">开始标注</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { getItemDetail, queryKnowledgeBaseItemList, manualLabel, autoLabel } from '@/api/knowledgeBase';
import { KnowledgeBaseItemVo } from '@/api/knowledgeBase/types';
import { ElLoading } from 'element-plus'
import { globalHeaders } from "@/utils/request";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { deepClone } from '@/utils';
const labelLoading = ref(false)


const showBatchAiLabel = ref(false)
const formParamsRef = ref()
const emits = defineEmits(['update:visible', 'success'])
const carouselRef = ref()
const currentIndex = ref(0)
const currentId = ref('')
const btnLoading = ref(false)
const aiCreateFlag = ref(false)
const currentImageArray = ref<KnowledgeBaseItemVo[]>([])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const getDetailArray = ref<string[]>([])
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const headers = ref(globalHeaders());
const ctrl = new AbortController(); // 用于中断请求
const scrollDiv = ref()
const currentMergeMethod = ref(1)
// import axios from 'axios';
// const currentImgInfo = ref<KnowledgeBaseItemVo>({} as KnowledgeBaseItemVo)

interface formDataType {
  image: string;
  title: string;
  content: string;
  imageStatus: number;
  id: string;
}

const formData = ref<formDataType[]>([{
  image: '',
  title: '',
  content: '',
  imageStatus: 0,
  id: ''
}])
const oldFormData = ref<formDataType[]>([])
// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  kbId: {
    type: String,
    default: ''
  },
  activeId: {
    type: String,
    default: ''
  },
  orderByColumn: {
    type: String,
    default: 'name'
  },
  isAsc: {
    type: String,
    default: 'desc'
  },
})

watch(() => props.visible, val => {
  if (val) {
    nextTick(() => {
      currentId.value = props.activeId
      init()
    })

  }
})

// 初始化 获取知识库内容列表
const init = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    target: document.querySelector('.box-container') // 设置加载动画区域
  })
  const res = await queryKnowledgeBaseItemList({
    kbId: props.kbId,
    orderByColumn: props.orderByColumn,
    isAsc: props.isAsc,
  })

  currentImageArray.value = res.rows
  formData.value = res.rows.map(item => {
    return {
      image: item.url,
      title: item?.labelInfo?.title || item.name,
      id: item.id,
      imageStatus: 0,
      content: '',
    }
  })
  oldFormData.value = deepClone(formData.value)
  currentIndex.value = currentImageArray.value.findIndex(item => item.id === currentId.value)
  // console.log(currentIndex.value)
  loading.close()
  nextTick(() => {
    if (currentIndex.value === 0) {
      _getItemDetail(currentId.value)
    } else {
      carouselRef.value.setActiveItem(currentIndex.value)
    }
  })
  // return

}

const changeImg = (type: string) => {
  if (aiCreateFlag.value) {
    return
  }
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      if (type === 'prev') {
        carouselRef.value.prev()
      } else if (type === 'next') {
        carouselRef.value.next()
      }
    }
  })
}

const changeCarousel = (current: number, prev: number) => {
  currentIndex.value = current
  _getItemDetail(currentImageArray.value[currentIndex.value].id)

}
// 获取知识库内容详情
const _getItemDetail = async (id: string) => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    target: document.querySelector('.box-container') // 设置加载动画区域
  })
  const res = await getItemDetail(id)
  if (!getDetailArray.value.includes(id)) {
    getDetailArray.value.push(id)
  }
  //存老数据
  oldFormData.value[currentIndex.value].title = oldFormData.value[currentIndex.value].title === '' ? (res.data.labelInfo?.title || res.data.name) : oldFormData.value[currentIndex.value].title
  oldFormData.value[currentIndex.value].content = oldFormData.value[currentIndex.value].content === '' ? (res.data.labelInfo?.content || '') : oldFormData.value[currentIndex.value].content
  oldFormData.value[currentIndex.value].imageStatus = res.data.status


  formData.value[currentIndex.value].title = formData.value[currentIndex.value].title === '' ? (res.data.labelInfo?.title || res.data.name) : formData.value[currentIndex.value].title
  formData.value[currentIndex.value].content = formData.value[currentIndex.value].content === '' ? (res.data.labelInfo?.content || '') : formData.value[currentIndex.value].content
  formData.value[currentIndex.value].imageStatus = res.data.status
  loading.close()
}

// 弹窗组件显示隐藏
const visibleDialog = computed({
  get() {
    return props.visible
  },
  /*************  ✨ Codeium Command ⭐  *************/
  /**
   * set visibleDialog to update parent component's visible state
   * @param {boolean} value - visible state
   */
  /******  01d0b5eb-a86c-4c2f-9674-15424efecf1f  *******/
  set(value) {
    emits('update:visible', value)
  }
})
// 终止标注
const stopLabel = () => {
  aiCreateFlag.value = false
  ctrl.abort();
}

const closeLabelDialog = () => {
  showBatchAiLabel.value = false
  currentMergeMethod.value = 1
}

const handleBatchAiLabelOrVoice = () => {
  showBatchAiLabel.value = false //隐藏弹框
  createAiLabel()
}

const confirmLabel = () => {
  if (formData.value[currentIndex.value].content !== '') {
    showBatchAiLabel.value = true
  } else {
    createAiLabel()
  }

}


// Ai生成标注描述
const createAiLabel = async () => {
  if (currentMergeMethod.value === 2) {
    formData.value[currentIndex.value].content = ''
  }
  let createResult = formData.value[currentIndex.value].content
  const requestOptions = {
    method: "POST", // 请求方法，SSE 通常是 GET 请求。如果涉及到双向通信，需要改为 POST。
    headers: {
      "Content-Type": "text/event-stream", // 设置内容类型为 SSE，即
      ...headers.value
    },
    signal: ctrl.signal,
    // 可以添加其他需要的配置：
    // 如果为 POST 请求，需要携带 body 参数以传递请求体；
    // 如果希望用户切换到另一个页面后仍能保持 SSE 连接，可以配置 openWhenHidden 属性为 true；
    // 如果需要使用 AbortController 来实现检测到问题终止连接的，可以配置 signal 属性等。
  };
  fetchEventSource(baseUrl + '/biz/KnowledgeBase/autoLabel/' + formData.value[currentIndex.value].id, {
    ...requestOptions,
    onopen(response) {
      console.log("Connection opened!", response);
      aiCreateFlag.value = true
    },
    onmessage(event) {
      if (event.data !== '') {
        const data = JSON.parse(event.data)
        // console.log(data.content)
        if (aiCreateFlag.value) {
          createResult += data.content
          setResult(createResult)
        }
      }
    },
    onerror(error) {
      console.log(error)
      aiCreateFlag.value = false
      ctrl.abort();
      throw error
    },
    onclose() {
      aiCreateFlag.value = false
      console.log("Connection closed!");
    },
  });
}

const setResult = (result: string) => {
  formData.value[currentIndex.value].content = result
  nextTick(() => {
    setTimeout(() => {
      const textarea = document.getElementById('textarea_id');
      textarea!.scrollTop = textarea!.scrollHeight;
    }, 100);
  });
}


const closeDialog = () => {
  console.log(JSON.stringify(oldFormData.value))
  console.log(123)
  console.log(JSON.stringify(formData.value))
  if (JSON.stringify(oldFormData.value) !== JSON.stringify(formData.value)) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要退出吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        carouselRef.value.setActiveItem(0)
        nextTick(() => {
          initDataBeforeClose()
        })
      })
      .catch(() => {
      })
  } else {
    carouselRef.value.setActiveItem(0)
    nextTick(() => {
      initDataBeforeClose()
    })
  }


}

const initDataBeforeClose = () => {
  currentId.value = ''
  getDetailArray.value = []
  labelLoading.value = false
  currentMergeMethod.value = 1
  carouselRef.value.setActiveItem(0)
  nextTick(() => {
    visibleDialog.value = false
  })
}


const handleSubmit = () => {
  // 判断当前是否可以保存
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      console.log(valid);
      // 获取获取详情才可以保存
      console.log(getDetailArray.value)
      const oldArray: any = []
      getDetailArray.value.forEach(item => {
        const findObj = formData.value.find(it => it.id === item)
        oldArray.push(findObj)
      })

      nextTick(() => {
        // 判断是否有标题为空
        const index = oldArray.findIndex((item: any) => item.title === '')
        if (index !== -1) {
          const findInx = formData.value.findIndex(item => item.id === oldArray[index].id)
          carouselRef.value.setActiveItem(findInx)
          proxy.$refs['formParamsRef'].validate()
          return
        }
        btnLoading.value = true
        const array = oldArray.map((item: any) => {
          return {
            kbItemId: item.id,
            title: item.title,
            content: item.content,
          }
        })
        manualLabel(array).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('保存成功');
            emits('success')
            carouselRef.value.setActiveItem(0)
            nextTick(() => {
              initDataBeforeClose()
            })
          }
        }).finally(() => {
          btnLoading.value = false
        })
      })

    }
  })







}
onMounted(() => {

})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  position: relative;

}

.arrow-container {


  // z-index: 0;
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
}

.disabled {
  background-color: #f1f1f1 !important;
  cursor: not-allowed !important;
}

.arrow {
  cursor: pointer;
  background-color: #eaeaf8;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left {
  position: absolute;
  top: 125px;
  left: 0
}

.right {
  position: absolute;
  top: 125px;
  right: 0;
}

.image-container {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.backdrop-text {
  position: relative;
  width: 100%;

  :deep(.el-textarea__inner) {
    padding-bottom: 40px;
    height: 250px;
  }
}

.tips {
  color: #909399;
  // padding: 5px;
  position: absolute;
  border-radius: 4px;
  bottom: 10px;
  right: 10px;
  padding: 4px 8px;
  line-height: 20px;
  background-color: #fff;
}

.ai {
  background-color: rgb(246, 247, 255);
  color: #4F66FF;
  // padding: 5px;
  position: absolute;
  border-radius: 4px;
  bottom: 10px;
  left: 10px;
  padding: 4px 8px;
  line-height: 20px;
  cursor: pointer;
}

:deep(.is-disabled) {
  .el-textarea__inner {
    background-color: #ffffff;
    color: var(--el-text-color-regular);
  }
}
</style>
