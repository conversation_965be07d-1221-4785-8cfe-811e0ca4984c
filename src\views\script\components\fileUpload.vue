<template>
  <div class="upload-file">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      drag
      name="files"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-change="handleChange"
      :on-success="handleUploadSuccess"
      :show-file-list="true"
      :limit="limit"
      :on-remove="handleRemove"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUploadRef"
      :auto-upload="false"
    >
      <!-- 上传按钮 -->
      <!-- <el-button type="primary">选取文件</el-button> -->
      <div class="upload-container">
        <img src="@/assets/icons/png/upload.png" />
        <div class="right">
          <div>将文档拖到此处，或点击上传</div>
          <div class="tips" v-if="showTip">支持PDF、Word文档（docx）、Excel表格（xlsx、xls），最多1个文件，不超过20MB，文件中的图片不支持识别。</div>
        </div>
      </div>
      <!-- <template #file="{ file }">
        <div class="ele-upload-list__item-content">
          <span class="el-icon-document">
            {{ file.status }}
            <el-icon v-if=" file.status=== 'uploading'" size="18" class="rotating-image">
              <Loading />
            </el-icon>
            <el-icon v-if=" file.status=== 'success'" size="18" class="rotating-image">
              <CircleCheck />
            </el-icon>
            {{ file.name }}
          </span>
          <div class="ele-upload-list__item-content-action">
            <el-link :underline="false" @click="handleDelete(file)">
              <el-icon size="18">
                <close />
              </el-icon>
            </el-link>
          </div>
        </div>
      </template> -->
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { listByIds } from "@/api/system/oss";
import { propTypes } from '@/utils/propTypes';
import { globalHeaders } from "@/utils/request";
const currentUploadRequest = ref(null)
const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: propTypes.number.def(1),
  // 大小限制(MB)
  fileSize: propTypes.number.def(20),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def([ "docx", "xls", "xlsx", "pdf"]),
  // 是否显示提示
  isShowTip: propTypes.bool.def(true),
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref<any[]>([]);

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/biz/ai/coze/fileUpload"); // 上传文件服务器地址
const headers = ref(globalHeaders());

const fileList = ref<any[]>([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

const fileUploadRef = ref<ElUploadInstance>();

watch(() => props.modelValue, async val => {
  console.log(val)
  if (val) {
    let temp = 1;
    // 首先将值转为数组
    let list = [];
    if (Array.isArray(val)) {
      list = val;
    } else {
      const res = await listByIds(val as string)
      list = res.data.map((oss) => {
        const data = { name: oss.originalName, url: oss.url, ossId: oss.ossId };
        return data;
      });
    }
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      item = { name: item.name, id: item.id };
      // item.uid = item.uid || new Date().getTime() + temp++;
      return item;
    });
  } else {
    fileList.value = [];
    return [];
  }
}, { deep: true, immediate: true });

// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  // proxy?.$modal.loading("正在上传文件，请稍候...");
  currentUploadRequest.value = file.request
  number.value++;
  return true;
}

// 文件个数超出
const handleExceed = () => {
  console.log(fileList.value)
  proxy?.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

const handleChange = () => {
  console.log(fileList.value)
  // emit("update:modelValue", fileList.value);
}


// 上传失败
const handleUploadError = (file: any) => {
  if (number.value > 0) {
    number.value--;
  }
  fileUploadRef.value?.handleRemove(file);
  emit("update:modelValue", fileList.value);
  proxy?.$modal.msgError("上传文件失败");
}
const handleRemove = () => {
  if (number.value > 0) {
    number.value--;
  }
  emit("update:modelValue", fileList.value);
}
// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  if (res.code === 200) {
    uploadList.value.push({ name: file.name, id: res.data[0] });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy?.$modal.closeLoading();
    proxy?.$modal.msgError(res.msg);
    fileUploadRef.value?.handleRemove(file);
    // uploadedSuccessfully();
    emit("update:modelValue", fileList.value);
  }
}





// 上传结束处理
const uploadedSuccessfully = () => {
  if (number.value > 0 && uploadList.value.length === number.value) {
    console.log(fileList.value)
    fileList.value = fileList.value.filter(f => f.id !== undefined).concat(uploadList.value);
    console.log(fileList.value)
    uploadList.value = [];
    number.value = 0;
    emit("update:modelValue", fileList.value);
    // proxy?.$modal.closeLoading();
  }
}

// 获取文件名称
// const getFileName = (name: string) => {
//     // 如果是url那么取最后的名字 如果不是直接返回
//     if (name.lastIndexOf("/") > -1) {
//         return name.slice(name.lastIndexOf("/") + 1);
//     } else {
//         return name;
//     }
// }

// 对象转成指定字符串分隔
// const listToString = (list: any[], separator?: string) => {
//     let strs = "";
//     separator = separator || ",";
//     list.forEach(item => {
//         if (item.ossId) {
//             strs += item.ossId + separator;
//         }
//     })
//     return strs != "" ? strs.substring(0, strs.length - 1) : "";
// }
</script>

<style scoped lang="scss">
.upload-file {
  width: 100%;
}

.rotating-image {
  animation: rotate 2s linear infinite;
}

:deep(.el-upload) {
  width: 100%;
  // padding: 48px 24px;
  // border: 1px solid var(--el-border-color);
  // background: transparent;
}

.upload-file-uploader {
  margin-bottom: 5px;
}

.el-upload-list__item {
  // border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
  padding: 8px;
}

.el-icon-document {
  display: flex;
  align-items: center;
  gap: 0 12px;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.upload-container {
  margin: 0 auto;
  display: flex;
  gap: 0 16px;
  width: 100%;

  border-radius: 4px;

  img {
    width: 48px;
    height: 48px;
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;

    .tips {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
      color: #999999;
    }

  }
}
</style>
