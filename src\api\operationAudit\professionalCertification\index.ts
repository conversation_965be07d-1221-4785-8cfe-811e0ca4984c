import request from '@/utils/request';
import {
  ProfessionalCertificationQuery,
  ProfessionalCertificationListResult,
  ProfessionalCertificationVo,
  AuditQueryVo,
  AuditVo,
  AuditFormVo
} from './types';
import { AxiosPromise } from 'axios';

// 查询列表
export function listProfessionalCertification(query: ProfessionalCertificationQuery): AxiosPromise<ProfessionalCertificationListResult[]> {
  return request({
    url: '/biz/professionalCertification/list',
    method: 'get',
    params: query
  });
}

export function getAuditDetail(query: { id: string }): AxiosPromise<ProfessionalCertificationVo> {
  return request({
    url: '/biz/professionalCertification/auditDetail',
    method: 'get',
    params: query
  });
}

export function getAuditRecordList(query: AuditQueryVo): AxiosPromise<AuditVo[]> {
  return request({
    url: '/biz/AuditRecord/list',
    method: 'get',
    params: query
  });
}

export function sameNameCheck(query: { id: string }): AxiosPromise<string[]> {
  return request({
    url: '/biz/professionalCertification/sameNameCheck',
    method: 'get',
    params: query
  });
}

export function audit(data: AuditFormVo): AxiosPromise<any> {
  return request({
    url: '/biz/professionalCertification/audit',
    method: 'post',
    data
  });
}
