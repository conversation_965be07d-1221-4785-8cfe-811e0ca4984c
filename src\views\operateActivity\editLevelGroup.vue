<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex" v-if="editFlag">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            编辑闯关</span
          >
          <span class="card-title flex" v-else>
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            闯关详情</span
          >
          <div class="right-button" v-if="editFlag">
            <el-button plain type="info" @click="props.isEdit ? back() : checkSave()">取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
          <div class="right-button" v-else>
            <el-button type="primary" plain @click="emits('change-type', { flagType: 'list', id: '', listType: listType })">返回</el-button>

            <el-button type="primary" @click="editFlag = true">编辑</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" ref="scrollDiv" v-if="editFlag">
        <el-form ref="formParamsRef" v-loading="formLoading" :model="formParams" :rules="rules" label-width="90px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="9">
              <el-form-item label="活动时间" prop="timeArray">
                <el-date-picker
                  v-model="formParams.timeArray"
                  type="daterange"
                  range-separator="-"
                  ::clearable="false"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="margin-right: 12px;width: 280px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="参与部门" prop="deptIds">
                <div class="flex" style="align-items: flex-start;">
                  <el-button @click="showChooseDeptFn">选择</el-button>
                  <div class="wrapper">
                    <input id="exp1" class="exp" type="checkbox" />
                    <div class="text">
                      <label class="btn" for="exp1"></label>
                      {{ deptOptions.length > 0 && deptOptions[0].label ===
                        formParams.deptNames[0] ?
                        formParams.deptNames[0] : formParams.deptNames.join("、") }}
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />
          <div class="qa" v-for="(it, i) in formParams.levelStages" :key="i">
            <el-row :gutter="24" class="flex" style="display: flex;align-items: center;" v-for="(item, index) in it.detail" :key="index">
              <el-col :span="10">
                <el-form-item :label="`关卡${i + 1}`" :prop="'levelStages.' + i + '.detail.' + index + '.scriptId'" :rules="setRules.scriptId">
                  <el-select v-model="item.scriptId" placeholder="请选择" @change="setScriptId(item)">
                    <template #label="{ label }">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(Number(label.slice(0, 1))) }"
                        ></div>
                        <span>{{ label.slice(1, label.length) }}</span>
                      </div>
                    </template>
                    <el-option v-for="it in scriptOptions" :key="it.id" :label="it.status + it.name" :value="it.id">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(it.status) }"
                        ></div>
                        {{ it.name }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :label="`通关条件`" :prop="'levelStages.' + i + '.detail.' + index + '.conditionText'" :rules="setRules.conditionText">
                  <div class="condition">
                    <span v-if="item.conditionText === ''">待设置</span>
                    <span v-else>{{ item.conditionText }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="`开启时间`" :prop="'levelStages.' + i + '.openTime'" :rules="setRules.openTime">
                  <div class="condition">
                    <span v-if="it.openTime === ''">待设置</span>
                    <span v-else> {{ it.openTime ? Dayjs(it.openTime).format('YYYY.MM.DD HH:mm') : '-' }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <!-- <el-icon color="#dddddd" v-if="index === 0 && formParams.qaList.length === 1">
                <Delete />
              </el-icon> -->
              <div class="delete">
                <el-tooltip content="设置条件" placement="top">
                  <img src="@/assets/icons/png/setting.png" @click="handleAddCondition(i, index)" />
                </el-tooltip>
                <el-popover
                  trigger="click"
                  v-if="formParams.levelStages.length > 1"
                  placement="top"
                  :ref="(el: refItem) => handleSetPopMap(el, it.popId)"
                  :width="300"
                  popper-class="popover"
                >
                  <div style="display: flex;flex-direction: column;gap:8px 0;">
                    <span class="title" style="font-size: 16px;">确定删除此关卡吗？</span>
                    <!-- <span class="message">删除后此关卡的脚本无需解锁即可使用，脚本解锁状态将重置。</span> -->
                  </div>
                  <div style="text-align: right; margin: 0;margin-top: 30px;">
                    <el-button @click="cancelRemove(it.popId)">取消</el-button>
                    <el-button type="primary" @click="deleteItem(it.popId, item, i)">确定</el-button>
                  </div>
                  <template #reference>
                    <el-icon>
                      <img src="@/assets/icons/png/delete.png" @click="showDelete(it.popId, item, i)" />
                    </el-icon>
                  </template>
                </el-popover>
              </div>
            </el-row>
          </div>
          <el-button type="primary" icon="Plus" @click="handleAdd">添加关卡</el-button>
        </el-form>
      </div>
      <div class="box-container" v-else>
        <div class="script-msg">
          <div class="left">名称</div>
          <div class="right">
            <span>{{ oldFromInfo.name }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">参与部门</div>
          <div class="right">
            <div class="wrapper">
              <input id="exp1" class="exp" type="checkbox" />
              <div class="text" style="color: #303133 !important;font-size: 16px;">
                <label class="btn" for="exp1" style="line-height: 24px;"></label>
                {{ oldFromInfo.deptNames && oldFromInfo.deptNames.length > 0 && deptOptions.length > 0 ?
                (deptOptions[0].label
                  ===
                  oldFromInfo.deptNames[0] ? oldFromInfo.deptNames[0] : oldFromInfo.deptNames.join("、")) : ''
                }}
              </div>
            </div>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">活动时间</div>
          <div class="right">
            <span
              >{{ Dayjs(oldFromInfo.startDate).format('YYYY年MM月DD日') }}-{{
              Dayjs(oldFromInfo.endDate).format('YYYY年MM月DD日')
              }}</span
            >
          </div>
        </div>
        <el-divider />
        <div class="qa" v-for="(it, i) in oldFromInfo.levelStages" :key="i">
          <el-row :gutter="24" v-for="(item, index) in it.detail" :key="index">
            <el-col :span="10">
              <div class="script-msg">
                <div class="left">关卡{{ it.orderNum }}</div>
                <div class="right">
                  <div
                    class="dot"
                    style="width: 11px; height: 11px;border-radius: 50%;"
                    :style="{ backgroundColor: colorMap.get(item.scripStatus) }"
                  ></div>
                  <span style="margin-left: 6px;"> {{ item.scriptName }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="script-msg">
                <div class="left">通关条件</div>
                <div class="right" style="color:#aaaaaa;font-size: 14px;">
                  <span> {{ item.conditionText }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="script-msg">
                <div class="left">开启时间</div>
                <div class="right" style="color:#aaaaaa;font-size: 14px;">
                  <span> {{ it.openTime ? Dayjs(it.openTime).format('YYYY.MM.DD HH:mm') : '-' }}</span>
                </div>
              </div>
            </el-col>
            <!-- <el-icon color="#dddddd" v-if="index === 0 && formParams.qaList.length === 1">
                <Delete />
              </el-icon> -->
          </el-row>
        </div>
      </div>

      <SetCondition
        :script-id="currentScriptId"
        :current-condition="currentConditionArray"
        :current-open-time="currentOpenTime"
        @success="setConditionFn"
        v-model:visible="showLevelDialog"
        title="通关设置"
      />
      <ChooseDept
        :parent-dept-options="deptOptions"
        v-model:deptIds="formParams.deptIds"
        v-model:deptNames="formParams.deptNames"
        v-model:visible="showChooseDept"
        title="参与部门"
      />
    </el-card>
  </div>
</template>

<script setup name="AddLevelGroup" lang="ts">
import SetCondition from "./components/setCondition.vue";
import ChooseDept from "./components/chooseDept.vue";
import { TreeLong } from "@/api/department/types";
import { reactive, ref } from 'vue'
import { ElMessageBox, ElLoading } from 'element-plus'
import type { FormRules } from 'element-plus'
import { LevelFormBo, ScriptDetailVo, LevelStageUnlockConditionBo, LevelStageBo } from "@/api/eventLevel/types";
import { getLevelScriptList, updateLevelScriptGroup, getLevelDetail } from "@/api/eventLevel";
import { deepClone } from "@/utils/index"
import { getDeptTreeList } from "@/api/department/index"
type refItem = Element | ComponentPublicInstance | null;
// import { FormValidators } from '@/utils/validate.js'
import { } from "@/utils/index"
import Dayjs from "dayjs";
const scrollDiv = ref()
const deptOptions = ref<TreeLong[]>([])
const detailDom = ref()
const deletePopverRefMap = ref({});
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
const props = defineProps({
  listType: {
    type: Number
  },
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
})
const editFlag = ref(props.isEdit)
const formLoading = ref<boolean>(false)
const showChooseDept = ref(false)
const visible = ref(false)
const initForm = {
  name: '',
  timeArray: [],
  startDate: '',
  endDate: '',
  deptIds: [],
  deptNames: [],
  levelStages: [
    {
      index: 0,
      openTime: '',
      detail: [
        {
          scriptId: '',
          conditionText: '', // 拼接condition
          condition: [{
            scriptId: '',
            scriptName: '',
            time: null,
            score: null
          }],
        }
      ]
    }
  ]
} as unknown as LevelFormBo
const oldFromInfo = ref<LevelFormBo>({})
const formParams = ref(deepClone(initForm))
const scriptOptions = ref<ScriptDetailVo[]>([])
const btnLoading = ref(false)
const showLevelDialog = ref(false)
const currentIndex = ref<number | null>(null)
const currentParentIndex = ref<number | null>(null)
const currentScriptId = ref<string>('')
const currentOpenTime = ref<string>('')
const currentConditionArray = ref<LevelStageUnlockConditionBo[]>([])
const colorMap = new Map([
  [2, '#5EEE3A'],
  [1, '#D9D9D9'],
])

// const checkSameScriptLimit = async (rule: any, value: any, callback: any) => {
//   // formLoading.value = true
//   const res = await scriptLevelDuplicateCheck({ scriptId: value, levelId: props.id })
//   // formLoading.value = false
//   if (res.data) {
//     callback(new Error('关卡脚本之间不能重复，且不能与其它闯关组重复'))
//   } else {
//     const questionArray = formParams.value.levelStages?.filter(item => item.scriptId === value) || []
//     if (questionArray.length > 1) {
//       callback(new Error('关卡脚本之间不能重复，且不能与其它闯关组重复'))
//     } else {
//       callback()
//     }
//   }

// }

const rules = reactive<FormRules<LevelFormBo>>({
  name: [
    { required: true, message: '请输入闯关名称', trigger: 'blur' },
    {
      min: 1,
      max: 50,
      message: '最多50个字符',
      trigger: ["blur"]
    }
  ],
  timeArray: [
    { required: true, message: '请选择活动时间', trigger: 'change' },
  ],
  deptIds: [
    { required: true, message: '请选择参与部门', trigger: 'change' },
  ]
})


//监听日期选择
watch(
  () => formParams.value.timeArray,
  (val) => {
    if (val && val.length > 0) {
      formParams.value.startDate = val[0];
      formParams.value.endDate = val[1];
    } else {
      formParams.value.startDate = '';
      formParams.value.endDate = '';
    }
  }
);

const setScriptId = (item: any) => {
  item.condition.forEach((element: any) => {
    element.scriptId = item.scriptId
  });
}


const setRules = reactive({
  openTime: [
    {
      required: true,
      message: '请设置开启时间',
      trigger: ['change']
    }
  ],
  scriptId: [
    {
      required: true,
      message: '请设置关卡脚本',
      trigger: ['change']
    },
  ],
  conditionText: [
    {
      required: true,
      message: '请设置通关条件',
      trigger: ['change']
    }
  ]

})

const handleAdd = () => {
  formParams.value.levelStages.push({
    popId: new Date().getTime(),
    openTime: '',
    detail: [{
      scriptId: '',
      conditionText: '',// 拼接condition
      condition: [{
        scriptId: '',
        scriptName: '',
        time: null,
        score: null
      }],
    }]
  })
  nextTick(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
  });
}

//条件选择弹窗
const handleAddCondition = (parentIndex: number, index: number) => {
  showLevelDialog.value = true
  currentIndex.value = index
  currentParentIndex.value = parentIndex
  currentScriptId.value = formParams.value.levelStages[parentIndex].detail[index].scriptId
  currentOpenTime.value = formParams.value.levelStages[parentIndex].openTime
  currentConditionArray.value = formParams.value.levelStages[parentIndex].detail[index].condition && formParams.value.levelStages[parentIndex].detail[index].condition![0].scriptId !== '' ? formParams.value.levelStages[parentIndex].detail[index].condition as LevelStageUnlockConditionBo[] : []
}
const _getLevelScriptList = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
  })
  const res = await getLevelScriptList();
  loading.close()
  scriptOptions.value = res.data;
  _getDetail();

}
//获取弹窗内容
const setConditionFn = (val: LevelStageUnlockConditionBo[], openTime: string) => {
  if (!currentIndex.value && currentIndex.value !== 0) return
  formParams.value.levelStages[currentParentIndex.value as number].detail[currentIndex.value as number].condition = val
  let text = ''
  val.forEach(item => {
    text += (`获得${item.time}次${item.score}分及以上`)
  })
  formParams.value.levelStages[currentParentIndex.value as number].detail[currentIndex.value as number].conditionText = text
  formParams.value.levelStages[currentParentIndex.value as number].openTime = openTime
  proxy.$refs['formParamsRef'].validateField('levelStages.' + currentParentIndex.value + '.detail.' + currentIndex.value + '.conditionText')
  proxy.$refs['formParamsRef'].validateField('levelStages.' + currentParentIndex.value + '.openTime')

  currentScriptId.value = ''
}
const checkSave = () => {
  if (editFlag.value) {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
      if (typeof formParams.value[key] !== 'object') {
        console.log(JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key])))
        if (
          JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
        ) {
          bool = true;
          return;
        }
      } else {
        if (
          JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
        ) {
          bool = true;
          return;
        }
      }
    });
    if (bool) {
      ElMessageBox.confirm(
        '未保存的内容将丢失',
        '确定要返回吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          editFlag.value = false
        })
        .catch(() => {
        })

    } else {
      editFlag.value = false
    }
  }
  else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}


const showChooseDeptFn = () => {
  showChooseDept.value = true
}


const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
      ) {
        console.log(JSON.stringify(formParams.value[key]), JSON.stringify(oldFromInfo.value[key]))
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}

const showDelete = (popId: any, item: any, index: any) => {
  currentIndex.value = index
  item.scriptId || item.conditionText ? visible.value = true : deleteItem(popId, item, index)
}
const handleSetPopMap = (el: refItem, item: any) => {
  if (el) {
    deletePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelRemove = (id: any) => {
  deletePopverRefMap.value[`Pop_Ref_${id}`].hide()
}


const deleteItem = (popId: any, item: any, index: number) => {
  formParams.value.levelStages.splice(index, 1)
  cancelRemove(popId)
}




// 提交
const handleSubmit = () => {
  btnLoading.value = true;
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      submitFn()
    } else {
      btnLoading.value = false;
      return false;
    }
  });
}


// 提交方法
const submitFn = async () => {
  const formData = deepClone(formParams.value)
  formData.levelStages?.forEach((element: any, index: number) => {
    let array: any = []
    element.detail.forEach((item: any) => {
      array = [...array, ...item.condition]
    });
    element.condition = array
    element.openTime = element.openTime + ':00'
    element.orderNum = index + 1
  });
  formData.deptIds = formData.deptIds.join(',')
  const params = {
    ...formData,
    id: props.id
  };
  await updateLevelScriptGroup(params).then((res) => {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
    proxy.$modal.msgSuccess('保存成功');
  }).finally(() => {
    btnLoading.value = false;
  });
}


const _getDetail = async () => {
  formLoading.value = true
  const res = await getLevelDetail(props.id);
  formLoading.value = false
  res.data.levelStages.forEach((el: any, index: any) => {
    el.popId = new Date().getTime() + index
    el.detail.forEach((element: any) => {
      let text = ''
      element.condition.forEach((item: any) => {
        element.conditionText = text += (`获得${item.time}次${item.score}分及以上`)
      });
    });
  });
  formParams.value = {
    name: res.data.name,
    levelStages: res.data.levelStages,
    status: res.data.status,
    deptIds: res.data.deptIds.split(","),
    deptNames: res.data.deptNames.split(","),
    startDate: res.data.startDate,
    endDate: res.data.endDate,
    timeArray: [res.data.startDate, res.data.endDate],

  }
  oldFromInfo.value = deepClone(formParams.value)
}
const _getDeptTreeList = async () => {
  const res = await getDeptTreeList()
  if (res.data) {
    deptOptions.value = res.data
  }
  deptOptions.value = res.data
}

onMounted(() => {
  _getDeptTreeList()
  _getLevelScriptList()
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  // margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.condition {
  color: #aaaaaa;
}

@media screen and (max-width: 1600px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1601px) {
  .box-container {
    padding: 0 8% 100px 8%;
  }
}

.box-container {
  // display: flex;
  // flex-direction: column;
  // gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  box-sizing: border-box;
  height: calc(100vh - 84px - 32px - 100px);
  overflow-y: auto;
  width: 100%;
  margin-top: 30px;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  .el-card__body {

    // overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}




:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 84px - 32px - 100px);
  }
}

.delete {
  display: flex;
  align-items: center;
  gap: 0 16px;
  height: 30px;

  img {
    width: 20px;
    cursor: pointer;
    margin-bottom: 18px;
  }
}


.wrapper {
  flex:1;
  display: flex;
  margin: 0 auto;
  // width: 800px;
  overflow: hidden;
  border-radius: 8px;
  // padding: 15px ;
  // box-shadow: 20px 20px 60px #bebebe,
  //   -20px -20px 60px #ffffff;
}
.text {
  font-size: 14px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  /* display: flex; */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
}
.text::before {
  content: '';
  height: calc(100% - 24px);
  float: right;
}
.text::after {
  content: '';
  width: 999vw;
  height: 999vw;
  position: absolute;
  box-shadow: inset calc(100px - 999vw) calc(30px - 999vw) 0 0 #fff;
    margin-left: -100px;
}
.btn{
  float: right;
  clear: both;
  margin-left: 10px;
  font-size: 14px;
  padding: 0 8px;
  // background: #3F51B5;
  line-height: 14px;
  border-radius: 4px;
  color:  #333333;
  cursor: pointer;
  /* margin-top: -30px; */
}
.btn::before{
  content:'展开'
}
.exp{
  display: none;
}
.exp:checked+.text{
  -webkit-line-clamp: 999;
}
.exp:checked+.text::after{
  visibility: hidden;
}
.exp:checked+.text .btn::before{
  content:'收起'
}


.script-msg {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  .left {
    width: 90px;
    font-weight: 500;
    margin-right: 16px;
  }

  .right {
    flex: 1;
    display: flex;
    align-items: center;
  }
}

.error {
  color: #DE9827;
  font-size: 12px;
  margin-bottom: 18px;
  margin-left: 90px;
  font-family: var(--el-font-family);
}
</style>
