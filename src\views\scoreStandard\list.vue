<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">评分标准管理</span>
          <div class="search-container">
            <el-button type="primary" icon="Plus" @click="handleAdd">创建评分标准</el-button>
          </div>
        </div>
      </template>
      <el-table v-loading="loading" :data="list" @row-click="handleDetail">
        <!-- <el-table-column label="编号" align="center" type="index" width="150" /> -->
        <!-- <el-table-column type="selection" width="55" align="left" /> -->
        <el-table-column label="名称" align="left" prop="name" />

        <el-table-column label="适用脚本" align="center" prop="type">
          <template #default="scope">
            <span>{{ typeMap.get(scope.row.type) }}</span>
          </template>
        </el-table-column>
        <!--  <el-table-column label="调试" align="left" prop="checkFlag" width="100px">
          <template #default="scope">
            <div class="flex">
              <div class="dot" v-if="scope.row.checkFlag !== null" :style="{ backgroundColor: colorMap.get(scope.row.checkFlag) }"></div>
              {{ scope.row.checkFlag === null ? '待调试' : statusMap.get(scope.row.checkFlag) }}
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="状态" align="left" prop="status" width="150px">
          <template #default="scope">
            <span>{{scope.row.status === null ? statusMap.get(0):  statusMap.get(scope.row.status) }}</span>
          </template>
        </el-table-column> -->

        <el-table-column label="创建时间" align="left" prop="updateTime">
          <template #default="scope">
            <span v-formatTime="scope.row.createTime"></span>
          </template>
        </el-table-column>
        <el-table-column width="200" label="操作" align="left" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="gap-12">
              <el-button link type="primary" :disabled="scope.row.system" @click.stop="handleEdit(scope.row)" text>编辑</el-button>
              <span @click.stop>
                <el-popconfirm
                  width="220"
                  @confirm.stop="handleCopy(scope.row)"
                  placement="top"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  title="确定复制吗?"
                >
                  <template #reference>
                    <el-button link type="primary" text>复制</el-button>
                  </template>
                </el-popconfirm>
              </span>

              <!-- <span v-if="scope.row.status === null || scope.row.status === 0" @click.stop>
                <el-popconfirm
                  width="220"
                  placement="top"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  @confirm="onLineFn(scope.row)"
                  title="确定上线吗？"
                >
                  <template #reference>
                    <el-button link type="primary" text>发布</el-button>
                  </template>
                </el-popconfirm>
              </span> -->
              <!-- <span v-else> <el-button link type="primary" text disabled>发布</el-button></span> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-if="queryParams.status !== 4"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- <el-dialog
        ref="formDialogRef"
        title="发布"
        v-model="visibleFlag"
        width="600px"
        append-to-body
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form ref="formParamsRef" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="更新内容" prop="content">
                <el-input
                  v-model="form.content"
                  placeholder="请输入"
                  show-word-limit
                  :autosize="{ minRows: 8, maxRows: 10 }"
                  maxlength="150"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
          </div>
        </template>
      </el-dialog> -->
    </el-card>
  </div>
  <el-dialog class="script-type-dialog" v-model="chooseAddDialogVisible" width="100%" align-center :show-close="false">
    <template #title>
      <div class="script-type-head">请选择评分标准类型</div>
    </template>
    <div class="script-type-container">
      <div class="script-type-box" @click="chooseAddType(1)">
        <img class="script-type-icon" src="@/assets/images/script_skill.png" />
        <el-button type="primary" size="large" class="script-type-title">技巧类</el-button>
        <div class="script-type-tip" type="info">适用于技巧类脚本</div>
      </div>

      <!--      <div class="script-type-box" @click="chooseAddType(2)">
        <img class="script-type-icon" src="@/assets/images/script_question.png" />
        <el-button type="info" size="large" class="script-type-title">答题类</el-button>
        <div class="script-type-tip" type="info">适用于答题类脚本</div>
      </div>-->

      <div class="script-type-box" @click="chooseAddType(3)">
        <img class="script-type-icon script-type-icon-ppt" src="@/assets/images/script_ppt.svg" />
        <el-button type="primary" size="large" class="script-type-title">幻灯片演练</el-button>
        <div class="script-type-tip" type="info">适用于幻灯片演练</div>
      </div>
    </div>

    <img class="script-type-close" @click="closeAddTypeDialog" src="@/assets/images/icon_cancel.svg" />
  </el-dialog>
</template>

<script setup lang="ts">
import { queryList, duplicate } from '@/api/scoreStandard';
import { ChatReportScoringStandardVo } from '@/api/scoreStandard/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// import type { FormRules } from 'element-plus'
const list = ref<ChatReportScoringStandardVo[]>([]);
const loading = ref(true);
const btnLoading = ref(false);
// const formParamsRef = ref()
// const visibleFlag = ref(false)
// const currentRowId = ref('')
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  },
});
const total = ref(0);
const { queryParams } = toRefs(data);

const chooseAddDialogVisible = ref(false)

const emits = defineEmits(['change-type']);
const typeMap = new Map([
  [1, '技巧类'],
  [2, '答题类'],
  [3, '幻灯片演练']
])
const statusMap = new Map([
  [1, '已发布'],
  [0, '有修改未发布']
])
/* const colorMap = new Map([
  [true, '#2DDF29'],
  [false, '#FF1C1C']
]) */
/* const form = ref({
  content: ''
}) */
/* const rules = reactive<FormRules<{ content: string }>>({
  content: [
    { required: true, message: '请输入更新内容', trigger: 'blur' },
    {
      min: 1,
      max: 150,
      message: '最多150个字符',
      trigger: ["blur"]
    }
  ],
}) */

/**创建 */
const handleAdd = () => {
  chooseAddDialogVisible.value = true;
}

/**编辑 */
const handleEdit = (row: any) => {
  if (row.type === 3) {
    emits('change-type', { flagType: 'detail', isEdit: true, id: row.id, type: row.type })
  } else {
    emits('change-type', { flagType: 'skillDetail', isEdit: true, id: row.id, type: row.type })
  }


}
/**发布 */
/* const onLineFn = async (row: any) => {
  await online({ id: row.id }).then(() => {
        proxy.$modal.msgSuccess('发布成功');

        getList()
      }).finally(() => {
        btnLoading.value = false;
      });
} */

/**详情 */
const handleDetail = (row: any) => {
  if (row.type === 3) {
    emits('change-type', { flagType: 'detail', isEdit: false, id: row.id, type: row.type })
  } else {
    emits('change-type', { flagType: 'skillDetail', isEdit: false, id: row.id, type: row.type })
  }
}

/* const closeDialog = () => {
  currentRowId.value = ''
  formParamsRef.value.resetFields()
  formParamsRef.value.content = ''
  visibleFlag.value = false
} */


/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await queryList(queryParams.value);
  list.value = res.rows.filter(item => item.type !== 2);
  total.value = res.total;
  loading.value = false;
}

const handleCopy = async (row: any) => {
  try {
    // await proxy?.$modal.confirm('确定复制吗？');
    await duplicate(row.id)
    proxy?.$modal.msgSuccess('复制成功')
    getList()
  } catch (error) {
    console.log(error);
  }
}

const chooseAddType = (type: number) => {
  if (type === 2) {
    proxy?.$modal.msgError('暂不支持答题类评分标准创建')
    return
  }
  if (type === 1) {
    emits('change-type', { flagType: 'skillAdd', scriptType: type })
  } else if (type === 3) {
    emits('change-type', { flagType: 'add', scriptType: type })
  }
}
const closeAddTypeDialog = () => {
  chooseAddDialogVisible.value = false;
}

onMounted(() => {
  getList();
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}

:global(.script-type-dialog) {
  background: none;
  box-shadow: none;
}

.script-type {
  &-head {
    color: #fff;
    text-align: center;
    font-size: 24px;
  }

  &-content {
    cursor: pointer;
  }

  &-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    gap: 46px;
  }

  &-box {
    width: 280px;
    height: 310px;
    border-radius: 9px;
    text-align: center;
    background-color: #fff;
    overflow: hidden;
  }

  &-icon {
    width: 93px;
    height: 93px;
    display: block;
    margin: 40px auto 17px;

    &-ppt {
      width: 120px;
      height: 89px;
      margin: 43px auto 20px;
    }
  }

  &-title {
    font-size: 20px;
    font-weight: 500;
    width: 187px;
  }

  &-tip {
    font-size: 15px;
    margin-top: 20px;
    margin-left: 46px;
    margin-right: 46px;
  }

  &-close {
    display: block;
    width: 32px;
    height: 32px;
    margin: 68px auto 0;
    cursor: pointer;
  }
}

:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 90px - 100px);
  }
}

:deep(.el-table__row) {
  cursor: pointer;
}

.gap-12 {
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  gap: 0 12px;
}

.flex {
  display: flex;
  align-items: center;
}

.dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  margin-right: 8px;
}
</style>
