<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            创建闯关</span
          >
          <div class="right-button">
            <el-button plain type="info" @click="back()">取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" ref="scrollDiv">
        <el-form ref="formParamsRef" v-loading="formLoading" :model="formParams" :rules="rules" label-width="90px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />
          <div class="qa" v-for="(item, index) in formParams.levelStages" :key="index">
            <el-row :gutter="24" class="flex" style="display: flex;align-items: flex-start;">
              <el-col :span="10">
                <el-form-item :label="`关卡${index + 1}`" :prop="'levelStages.' + index + '.scriptId'" :rules="setRules.scriptId">
                  <el-select v-model="item.scriptId" placeholder="请选择" @change="checkPermission(item.scriptId, index)">
                    <template #label="{ label }">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(Number(label.slice(0, 1))) }"
                        ></div>
                        <span>{{ label.slice(1, label.length) }}</span>
                      </div>
                    </template>
                    <el-option v-for="it in scriptOptions" :key="it.id" :label="it.status + it.name" :value="it.id">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(it.status) }"
                        ></div>
                        {{ it.name }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item :label="`解锁条件`" :prop="'levelStages.' + index + '.conditionText'" :rules="setRules.conditionText">
                  <div class="condition">
                    <span v-if="item.conditionText === ''">待设置</span>
                    <span v-else>{{ item.conditionText }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <!-- <el-icon color="#dddddd" v-if="index === 0 && formParams.qaList.length === 1">
                <Delete />
              </el-icon> -->
              <div class="delete" v-if="index !== 0">
                <el-tooltip content="设置条件" placement="top">
                  <img src="@/assets/icons/png/setting.png" @click="handleAddCondition(index)" />
                </el-tooltip>

                <el-popover trigger="click" placement="top" :ref="(el: refItem) => handleSetPopMap(el, item.id)" :width="300" popper-class="popover">
                  <div style="display: flex;flex-direction: column;gap:8px 0;">
                    <span class="title">确定删除此关卡吗？</span>
                    <span class="message">删除后此关卡的脚本无需解锁即可使用，脚本解锁状态将重置。</span>
                  </div>
                  <div style="text-align: right; margin: 0;margin-top: 30px;">
                    <el-button @click="cancelRemove(item.id)">取消</el-button>
                    <el-button type="primary" @click="deleteItem(item, index)">确定</el-button>
                  </div>
                  <template #reference>
                    <el-icon>
                      <img src="@/assets/icons/png/delete.png" @click="showDelete(item, index)" />
                    </el-icon>
                  </template>
                </el-popover>
              </div>
            </el-row>
            <div class="error" v-if="item.errorFlag">
              建议解锁条件设置的任务脚本的权限≥关卡脚本的权限，否则可能导致部分学员没有任务脚本权限导致关卡脚本无法解锁。
            </div>
          </div>
          <el-button type="primary" icon="Plus" @click="handleAdd">添加关卡</el-button>
        </el-form>
      </div>
      <SetCondition
        :script-id="currentScriptId"
        :current-condition="currentConditionArray"
        @success="setConditionFn"
        v-model:visible="showLevelDialog"
        title="解锁条件设置"
      />
    </el-card>
  </div>
</template>

<script setup name="AddLevelGroup" lang="ts">
import SetCondition from "./components/setCondition.vue";
import { reactive, ref } from 'vue'
import { ComponentPublicInstance } from "vue";
import { ElMessageBox } from 'element-plus'
import type { FormRules } from 'element-plus'
import { LevelFormBo, ScriptDetailVo, LevelStageUnlockConditionBo, LevelStageBo } from "@/api/script/types";
import { getLevelScriptList, addLevelScriptGroup, scriptLevelDuplicateCheck, scriptUnlockPermissionCheck } from "@/api/script";
// import { FormValidators } from '@/utils/validate.js'
import { } from "@/utils/index"
type refItem = Element | ComponentPublicInstance | null;
const deletePopverRefMap = ref({});



const scrollDiv = ref()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
import { deepClone } from "@/utils/index"
const formLoading = ref<boolean>(false)
const visible = ref(false)
const initForm = {
  name: '',
  levelStages: [
    {
      scriptId: '',
      conditionText: '无条件',// 拼接condition
      condition: null,
      errorFlag: false
    }
  ]
} as LevelFormBo
const formParams = ref(deepClone(initForm))
const scriptOptions = ref<ScriptDetailVo[]>([])
const btnLoading = ref(false)
const showLevelDialog = ref(false)
const currentIndex = ref<number | null>(null)
const currentScriptId = ref<string>('')
const currentConditionArray = ref<LevelStageUnlockConditionBo[]>([])
const colorMap = new Map([
  [2, '#5EEE3A'],
  [1, '#D9D9D9'],
])

const checkSameScriptLimit = async (rule: any, value: any, callback: any) => {
  const res = await scriptLevelDuplicateCheck({ scriptId: value, levelId: null })
  if (res.data) {
    callback(new Error('关卡脚本之间不能重复，且不能与其它闯关组重复'))
  } else {
    const questionArray = formParams.value.levelStages?.filter(item => item.scriptId === value) || []
    if (questionArray.length > 1) {
      callback(new Error('关卡脚本之间不能重复，且不能与其它闯关组重复'))
    } else {
      callback()
    }
  }

}

const handleSetPopMap = (el: refItem, item: string) => {
  if (el) {
    deletePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelRemove = (id) => {
  deletePopverRefMap.value[`Pop_Ref_${id}`].hide()
}

const rules = reactive<FormRules<LevelFormBo>>({
  name: [
    { required: true, message: '请输入脚本名称', trigger: 'blur' },
    {
      min: 1,
      max: 50,
      message: '最多50个字符',
      trigger: ["blur"]
    }
  ]
})
const setRules = reactive({
  scriptId: [
    {
      required: true,
      message: '请设置关卡脚本',
      trigger: ['change']
    },
    {
      validator: checkSameScriptLimit,
      trigger: ['change']
    },
  ],
  conditionText: [
    {
      required: true,
      message: '请设置解锁条件',
      trigger: ['change']
    }
  ]

})

const handleAdd = () => {
  formParams.value.levelStages.push({
    scriptId: '',
    conditionText: '',// 拼接condition
    condition: [{
      scriptId: '',
      scriptName: '',
      time: null,
      score: null
    }],
  })
  nextTick(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
  });
}

//条件选择弹窗
const handleAddCondition = (index: number) => {
  showLevelDialog.value = true
  currentIndex.value = index
  currentScriptId.value = formParams.value.levelStages[index].scriptId
  currentConditionArray.value = formParams.value.levelStages[index].condition![0].scriptId !== '' ? formParams.value.levelStages[index].condition as LevelStageUnlockConditionBo[] : []
}
const _getLevelScriptList = async () => {
  formLoading.value = true
  const res = await getLevelScriptList();
  formLoading.value = false
  scriptOptions.value = res.data;
}
//获取弹窗内容
const setConditionFn = (val: LevelStageUnlockConditionBo[]) => {
  if (!currentIndex.value) return
  formParams.value.levelStages[currentIndex.value as number].condition = val
  let text = ''
  val.forEach(item => {
    text += (`${item.scriptName}，获得${item.time}次${item.score}分及以上`)
  })
  formParams.value.levelStages[currentIndex.value as number].conditionText = text
  proxy.$refs['formParamsRef'].validateField('levelStages.' + currentIndex.value + '.conditionText')
  checkPermission(formParams.value.levelStages[currentIndex.value as number], currentIndex.value)
}


const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(initForm[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(initForm[key])
      ) {
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: 4 })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: 4 })
  }
}


// watch(() => formParams.value.levelStages, () => {

// }, { deep: true })


const deleteItem = (item: any, index: number) => {
  formParams.value.levelStages.splice(index, 1)
}

const showDelete = (item: any, index: any) => {
  currentIndex.value = index
  item.scriptId || item.conditionText ? visible.value = true : deleteItem(item, index)
}

//检查权限方法
const _scriptUnlockPermissionCheck = async (levelStage: LevelStageBo) => {
  formLoading.value = true
  const unlockConditionScriptIds = levelStage.condition?.map(item => item.scriptId) || []
  const res = await scriptUnlockPermissionCheck({ scriptId: levelStage.scriptId, unlockConditionScriptIds: unlockConditionScriptIds })
  formLoading.value = false
  if (!res.data) {
    formParams.value.levelStages[currentIndex.value as number].errorFlag = true
  } else {
    formParams.value.levelStages[currentIndex.value as number].errorFlag = false
  }
}

// 检查权限
const checkPermission = (value: any, index: any) => {
  formParams.value.levelStages.forEach((element: any, index: number) => {
    proxy.$refs['formParamsRef'].validateField('levelStages.' + index + '.scriptId')
  });
  if (index !== 0 && formParams.value.levelStages[index].scriptId && formParams.value.levelStages[index].conditionText) {
    _scriptUnlockPermissionCheck(formParams.value.levelStages[index])
  }
}


// 提交
const handleSubmit = () => {
  btnLoading.value = true;
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const formData = deepClone(formParams.value)
      const errorArray = formData.levelStages.filter((item: any) => item.errorFlag)
      if (errorArray.length > 0) {
        ElMessageBox.confirm(
          '建议解锁条件设置的任务脚本的权限≥关卡脚本的权限，否则可能导致部分学员没有任务脚本权限导致关卡脚本无法解锁。',
          '提醒',
          {
            confirmButtonText: '仍然保存',
            cancelButtonText: '取消',
            type: 'none',
          }
        )
          .then(async () => {
            submitFn()
          })
          .catch(() => {
            btnLoading.value = false;
          })
      } else {
        submitFn()
      }

    } else {
      btnLoading.value = false;
      return false;
    }
  });
}
// 提交方法
const submitFn = async () => {
  const params = {
    ...formParams.value,
  };
  await addLevelScriptGroup(params).then((res) => {
    emits('change-type', { flagType: 'list', id: '', listType: 4 })
    proxy.$modal.msgSuccess('保存成功');
  }).finally(() => {
    btnLoading.value = false;
  });
}





_getLevelScriptList()
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.condition {
  color: #aaaaaa;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 8% 100px 8%;
  }
}

.box-container {
  // display: flex;
  // flex-direction: column;
  // gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  box-sizing: border-box;
  height: calc(100vh - 84px - 32px - 100px);
  overflow-y: auto;
  margin-top: 30px;
  width: 100%;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  .el-card__body {

    // overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}


.error {
  color: #DE9827;
  font-size: 12px;
  margin-bottom: 18px;
  margin-left: 90px;
  font-family: var(--el-font-family);
}


:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 84px - 32px - 100px);
  }
}

.delete {
  display: flex;
  align-items: center;
  gap: 0 16px;

  img {
    width: 20px;
    cursor: pointer;
    margin-bottom: 18px;
  }
}

.tips {
  color: #999999;
  margin-top: 4px;
  font-size: 14px;
}

.random-num {
  color: #333;
}
</style>
