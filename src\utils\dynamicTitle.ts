// import defaultSettings from '@/settings';
// import { useSettingsStore } from '@/store/modules/settings';
import { useUserStore } from '@/store/modules/user';
/**
 * 动态修改标题
 */
export const useDynamicTitle = () => {
  // const settingsStore = useSettingsStore();
  const userStore = useUserStore();
  document.title = import.meta.env.VITE_APP_TITLE ? import.meta.env.VITE_APP_TITLE : userStore.enterpriseFlag ? 'Ecai语贝运营后台' : '语贝CMS后台';
  // if (settingsStore.dynamicTitle) {
  //   document.title = settingsStore.title + ' - ' + import.meta.env.VITE_APP_TITLE;
  // } else {
  //   document.title = defaultSettings.title as string;
  // }
};
