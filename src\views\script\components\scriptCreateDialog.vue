<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      v-model="visibleShow"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      :show-close="false"
      @open="onOpen"
    >
      <template #header="{ titleId, titleClass }">
        <div :id="titleId" :class="titleClass">{{title}}</div>
        <div class="title-sub">{{ titleSub }}</div>
      </template>
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="visitObject">
                <template #label>
                  <span class="label-text">拜访对象</span>
                  <el-tooltip content="此次拜访假设的拜访对象是谁，比如：医生、店员、患者等。" placement="top">
                    <el-icon class="label-tip">
                      <!--  <img src="@/assets/icons/svg/help1.svg" class="help" /> -->
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-select clearable v-model="formParams.visitObject" placeholder="请选择">
                  <el-option v-for="item in visitorCreateList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="visitStage">
                <template #label>
                  <span class="label-text">拜访阶段</span>
                  <el-tooltip content="此次拜访与拜访对象之间的互动处于哪个阶段。" placement="top">
                    <el-icon class="label-tip">
                      <!--  <img src="@/assets/icons/svg/help1.svg" class="help" /> -->
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-select clearable v-model="formParams.visitStage" placeholder="请选择">
                  <el-option v-for="item in visitStageList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="22">
              <el-form-item prop="keyInfo">
                <template #label>
                  <span class="label-text">药品信息</span>
                  <el-tooltip content="此次拜访需要向拜访对象传递的药品信息。" placement="top">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-input placeholder="请输入" resize="none" v-model="formParams.keyInfo" :autosize="{ minRows: 4,maxRows:6 }" type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer" :class="dialogType === 'create' ? '':'end'">
          <el-button @click="handleManual" v-if="dialogType === 'create'">手动创建</el-button>
          <div class="footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" :loading="loading" :disabled="submitDisabled" @click="handleSubmit">
              <img src="@/assets/icons/svg/shine.svg" class="start-icon" />
              开始生成</el-button
            >
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { FormRules } from 'element-plus';
import {visitorCreateList, visitStageList} from './data'
import { generateScript, getGenerateInfo, regenerateScript } from '@/api/script';
const loading=ref(false)
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'onManual', 'onSuccess'])
const hasBefore = ref(false)
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogType: {
    type: String,
    default: ''
  },
  scriptId: {
    type: String,
    default: ''
  },
  params: {
    type: Object
  }
})

interface RuleForm {
  visitObject: string
  visitStage: string
  keyInfo: string
}
const initForm = {
  visitObject:  '',
  visitStage: '',
  keyInfo: ''
}

const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 5000) {
    callback(new Error('最多5000个字符'))
  } else {
    callback()
  }
}

const formParamsRef = ref()
const formParams = ref(initForm)
const rules = reactive<FormRules<RuleForm>>({
  visitObject: [
  { required: true, message: '请选择拜访对象', trigger: ["blur", "change"] },
  ],
  visitStage: [
  { required: true, message: '请选择拜阶段', trigger: ["blur", "change"] },
  ],
  keyInfo: [
    { required: true, message: '请输入关键信息', trigger: 'blur' },
    {
      validator: checkFontLengthLimit,
      trigger: ["blur"]
    }
  ]
})
const submitDisabled = computed(() => {
  // formParams属性值都不为null，则是true，否则false
  return Object.values(formParams.value).some(item => item === null || item === ''|| item === undefined)
})
// 弹窗组件显示隐藏
const visibleShow = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const title = computed(() => {
  return props.dialogType === 'create'? '创建脚本' : 'AI生成'
})

const titleSub = computed(() => {
  return props.dialogType === 'create'? '请输入相关信息，AI将自动生成脚本' : '生成成功后，将替换当前脚本内容'
})

const handleManual = () => {
  emits('onManual')
}
const closeDialog = () => {
  formParams.value = {...initForm}
  formParamsRef.value?.resetFields();
  visibleShow.value = false
}

const handleSubmit = () => {
  loading.value = true
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      try {
        const res = hasBefore.value ? await regenerateScript(formParams.value) : await generateScript(formParams.value)
        loading.value = false
        console.log(res, 'generate');
        emits('onSuccess', res.data, formParams.value)
      } catch (error) {
        loading.value = false
        console.log(error);
      }

    } else {

      console.log('error submit!!');
      return false;
    }
  });

}

const onOpen = async () => {
  console.log('onOpen', props.scriptId);
  if(props.scriptId) {
    const res = await getGenerateInfo(props.scriptId)
    if(res.data) {
      formParams.value = res.data
      hasBefore.value = true
    } else {
      formParams.value = {...initForm}
      hasBefore.value = false
    }

  } else {
    if(props.params) {
      formParams.value = {...props.params}
    } else {
      formParams.value = {...initForm}
      hasBefore.value = false
    }

  }

}
</script>

<style scoped lang="scss">
.title-sub {
  font-size: 14px;
  font-weight: normal;
  color: #999999;
  margin-top: 8px;
}
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.end {
    justify-content: flex-end;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}
.help {
  width: 16px;
  margin-left: 8px;
}
</style>
