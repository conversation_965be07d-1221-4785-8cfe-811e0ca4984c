<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">成员管理</span>
          <div class="search-container">
            <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
              <el-form-item prop="keyword">
                <el-input
                  v-model="queryParams.keyword"
                  @clear="resetQuery"
                  placeholder="请输入搜索内容"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <!-- <el-form-item prop="deptIds">
                <el-tree-select
                  v-model="queryParams.deptIds"
                  @change="handleQuery"
                  :data="deptOptions"
                  node-key="id"
                  collapse-tags
                  check-strictly
                  collapse-tags-tooltip
                  :max-collapse-tags="2"
                  multiple
                  show-checkbox
                  style="width: 250px"
                  :props="{ value: 'id', label: 'label', children: 'children', disabled: 'selected' }"
                  placeholder="请选择部门"
                  :default-expand-all="true"
                />
              </el-form-item> -->
              <el-col :span="1.5" style="float: right;">
                <el-button
                  @click="handleBatchDelete()"
                  v-if="queryParams.status === 2 && multipleSelection.length > 0"
                  :disabled="multipleSelection.length === 0"
                  >删除</el-button
                >

                <el-button @click="showImportDialog = true">批量导入</el-button>

                <el-button v-if="userStore.cpFlag" @click="_syncUser" :loading="syncLoading" loading-text="正在同步">{{
                  syncLoading ? '正在同步' : '一键同步' }}</el-button>

                <el-button type="primary" icon="Plus" @click="handleAdd()">新增成员</el-button>

                <el-dropdown style="margin-left: 8px" v-if="queryParams.status !== 2">
                  <el-button>
                    <!-- <el-icon>
                    <SetUp />
                  </el-icon> -->
                    <img src="@/assets/icons/png/more.png" style="width:20px;height: 20px;" />
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click.stop="handleEditDept()" :disabled="multipleActionDisabled">
                        <span v-if="multipleExternal">
                          <el-tooltip content="不支持对企微成员进行编辑" placement="top"> 编辑部门 </el-tooltip>
                        </span>
                        <span v-else> 编辑部门 </span>
                      </el-dropdown-item>
                      <el-dropdown-item @click.stop="handleBatchFreezeUser()" :disabled="multipleActionDisabled">
                        <span v-if="multipleExternal">
                          <el-tooltip content="不支持对企微成员进行冻结" placement="top"> 冻结 </el-tooltip>
                        </span>
                        <span v-else> 冻结 </span>
                      </el-dropdown-item>
                      <el-dropdown-item @click.stop="_sendBatchSms()" :disabled="multipleActionDisabled">
                        <span v-if="multipleExternal">
                          <el-tooltip content="不支持对企微成员发送提醒" placement="top"> 发送提醒 </el-tooltip>
                        </span>
                        <span v-else> 发送提醒 </span>
                      </el-dropdown-item>
                      <el-dropdown-item @click.stop="_handleBatchAuthority()" :disabled="multipleSelection.length === 0">设置</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-col>
            </el-form>
          </div>
        </div>
      </template>

      <div class="content-wrap">
        <!-- <el-col :span="6"> -->
        <div class="dept-container-wrap" :style="{ width: extendFlag ? '30%' : '0' }">
          <div class="dept-container">
            <el-tree
              class="mt-2"
              node-key="id"
              :props="{ label: 'deptName', children: 'children', isLeaf: 'isLeaf' }"
              :load="loadNode"
              :loading="treeLoading"
              :expand-on-click-node="false"
              highlight-current
              lazy
              ref="deptRef"
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  {{ data.deptName }}
                  <!-- {{ data }} -->
                  <span>（{{ data.userNum }}）</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
        <!-- </el-col> -->
        <el-divider direction="vertical" v-show="extendFlag" class="divider" />
        <div class="tab-container">
          <div class="header">
            <div class="flex">
              <el-tooltip :content="extendFlag ? '收起' : '展开'" placement="top">
                <hamburger id="hamburger-container" :is-active="extendFlag" class="hamburger-container" @toggleClick="toggleSideBar" />
              </el-tooltip>
              <el-tabs v-model="queryParams.status" class="demo-tabs" @tab-click="switchTab">
                <el-tab-pane label="成员" :name="-1"></el-tab-pane>
                <el-tab-pane label="已冻结" :name="2"></el-tab-pane>
                <el-tab-pane label="白名单" :name="3"></el-tab-pane>
              </el-tabs>
            </div>

            <div class="flex">
              <el-tooltip content="筛选" placement="top">
                <div style="display: flex; align-items: center;">
                  <el-popover placement="bottom" :width="300" trigger="click">
                    <template #reference>
                      <img class="export" :src="checkList.length > 0 ? filter1 : filter" v-if="queryParams.status === -1" />
                    </template>
                    <div class="title" style="margin-bottom: 12px;">筛选</div>
                    <el-checkbox-group v-model="checkList" style="display: flex; flex-direction: column; gap: 10px;" @change="filterCheckboxChange">
                      <el-checkbox v-if="!userStore.cpFlag" label="拥有数据查看权限" value="kanbanFlag" />
                      <el-checkbox label="拥有报告分享权限" value="shareFlag" />
                      <el-checkbox label="白名单" value="whiteFlag" />
                      <el-checkbox label="已激活" value="activated" />
                      <el-checkbox label="未激活" value="unactivated" />
                    </el-checkbox-group>
                  </el-popover>
                </div>
              </el-tooltip>
              <el-tooltip content="导出" placement="top">
                <img class="export" src="@/assets/icons/png/export.png" @click="exportFile" v-if="queryParams.status === -1" />
              </el-tooltip>
            </div>
          </div>

          <el-form-item prop="whiteFlag" v-if="queryParams.status === 3">
            <template #label>
              <span class="label-text flex">白名单数据过滤</span>
              <el-tooltip
                content="开启后，数据看板中的学员数据、脚本数据、积分数据、闯关数据将不统计白名单内的成员的练习数据;但闯关数据中通关人数的统计会包含白名单。"
                placement="top"
                popper-class="popper"
              >
                <el-icon class="label-tip">
                  <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;  margin-left: 8px;" />
                </el-icon>
              </el-tooltip>
            </template>
            <el-switch v-model="currentWhiteFlag" @change="changeCurrentWhiteFlagFn" />
          </el-form-item>

          <el-table
            v-loading="loading"
            :data="userList"
            ref="tableRef"
            style="width: 100%;"
            row-key="id"
            @row-click="handleDetail"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="left" reserve-selection />
            <!-- <el-table-column label="编号" align="left" type="index" width="150" /> -->
            <el-table-column label="姓名" align="left" prop="name" min-width="130">
              <template #default="scope">
                <div class="flex" style="justify-content: flex-start;gap:0 8px;">
                  {{ scope.row.name }}
                  <img src="@/assets/icons/png/kanban.png" v-if="scope.row.kanbanFlag && !userStore.cpFlag" style="width: 14px;" />
                  <img src="@/assets/icons/png/whiteflag.png" v-if="scope.row.whiteFlag" style="width: 14px;" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="部门" align="left" min-width="250" prop="deptName" v-if="queryParams.status !== 2" />

            <el-table-column label="手机号" width="150" align="left" prop="phoneNumber" />

            <!-- <el-table-column label="角色" align="left" prop="name" /> -->

            <el-table-column label="激活状态" align="left" prop="activateFlag" width="180">
              <template #default="scope">
                <div class="sms flex">
                  <span> {{ scope.row.activateFlag ? '是' : '否' }}</span>

                  <div
                    class="primary"
                    @click.stop="_sendSms(scope.row.phoneNumber, scope.row.id)"
                    v-if="userStore.cpFlag ? (scope.row.externalUserId === null ? !scope.row.activateFlag && scope.row.status !== 2 && scope.row.canSendSms : false) : (!scope.row.activateFlag && scope.row.status !== 2 && scope.row.canSendSms)"
                  >
                    发送提醒
                  </div>
                  <div
                    class="success"
                    v-if="userStore.cpFlag ? (scope.row.externalUserId === null ? !scope.row.activateFlag && !scope.row.canSendSms && scope.row.status !== 2 : false) : (!scope.row.activateFlag && !scope.row.canSendSms && scope.row.status !== 2)"
                  >
                    已发送
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200" label="操作" align="left" fixed="right" class-name="small-padding fixed-width">
              <template #default="scope">
                <span @click.stop v-if="queryParams.status === 3">
                  <el-popconfirm
                    width="300"
                    placement="top"
                    @confirm.stop="handleRemoveWhiteFlag(scope)"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定要将用户移除白名单吗？"
                  >
                    <template #reference>
                      <el-button link type="primary" text>移除</el-button>
                    </template>
                  </el-popconfirm>
                </span>

                <div v-else-if="queryParams.status !== 3 && scope.row.status !== 2">
                  <template v-if="userStore.cpFlag && scope.row.externalUserId !== null">
                    <el-tooltip content="不支持对企微成员进行编辑" placement="top">
                      <el-button link type="primary" @click.stop="handleEdit(scope)" text disabled>编辑</el-button>
                    </el-tooltip>
                  </template>
                  <template v-else>
                    <el-button link type="primary" @click.stop="handleEdit(scope)" text>编辑</el-button>
                  </template>

                  <el-button link type="primary" @click.stop="handleAuthority(scope)" text>设置</el-button>

                  <template v-if="userStore.cpFlag && scope.row.externalUserId !== null">
                    <el-tooltip content="不支持对企微成员进行冻结" placement="top">
                      <el-button link type="primary" @click.stop="handleAudit(scope)" text :disabled="true">冻结</el-button>
                    </el-tooltip>
                  </template>
                  <template v-else>
                    <el-button link type="primary" @click.stop="handleAudit(scope)" text>冻结</el-button>
                  </template>
                </div>
                <el-button v-else link type="primary" @click.stop="handleAudit(scope)" text>解冻</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <pagination
              v-show="total > 0"
              :total="total"
              :page-sizes="[10, 20, 30, 50, 100, 150, 200, 250, 300]"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="setInitList"
            />
          </div>
        </div>
      </div>
    </el-card>
    <Add :title="comAddTitle" @freezeUser="_freezeUser" v-model:visible="showAdd" @success="getList"> </Add>
    <Detail :id="comAddId" :isEdit="isEdit" :title="comEditTitle" v-model:visible="showEdit" @success="getList"> </Detail>
    <DeptSelect title="编辑" :user-id-list="multipleSelection" v-model:visible="showDept" @success="getList"></DeptSelect>
    <FileImport v-model:visible="showImportDialog" @success="getList"></FileImport>
    <Authority :ids="comAddIds" v-model:visible="showAuthorityDialog" @success="getList" :data-scope="currentDataScope"> </Authority>
  </div>
</template>

<script setup lang="ts">
import { getUserList, freezeUser, deleteUser, checkAppUserLimit, sendSms, sendBatchSms, removeWhiteFlag, syncUser, getIsSyncing } from '@/api/user';
import { getTenantWhiteFlag, changeWhiteFlag } from "@/api/system/tenant";
import { getDeptList } from "@/api/department";
import { userListQuery, userResult } from '@/api/user/types';
import { DeptVo } from "@/api/department/types";
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
// import { useUserStore } from '@/store/modules/user';
import Add from "./add.vue";
import Detail from "./detail.vue";
import DeptSelect from "./dept.vue";
import FileImport from "./components/fileImport.vue";
import Authority from "./components/authority.vue";
import filter1 from "@/assets/icons/svg/filter1.svg";
import filter from "@/assets/icons/svg/filter.svg";
// import router from '@/router';
const deptOptions = ref<DeptVo[]>([])
import { ElMessageBox } from 'element-plus'
import type { TabsPaneContext, TableInstance } from 'element-plus'
// const userStore = useUserStore();
const treeLoading = ref(false)
const deptRef = ref()
const syncLoading = ref(false)
const tableRef = ref<TableInstance>()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userList = ref<userResult[]>([]);
const multipleSelection = ref<userResult[]>([]);
// const cacheMultipleSelection = ref<userResult[]>([])
// const tableRef = ref<TableInstance>()
const loading = ref(true);
const total = ref(0);
const showAdd = ref(false)
const showEdit = ref(false)
const showDept = ref(false)
const showAuthorityDialog = ref(false)
const isEdit = ref(false)
const showImportDialog = ref(false)
const currentDataScope = ref()
const currentWhiteFlag = ref(false)
const comAddId = ref() // 事件id
const comAddIds = ref()
const comAddTitle = ref('新建') // 弹窗组件标题
const comEditTitle = ref('编辑') // 弹窗组件标题
const rootResolve = ref()
const rootNode = ref()
const extendFlag = ref(false) //部门折叠标志




let intervalId: any = null
import { download } from "@/utils/request";
import { deepClone } from '@/utils';
import { use } from 'echarts';
import { styleType } from 'element-plus/es/components/table-v2/src/common';
// import { has } from 'vue-types/dist/utils';
const queryFormRef = ref<ElFormInstance>();
const props = defineProps({
  listType: {
    type: Number,
    default: null
  },
  handleId: {
    type: String,
    default: null
  },
})
const data = reactive<PageQueryData<userListQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: '',
    deptIds: [],
    whiteFlag: null,
    activateFlag: null,
    kanbanFlag: null,
    status: props.listType as unknown as number || -1
  },
});
const { queryParams } = toRefs(data);
const checkList = ref([])

const multipleActionDisabled = computed(() => {
  if (userStore.cpFlag && multipleSelection.value.length > 0) {
    return multipleSelection.value.filter(Item => Item.externalUserId !== null).length > 0
  } else {
    return multipleSelection.value.length === 0
  }
})

const multipleExternal = computed(() => {
  return userStore.cpFlag && multipleSelection.value.filter(Item => Item.externalUserId !== null).length > 0
})

/**表格多选 */
const handleSelectionChange = (val: userResult[]) => {
  // // cacheMultipleSelection.value = cacheMultipleSelection.value.concat(val)
  // val.forEach((item: userResult) => {
  //   if (!cacheMultipleSelection.value.find(item => item.id === item.id)) {
  //     cacheMultipleSelection.value.push(item)
  //   }else{
  //     cacheMultipleSelection.value.splice(cacheMultipleSelection.value.findIndex(item => item.id === item.id), 1)
  //   }
  // })
  // console.log(cacheMultipleSelection.value)

  multipleSelection.value = val
  console.log(multipleSelection.value)

}

const toggleSideBar = () => {
  extendFlag.value = !extendFlag.value
}

/** 节点单击事件 */
const handleNodeClick = (data: any) => {
  queryParams.value.deptIds = data.id;
  queryParams.value.pageNum = 1;
  handleQuery()
}


//发短信
const _sendSms = async (phoneNumber: string, id: string | number) => {
  if (!phoneNumber) {
    proxy?.$modal.msgError('缺少手机号');
    return
  }
  const res = await sendSms(id)
  if (res.code === 200) {
    proxy?.$modal.msgSuccess(res.msg);
    getList();
  }
}
const setInitList = () => {
  // tableRef.value!.clearFilter(['activateFlag'])
  // queryParams.value.activateFlag=null
  // console.log(cacheMultipleSelection.value)
  getList()

}
//批量发短信
const _sendBatchSms = async () => {
  if (multipleSelection.value.filter(item => item.activateFlag === false).length === 0) {
    proxy?.$modal.msgError('请选择未激活人员');
    return
  }
  ElMessageBox.confirm(
    '将对激活状态为“否”的成员发送短信提醒，当日已发送提醒和已激活的成员将自动跳过，每日每人只能发送1次提醒。',
    '确定要批量发送短信提醒吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'none',
    }
  )
    .then(async () => {
      const array = multipleSelection.value.map((item: userResult) => item.id)
      await sendBatchSms(array).then((res) => {
        if (res.code === 200) {
          proxy?.$modal.msgSuccess(res.msg);
          getList();
        }
      });
    })
    .catch(() => {
    })
}

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  let params: any = deepClone(queryParams.value)
  if (params.status === -1 || params.status === 3) {
    delete params.status;
  }
  // 已冻结不在部门中，不用查询部门id
  if (params.status === 2) {
    delete params.deptIds;
  }
  const res = await getUserList(params);
  userList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
//获取白名单标志
const _getTenantWhiteFlag = async () => {
  const res = await getTenantWhiteFlag();
  currentWhiteFlag.value = res.data
}
//修改白名单标志

const changeCurrentWhiteFlagFn = async (val: boolean) => {
  await changeWhiteFlag({ status: val ? 1 : 0 })
}


/** 搜索 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

const switchTab = (tab: TabsPaneContext) => {
  checkList.value = []
  if (tab.props.name as number !== 3) {
    queryParams.value.whiteFlag = null
    queryParams.value.status = tab.props.name as number
  } else {
    queryParams.value.status = tab.props.name as number
    queryParams.value.whiteFlag = true
    _getTenantWhiteFlag()
  }
  queryParams.value.kanbanFlag = null
  queryParams.value.activateFlag = null
  queryParams.value.pageNum = 1;
  tableRef.value!.clearFilter(['activateFlag'])
  tableRef.value?.clearSelection()
  queryParams.value.activateFlag = null
  // console.log(queryParams.value.activateFlag)
  getList();

}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}
/**详情 */
const handleDetail = (row: any) => {
  showEdit.value = true
  comAddId.value = row.id
  isEdit.value = false
  comEditTitle.value = '基本信息'
}

/**编辑 */
const handleEdit = (scope: any) => {
  showEdit.value = true
  comAddId.value = scope.row.id
  isEdit.value = true
  comEditTitle.value = '编辑成员'
}
// const _getDeptTreeList = async () => {
//   const res = await getDeptList({ parentId: '0' })
//   if (res.data) {
//     const data = proxy?.handleTree<DeptVo>(res.data, "id")
//     data.forEach((item: DeptVo) => {
//       item.isLeaf = !item.hasChildren
//     })
//     if (data) {
//       deptOptions.value = data
//       nextTick(() => {
//         deptRef.value?.setCurrentKey(deptOptions.value[0].id);
//       })
//     }
//   }
// }

const loadNode = async (
  node: any,
  resolve: (data: DeptVo[]) => void
) => {
  // console.log(node)
  let res: any = [];
  if (node.level === 0) {
    // deptRef.value.store.root.doCreateChildren(null);
    rootResolve.value = resolve
    rootNode.value = node
    treeLoading.value = true
    res = await getDeptList({ parentId: '0' });
    deptOptions.value = res.data
    nextTick(() => {
      deptRef.value?.setCurrentKey(res.data[0].id);
      let nodeData = node.childNodes[0];
      nodeData.expanded = true;
      nodeData.loadData();
      treeLoading.value = false
    })
  } else {
    res = await getDeptList({ parentId: node.data.id });
  }

  res.data.forEach((item: DeptVo) => {
    item.isLeaf = !item.hasChildren
  })
  resolve(res.data);
}

// 导出
const exportFile = () => {
  const param: any = { ...queryParams.value }
  delete param.status;
  delete param.pageNum;
  delete param.pageSize;
  download('/biz/user/export', param, `${deptOptions.value[0].deptName || ''}已导入成员.xlsx`)
}

const handleAuthority = (scope: any) => {
  showAuthorityDialog.value = true
  comAddIds.value = [scope.row.id]
  currentDataScope.value = { dataScope: scope.row.dataScope, kanbanFlag: scope.row.kanbanFlag, whiteFlag: scope.row.whiteFlag || false }
  comEditTitle.value = '设置'
}

const _handleBatchAuthority = () => {
  const array = multipleSelection.value.map((item: userResult) => item.id)
  comAddIds.value = array
  comEditTitle.value = '设置'
  currentDataScope.value = {}
  showAuthorityDialog.value = true
}


/**成员编辑部门 */
const handleEditDept = () => {
  showDept.value = true
  comAddTitle.value = '编辑'
}

const handleAdd = async () => {
  const res = await checkAppUserLimit()
  if (!res.data.checkAppUserLimit) {
    proxy?.$modal.msgError(`组织架构成员最多${res.data.appUserLimit}人`);
    return
  }
  showAdd.value = true
  comAddTitle.value = '新增成员'
}


const handleBatchFreezeUser = () => {
  ElMessageBox.confirm(
    '请注意，冻结之后这些成员的账号将无法正常使用。确认要继续执行此操作吗？',
    '批量冻结',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const array = multipleSelection.value.map((item: userResult) => item.id)
      await freezeUser(array).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('冻结成功');
          queryParams.value.pageNum = 1;
          getList();
        }
      });
    })
    .catch(() => {
    })
}

const _syncUser = () => {
  ElMessageBox.confirm(
    '本次同步预计需要1-3分钟时间。此外系统将于每天凌晨自动进行成员同步。',
    '确定要现在同步吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'none',
    }
  )
    .then(async () => {
      proxy.$modal.msgSuccess('正成员同步中')
      try {
        syncLoading.value = true
        await syncUser()
        _checkIsSyncingStatus()
      } catch (error) {
        syncLoading.value = false
        if (intervalId) {
          clearInterval(intervalId);
        }
        console.log(error);
      }

    })
    .catch(() => {
    })
}
const _checkIsSyncingStatus = () => {
  // 设置定时器
  intervalId = setInterval(async () => {
    const res = await getIsSyncing()
    if (res.code === 200) {
      syncLoading.value = res.data
      if (!res.data) {
        proxy.$modal.msgSuccess('同步成功');
        clearInterval(intervalId);
        getList()
        rootNode.value.childNodes = []
        loadNode(rootNode.value, rootResolve.value)
      }
    }
  }, 5000); // 每5秒查询一次
}
const getIsSyncingResult = async () => {
  const res = await getIsSyncing()
  if (res.code === 200) {
    syncLoading.value = res.data
    if (res.data) {
      _checkIsSyncingStatus()
    }
  }
}

const handleBatchDelete = () => {
  ElMessageBox.confirm(
    '请注意，此操作将清除这些成员的所有数据，且不可恢复，请谨慎操作。',
    '批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const array = multipleSelection.value.map((item: userResult) => item.id)
      await deleteUser(array).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('删除成功');
          queryParams.value.pageNum = 1;
          getList();
        }
      });
    })
    .catch(() => {
    })
}


/**冻结*/
const _freezeUser = (id: any) => {
  handleFreezeUser(id)
  queryParams.value.pageNum = 1;
  queryParams.value.status = 2;
  getList();
}

const handleFreezeUser = (id: any) => {
  if (id) {
    ElMessageBox.confirm(
      '确定要解冻该成员吗？',
      '解冻',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        await freezeUser([id]).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('解冻成功');
            queryParams.value.pageNum = 1;
            getList();
          }
        });
      })
      .catch(() => {

      })
  }
}

const handleAudit = (scope: any) => {
  if (scope.row.status === 2) {
    ElMessageBox.confirm(
      '确定要解冻该成员吗？',
      '解冻',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        await freezeUser([scope.row.id]).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('解冻成功');
            queryParams.value.pageNum = 1;
            getList();
          }
        });
      })
      .catch(() => {

      })

  } else {
    ElMessageBox.confirm(
      '请注意，冻结之后该成员账号将无法正常使用。确认要继续执行此操作吗？',
      '冻结',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        await freezeUser([scope.row.id]).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('冻结成功');
            queryParams.value.pageNum = 1;
            getList();
          }
        });
      })
      .catch(() => {

      })
  }


}

const handleRemoveWhiteFlag = async (scope: any) => {
  if (scope.row.whiteFlag) {
    await removeWhiteFlag(scope.row.id).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('移除成功');
        queryParams.value.pageNum = 1;
        getList();
      }
    }).finally(() => {

    });

  } else {
    ElMessageBox.confirm(
      '请注意，冻结之后该成员账号将无法正常使用。确认要继续执行此操作吗？',
      '冻结',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        await freezeUser([scope.row.id]).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('冻结成功');
            queryParams.value.pageNum = 1;
            getList();
          }
        });
      })
      .catch(() => {

      })
  }


}

// const filterHandler = (value: boolean, row: userResult) => {
//   return row.activateFlag === value
// }
// const filterChange = (value: any) => {
//   queryParams.value.activateFlag = value.activateFlag.length === 0 ? null : value.activateFlag[0]
//   queryParams.value.pageNum = 1
//   getList();
// }

const filterCheckboxChange = (value: any) => {
  queryParams.value.activateFlag = value.includes('activated') && value.includes('unactivated') ? null : value.includes('activated') ? true : value.includes('unactivated') ? false : null
  queryParams.value.kanbanFlag = value.includes('kanbanFlag') ? true : null
  queryParams.value.shareFlag = value.includes('shareFlag') ? true : null
  queryParams.value.whiteFlag = value.includes('whiteFlag') ? true : null
  queryParams.value.pageNum = 1
  getList();

}

onMounted(() => {
  getList();
  // _getDeptTreeList();
  if (userStore.cpFlag) {
    getIsSyncingResult();
  }
})

onUnmounted(() => {
  // 清理定时器
  if (intervalId) {
    clearInterval(intervalId);
  }
});
</script>

<style scoped lang="scss">
.export {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;

}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}

.card-header {
  .el-form-item {
    margin-bottom: 0px;
  }
}

.flex {
  display: flex;
  align-items: center;
  gap: 0 10px;
  justify-content: flex-start;

  span {
    width: 20px;
  }
}

.sms {
  width: 100%;
  position: absolute;
  // background-color: hsl(0, 0%, 51%);
  top: 50%;
  left: 0%;
  transform: translateX(12px) translateY(-50%);
}

.success {
  color: var(--el-color-success);
}

.primary {
  color: var(--el-color-primary);
  cursor: pointer;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 70px - 100px);
    overflow-y: auto;
  }
}

.dept-container {
  height: calc(100vh - 70px - 100px - 36px);
  overflow-y: auto;
  overflow-x: auto;
  width: 100%;
}

.content-wrap {
  height: 100%;
  display: flex;
  align-items: flex-start;
  width: auto;

}

.tab-container {
  // flex: 1;
  // width: 100%;
  align-items: flex-start;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.dept-container-wrap {
  -webkit-transition: width 0.28s;
  transition: width 0.28s;
  overflow-x: auto;
  height: 100%;
  max-width: 300px;
}

:deep(.el-tree) {
  // .el-tree-node__content {
  //   display: block !important;
  // }
  display: inline-block;
  min-width: 100%;

  .el-tree-node__children {
    overflow-x: visible !important;
  }
}


.divider {
  height: calc(100vh - 70px - 100px - 18px);
  margin-top: - 18px;
}



@media screen and (max-width: 1350px) {
  .pagination {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    scale: 0.9;
  }
}

.hamburger-container {
  margin-bottom: 10px;
  cursor: pointer;
}
</style>
