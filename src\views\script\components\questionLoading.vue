<template>
  <el-dialog
    v-model="visibleFlag"
    :show-close="status !== 3"
    :width="status === 3 ? '800px' : '300px'"
    :close-on-click-modal="false"
    append-to-body
    :before-close="close"
    align-center
  >
    <template #title>
      <div v-if="status === 3">
        <span class="title-text"> 生成完成，请选择题目</span>
        <div style="font-size: 14px;color: #999999;margin-top: 12px;">如需修改，请选择完后点击确定进行修改</div>
      </div>
    </template>
    <div class="loading-container" v-if="status === 0 || status === 1">
      <Vue3Lottie width="200px" height="200px" :animation-data="loadJson" />
      <span style="font-size: 16px;">题目生成中</span>
      <span class="tips">预计需要1分钟时间，请耐心等待</span>
    </div>
    <div class="loading-container" v-if="status === 2">
      <img :src="failImg" class="icon" />
      <span>生成失败</span>
      <el-button type="primary" @click="reset">重试</el-button>
    </div>
    <div class="question-list-container" v-if="status === 3">
      <el-checkbox-group v-model="checkQuestion">
        <div class="question-container" v-for="(item, index) in questionList" :key="index">
          <el-checkbox :label="item" @change="getQuestion">
            <div class="question-item">
              <div class="question">{{ item.question }}</div>
              <div style="line-height: 20px;">{{ item.answer }}</div>
            </div>
          </el-checkbox>
        </div>
      </el-checkbox-group>
    </div>
    <template #footer>
      <div class="dialog-footer" v-if="status === 3">
        <div class="button-container">
          <el-checkbox v-model="checkAll" label="全选" :indeterminate="isIndeterminateModel" @change="handleCheckAllChange" />&nbsp;&nbsp;&nbsp;
          <el-button @click="reset">重新生成</el-button>
        </div>
        <div class="button-container">
          <el-button @click="close">关 闭</el-button>
          <el-button type="primary" :disabled="checkQuestion.length === 0" @click="handleSubmit">确 定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { aiGenerateQuestionWithFile, checkGenerateQuestionStatus, getGenerateQuestion } from '@/api/script'
import { ScriptQABo } from '@/api/script/types'
import failImg from "@/assets/images/fail.png";
import loadJson from "@/assets/icons/json/loading.json";
const emits = defineEmits(['update:visible', 'sendAiQA'])
const conversationId = ref('')
const chatId = ref('')
const questionList = ref<ScriptQABo[]>([])
const statusName = ref('created')
const checkQuestion = ref<ScriptQABo[]>([])

const checkAll = ref(false)
const isIndeterminateModel = ref(false)


const statusMap = new Map([
  ['created', 0],
  ['in_progress', 0],
  ['completed', 1],
  ['failed', 2],
  ['requires_action', 2],
  ['hasQuestion', 3],
])

const status = computed(() => statusMap.get(statusName.value))
let intervalId: any = null;


// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  fileResult: {
    type: Object,
    default: () => {}
  },
})

watch(() => checkQuestion.value, (val: any) => {
  checkAll.value = (val.length === questionList.value.length) && questionList.value.length > 0
  isIndeterminateModel.value = val.length > 0 && val.length < questionList.value.length;
})

watch(() => props.visible, (val) => {
  if (val) {
    createQuestion()
    // _getGenerateQuestion()
  }
})

// 获取题目
watch(() => status.value, (val) => {
  if (val === 1) {
    // if (intervalId) {
    //   clearInterval(intervalId);
    // }
    _getGenerateQuestion()
  }
})
// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const reset = async () => {
  if (intervalId) {
    clearInterval(intervalId);
  }
  statusName.value = 'created'
  questionList.value = []
  checkQuestion.value = []
  checkAll.value = false
  isIndeterminateModel.value = false
 const res = await aiGenerateQuestionWithFile({files:props.fileResult.currentFile})
 conversationId.value =res.data.conversationId
 chatId.value = res.data.id
 _checkGenerateQuestionStatus()
}

const getQuestion = () => {
  console.log(checkQuestion.value)
}

const handleCheckAllChange = (val: any) => {
  checkQuestion.value = val ? questionList.value : [];
  isIndeterminateModel.value = false;
}

const createQuestion = async () => {
  // const res = await aiGenerateQuestion(props.fileIds as string[])
  conversationId.value = props.fileResult.conversationId
  chatId.value = props.fileResult.id
  _checkGenerateQuestionStatus()
}

//设置定时器
const _checkGenerateQuestionStatus = async () => {
// 设置定时器
intervalId = setInterval(async () => {
    const res = await checkGenerateQuestionStatus({ chatId: chatId.value, conversationId: conversationId.value })
    if (res.code === 200) {
      statusName.value = res.data.status
      if (['failed','requires_action','completed'].includes(res.data.status)) {
        clearInterval(intervalId);
      }
    }
  }, 5000); // 每5秒查询一次
}

const _getGenerateQuestion = async () => {
  const res = await getGenerateQuestion({ chatId: chatId.value, conversationId: conversationId.value })
  if (res.data.length > 0) {
    statusName.value = 'hasQuestion'
    questionList.value = res.data
  } else {
    statusName.value = 'failed'
    questionList.value = []
  }

}

const close = () => {
  ElMessageBox.confirm(
    '未保存的内容将丢失',
    '确定要退出吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      closeDialog()
    })
    .catch(() => {
    })
}
const closeDialog = () => {
  if (intervalId) {
    clearInterval(intervalId);
  }
  statusName.value = 'created'
  questionList.value = []
  checkQuestion.value = []
  checkAll.value = false
  isIndeterminateModel.value = false
  visibleFlag.value = false
}

const handleSubmit = () => {
  if (checkQuestion.value.length > 0) {
    emits('sendAiQA', checkQuestion.value)
    closeDialog()
  }
}

onUnmounted(() => {
  // 清理定时器
  if (intervalId) {
    clearInterval(intervalId);
  }
});
</script>
<style lang="scss" scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px 0;
}

.tips {
  color: #999999;
}

.icon {
  width: 180px;
  height: 180px;
}

.question-item {
  display: flex;
  border: 1px solid var(--el-border-color);
  padding: 8px;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  // margin-bottom: 20px;
  border-radius: 4px;
  gap: 8px 0;
}

.question-container {
  display: flex;
  align-items: center;
  gap: 0 12px;
  padding: 0 32px;
  margin-bottom: 16px;
}

:deee(.el-dialog__body) {
  padding-top: 24px;
  padding-bottom: 24px;
}

.el-checkbox-group {
  width: 100%;

  .el-checkbox {
    width: 100%;
    display: flex;
    align-items: center;
    white-space: normal;
    height: auto;
  }

  :deep(.el-checkbox__label) {
    width: 100%;
    display: block !important;
  }
}

.question {
  font-weight: bold;
  font-size: 16px;
}

.question-list-container {
  width: 100%;
}

.button-container {
  display: flex;
}

.dialog-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between
}

.title-text {
  color: var(--el-text-color-regular);
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: var(--el-text-color-regular);
}
</style>
