<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">E人训练</span>
          <el-button type="primary" icon="Plus" @click="handleAdd">创建E人</el-button>
        </div>
      </template>
      <div class="header">
        <el-tabs v-model="queryParams.status" class="demo-tabs" @tab-click="switchTab">
          <el-tab-pane label="已上线" :name="3"></el-tab-pane>
          <el-tab-pane label="待上线" :name="1"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="eMan-container">
        <div class="list flex" v-if="eManList.length > 0">
          <div class="eMan-item flex column" @click.stop="handleDetail(item)" v-for="(item, index) in eManList" :key="index">
            <div class="top flex">
              <div class="avatar-container">
                <el-image :src="item.avatar || avatar_default_man" class="avatar" fit="cover" lazy />
                <img class="tips-icon" src="@/assets/icons/svg/3d.svg" v-show="item.zipFileUrl&&item.show3dFlag" />
              </div>
              <div class="user flex column">
                <div class="user-name">
                  {{ item.name }}
                  <img src="@/assets/icons/png/eman.png" style="width: 16px;" v-if="item.type === 2" />
                  <Star v-model="item.emanDifficult" readonly size="small" v-if="company === 'kangyuan' && item.type === 1 && item.emanDifficult" />
                </div>
                <div class="user-msg">
                  {{ occupationList.slice(0, 4).includes(item.occupation) ? item.department ? (
                    item.department.includes('-') ?
                      item.department.split('-')[1] : item.department) : '' : item.occupation }}
                  <span v-if="item.title" style="margin: 0 2px;">|</span>
                  {{ item.title ? item.title.includes('-') ? item.title.split('-')[1] : item.title : '' }}
                </div>
              </div>
            </div>
            <div class="mid">擅长方向：{{ item.skill }}</div>
            <div class="bottom flex">
              <div class="left" style="  color: #999999;">创建者：{{ item.creator }}</div>
              <div class="right flex">
                <span @click.stop>
                  <el-popconfirm
                    width="220"
                    v-if="item.status === 3"
                    placement="top"
                    @confirm.stop="confirm(item)"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定下线吗?"
                  >
                    <template #reference>
                      <div>
                        <el-tooltip content="下线" placement="top">
                          <img src="@/assets/icons/png/online.png" class="icon" />
                        </el-tooltip>
                      </div>
                    </template>
                  </el-popconfirm>
                  <el-popconfirm
                    width="220"
                    @confirm.stop="confirm(item)"
                    v-if="item.status === 1"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定上线吗?"
                  >
                    <template #reference>
                      <div>
                        <el-tooltip content="上线" placement="top">
                          <img src="@/assets/icons/png/downline.png" class="icon" />
                        </el-tooltip>
                      </div>
                    </template>
                  </el-popconfirm>
                </span>

                <el-tooltip content="编辑" placement="top">
                  <img src="@/assets/icons/png/edit.png" @click.stop="handleEdit(item)" class="icon" />
                </el-tooltip>
                <span @click.stop>
                  <el-popconfirm
                    width="220"
                    @confirm.stop="confirmDelete(item)"
                    v-if="item.status === 1"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定删除吗?"
                  >
                    <template #reference>
                      <div>
                        <el-tooltip content="删除" placement="top">
                          <img src="@/assets/icons/png/del.png" class="icon" />
                        </el-tooltip>
                      </div>
                    </template>
                  </el-popconfirm>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="empty flex" v-else>
          <el-image :src="emptyEman"></el-image>
          <span>暂无E人</span>
        </div>
      </div>
      <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
    </el-card>
  </div>
  <el-dialog class="choose-type-dialog" v-model="chooseAddDialogVisible" width="800" align-center :show-close="false">
    <template #title>
      <div class="choose-type-head">请选择E人类型</div>
    </template>
    <div class="choose-type-container">
      <el-row :gutter="25">
        <el-col :span="12">
          <div class="choose-type-box" @click="chooseAddType(1)">
            <img class="choose-type-icon" src="@/assets/icons/png/eman2.png" />
            <el-button type="primary" size="large" class="choose-type-title">情景演练</el-button>
            <div class="choose-type-tip" type="info">适合练习场景，如沉浸式RolePlay、答题等练习角色</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="choose-type-box" @click="chooseAddType(2)">
            <img class="choose-type-icon" src="@/assets/icons/png/eman1.png" />
            <el-button type="primary" size="large" class="choose-type-title">企业智脑</el-button>
            <div class="choose-type-tip" type="info">适合学习场景，如学术经理、顾问等咨询角色</div>
          </div>
        </el-col>
      </el-row>

      <img class="choose-type-close" @click="closeAddTypeDialog" src="@/assets/images/icon_cancel.svg" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
// const userStore = useUserStore();
import { ElLoading } from 'element-plus'
import avatar_default_man from "@/assets/images/avatar_default_man.jpg";
import emptyEman from "@/assets/icons/png/empty-eman.png";


import { EmanQuery, EmanDetailVo } from "@/api/eman/types";
import { getEManList, onlineEMan, offlinEMan, deleteEMan } from "@/api/eman";
import { occupationList } from './components/data';

import type { TabsPaneContext } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const company = import.meta.env.VITE_APP_COMPANY;
const emits = defineEmits(['change-type']);
const props = defineProps({
  listType: {
    type: Number,
    default: null
  }
})
const userStore = useUserStore();
const data = reactive<PageQueryData<EmanQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 50,
    status: props.listType as unknown as number || 3,
  },
});
const { queryParams } = toRefs(data);
const eManList = ref<EmanDetailVo[]>([])
const total = ref(0)
const chooseAddDialogVisible = ref(false);

/** 查询列表 */
const getList = async () => {
  const loadingInstance = ElLoading.service({ lock: true, text: 'Loading', background: 'rgba(255, 255, 255, 0.4)' })
  const res = await getEManList(queryParams.value);
  nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
    loadingInstance.close()
  })
  eManList.value = res.rows;
  total.value = res.total;
}
/**上下线 */
const confirm = async (item: EmanDetailVo) => {
  if (item.status === 1) {
    await onlineEMan(item.id)
    proxy.$modal.msgSuccess('上线成功');
    getList()
  } else if (item.status === 3) {
    await offlinEMan(item.id)
    proxy.$modal.msgSuccess('下线成功');
    getList()
  }
}
/**删除 */
const confirmDelete = async (item: EmanDetailVo) => {
  await deleteEMan(item.id)
  proxy.$modal.msgSuccess('删除成功');
  getList()
}

/**跳转添加 */
const handleAdd = () => {
  chooseAddDialogVisible.value = true;
  // emits('change-type', { flagType: 'add', listType: queryParams.value.status })
}
/**切换tab状态 */
const switchTab = (tab: TabsPaneContext) => {
  queryParams.value.status = tab.props.name as number
  queryParams.value.pageNum = 1;
  getList();
}
/**详情 */
const handleDetail = (item: EmanDetailVo) => {
  emits('change-type', { flagType: 'detail', isEdit: false, id: item.id, listType: queryParams.value.status })
}

/**编辑 */
const handleEdit = (item: EmanDetailVo) => {
  emits('change-type', { flagType: 'detail', isEdit: true, id: item.id, listType: queryParams.value.status })
}
const chooseAddType = (type: number) => {
  emits('change-type', { flagType: 'add', listType: queryParams.value.status, emanType: type })
}
const closeAddTypeDialog = () => {
  chooseAddDialogVisible.value = false;
}

onMounted(() => {
  getList();
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}

.flex {
  display: flex;
}

.column {
  flex-direction: column;
}

:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 70px - 100px);
    overflow-y: auto;
  }
}

.eMan-container {
  width: 100%;

  .list {
    gap: 1vw;
    flex-wrap: wrap;
    box-sizing: border-box;
    margin-top: 0.5vw;
  }

  .empty {
    flex-direction: column;
    align-items: center;
    color: #999999;
    margin-top: 15vh;

    .el-image {
      width: 10%;
    }
  }

  .eMan-item {
    cursor: pointer;
    width: 32.4%;
    box-shadow: 0 0 10px 1px #dddddd;
    box-sizing: border-box;
    padding: 16px;
    border-radius: 12px;
    font-size: 14px;
    border: 2px solid #fff;
    gap: 24px 0;

    &:hover {
      border: 2px solid #4F66FF;
    }

    .top {
      gap: 0 12px;
    }

    .user {
      height: 56px;
      justify-content: center;
      gap: 6px 0;

      &-name {
        font-weight: bold;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 0 4px;
      }
    }

    .mid {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #999999;
      height: 3.0em;
      /* 设置为行高的两倍 */
      line-height: 1.5em;
      /* 这里的行高应与下面p标签中的行高一致 */
      height: 3.0em;
    }

    .bottom {
      border-top: 1px solid #dddddd;
      padding-top: 24px;
      justify-content: space-between;

      .right {
        gap: 0 8px;
      }
    }

    .icon {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
  }
}

.avatar-container {
  position: relative;

  .avatar {
    width: 56px;
    height: 56px;
    border-radius: 4px;
  }

  .tips-icon {
    position: absolute;
    bottom: 8px;
    right: 4px;
    width: 16px;
    z-index: 1;
    height: 16px;
  }
}


:global(.choose-type-dialog) {
  background: none;
  box-shadow: none;
}

.choose-type {
  &-head {
    color: #fff;
    text-align: center;
    font-size: 24px;
  }

  &-content {
    cursor: pointer;
  }

  &-container {
    width: 80%;
    margin: 0 auto;
  }

  &-box {
    width: 280px;
    height: 310px;
    border-radius: 9px;
    text-align: center;
    background-color: #fff;
    overflow: hidden;
  }

  &-icon {
    width: 158.5px;
    height: 146px;
    display: block;
    margin: 30px auto 17px;
  }

  &-title {
    font-size: 20px;
    font-weight: 500;
    width: 187px;
  }

  &-tip {
    font-size: 13px;
    margin-top: 20px;
    margin-left: 46px;
    margin-right: 46px;
  }

  &-close {
    display: block;
    width: 32px;
    height: 32px;
    margin: 68px auto 0;
    cursor: pointer;
  }
}
</style>
