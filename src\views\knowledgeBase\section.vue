<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            {{ rowName }}</span
          >
        </div>
      </template>
      <div class="box-container">
        <div class="tips">以下文件是经过知识库处理的知识切片，格式内容可能与原始文件有轻微差异，文件中的图片暂不支持识别。</div>
        <div v-for="(item, index) in splitList" :key="index" class="content">
          {{ item }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getItemSplitList } from '@/api/knowledgeBase';
// import router from '@/router';
// import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const splitList = ref<string[]>([]);
const emits = defineEmits(['change-type']);
const props = defineProps({
    id: {
        type: String,
        default: ''
    },
    rowName: {
        type: String,
        default: ''
    },
    preId: {
        type: String,
        default: ''
    },
    preRowName: {
        type: String,
        default: ''
    },
})


/** 查询列表 */
const getList = async () => {
    proxy?.$modal.loading("正在加载");
    const res = await getItemSplitList(props.id);
    splitList.value = res.data
    proxy?.$modal.closeLoading();
}



const back = () => {
    emits('change-type', { flagType: 'item', id: props.preId,rowName:props.preRowName })
}





onMounted(() => {
    getList();
})
</script>
<style scoped lang="scss">
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

:deep(.el-tabs) {
    .el-tabs__nav-wrap::after {
        display: none
    }
}



.card-header {
    .el-form-item {
        margin-bottom: 0px;
    }
}

.flex {
    display: flex;
    align-items: center;
    gap: 0 10px;
    justify-content: flex-start;
}

.box-container{
    display: flex;
    flex-direction: column;
    width: 100%;
}
.tips{
  color: #999999;
  margin: 20px 0;
  font-size: 14px;
}
.content {
    width: 100%;
    // white-space: pre-wrap;
    word-break: break-all;
}
:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 70px - 100px);
    overflow-y: auto;
  }
}
</style>
