<template>
  <el-config-provider :locale="appStore.locale" :size="size">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import useAppStore from '@/store/modules/app';
import { SettingTypeEnum } from './enums/SettingTypeEnum';

const appStore = useAppStore();
const settingsStore = useSettingsStore()
const sideTheme = ref(settingsStore.sideTheme);
const size = computed(() => appStore.size as any);
const isDark = useDark({
  storageKey: 'useDarkKey',
  valueDark: 'dark',
  valueLight: 'light',
});
const toggleDark = () => useToggle(isDark);

if(isDark.value) {
  const toggle = toggleDark()
  toggle(false)
  settingsStore.changeSetting({ key: SettingTypeEnum.SIDE_THEME, value: sideTheme.value })
}

onMounted(() => {


  // nextTick(() => {
  //   // 初始化主题样式
  //   handleThemeStyle(useSettingsStore().theme)
  // })
})
</script>
