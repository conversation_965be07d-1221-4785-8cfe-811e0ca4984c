<template>
  <el-dialog v-model="visibleDialog" width="750px" append-to-body>
    <template #title>
      <div>首页设置</div>
      <div class="sub-title">设置小程序默认进入的页面</div>
    </template>
    <div class="dialog-content">
      <div class="select-box">
        <div class="select-item">
          <div class="select-item-name">AI陪练</div>
          <div class="select-item-preview" :class="{ active: type === 'AI_PRACTISE'}" @click="changeType('AI_PRACTISE')">
            <img class="select-item-img" src="@/assets/images/weapp_index_prictice.jpg" alt="AI陪练" />
          </div>
        </div>

        <div class="select-item">
          <div class="select-item-name">对话页面</div>
          <div class="select-item-preview" :class="{active: type === 'CHAT_PAGE'}" @click="changeType('CHAT_PAGE')">
            <img class="select-item-img" src="@/assets/images/weapp_index_chat.jpg" alt="对话页面" />
            <div class="select-item-default">默认</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  value: {
    type: String,
    default: ''
  }
})
const emits = defineEmits(['update:visible', 'onSuccess'])

const type = ref()

// 弹窗组件显示隐藏
const visibleDialog = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const closeDialog = () => {
  visibleDialog.value = false
}
const changeType = (val:string) => {
  console.log('chageType', val)
  type.value = val
}

const handleSubmit = () => {
  closeDialog()
  emits('onSuccess', 'indexPage', type)
}
watch(() => props.visible, (val) => {
  if(val) {
    type.value = props.value
  }
})
</script>

<style scoped lang="scss">
@import "./style";
</style>
