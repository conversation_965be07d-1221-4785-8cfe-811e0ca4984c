import request from '@/utils/request';
import {
  knowledgeListQuery,
  KnowledgeBaseVo,
  KnowledgeBaseBo,
  knowledgeItemListQuery,
  KnowledgeBaseItemVo,
  KnowledgeBaseItemLabelInfoBo
} from './types';
import { AxiosPromise } from 'axios';
// 查询列表
export function getKnowledgeBaseList(query: knowledgeListQuery): AxiosPromise<KnowledgeBaseVo[]> {
  return request({
    url: '/biz/KnowledgeBase/queryKnowledgeBaseList',
    method: 'get',
    params: query
  });
}

// 新增
export function createKnowledgeBase(data: KnowledgeBaseBo): AxiosPromise<string> {
  return request({
    url: '/biz/KnowledgeBase/createKnowledgeBase',
    method: 'post',
    data
  });
}

//删除
export function removeKnowledgeBase(id: string | number): AxiosPromise<string> {
  return request({
    url: '/biz/KnowledgeBase/removeKnowledgeBase/' + id,
    method: 'post'
  });
}

//更新
export function updateKnowledgeBase(data: KnowledgeBaseBo): AxiosPromise<string> {
  return request({
    url: '/biz/KnowledgeBase/updateKnowledgeBase',
    method: 'post',
    data
  });
}
//内容列表
export function queryKnowledgeBaseItemList(query: knowledgeItemListQuery): AxiosPromise<KnowledgeBaseItemVo[]> {
  return request({
    url: '/biz/KnowledgeBase/queryKnowledgeBaseItemList',
    method: 'get',
    params: query
  });
}

//删除知识库内容
export function removeKnowledgeBaseItem(id: string | number): AxiosPromise<string> {
  return request({
    url: '/biz/KnowledgeBase/removeKnowledgeBaseItem/' + id,
    method: 'post'
  });
}

//内容列表
export function getKnowledgeBaseDetail(id: string | number): AxiosPromise<KnowledgeBaseVo> {
  return request({
    url: '/biz/KnowledgeBase/detail/' + id,
    method: 'get'
  });
}

export function uploadKnowledgeItem(data: { id: string; ossIds: string[] }): AxiosPromise<string> {
  return request({
    url: '/biz/KnowledgeBase/uploadItem',
    method: 'post',
    data
  });
}

//切片列表
export function getItemSplitList(id: string | number): AxiosPromise<string[]> {
  return request({
    url: '/biz/KnowledgeBase/getItemSplitList/' + id,
    method: 'get'
  });
}

//切片列表
export function getItemDetail(id: string | number): AxiosPromise<KnowledgeBaseItemVo> {
  return request({
    url: '/biz/KnowledgeBase/itemDetail/' + id,
    method: 'get'
  });
}
// 人工标注
export function manualLabel(data: KnowledgeBaseItemLabelInfoBo): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/manualLabel',
    method: 'post',
    data
  });
}

export function retryKnowledgeBase(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/retry/' + id,
    method: 'post'
  });
}
export function autoLabel(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/autoLabel/' + id,
    method: 'post'
  });
}
// 上传音频
export function uploadVoice(data: { kbItemId: string; ossId: string; mergeMethod: number }): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/uploadVoice',
    method: 'post',
    data
  });
}
// 详情
export function getVoiceDetail(query: { id: string }): AxiosPromise<KnowledgeBaseItemVoiceVo> {
  return request({
    url: '/biz/KnowledgeBase/getItemVoice',
    method: 'get',
    params: query
  });
}

// 删除
export function removeKnowledgeBaseItemVoice(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/removeKnowledgeBaseItemVoice/' + id,
    method: 'post'
  });
}
// 批量AI标注
export function batchAutoLabel(data: { kbItemIds: string[]; mergeMethod: number }): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/batchAutoLabel',
    method: 'post',
    data
  });
}
// 批量AI润色
export function batchColor(data: { kbItemIds: string[] }): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/batchColor',
    method: 'post',
    data
  });
}
// 批量处理音频
export function batchHandleVoice(data: { kbItemIds: string[]; mergeMethod: number }): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/batchHandleVoice',
    method: 'post',
    data
  });
}

export function batchRemoveKnowledgeBaseItem(data: string[]): AxiosPromise<any> {
  return request({
    url: '/biz/KnowledgeBase/batchRemoveKnowledgeBaseItem',
    method: 'post',
    data
  });
}
