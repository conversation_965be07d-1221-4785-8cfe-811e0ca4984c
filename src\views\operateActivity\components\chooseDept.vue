<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" label-width="120px" v-loading="loading">
          <el-row :gutter="24">
            <el-col :span="24">
              <div class="dept-container">
                <el-tree
                  show-checkbox
                  @check-change="checkChange"
                  ref="treeRef"
                  class="filter-tree"
                  node-key="id"
                  :default-expanded-keys="defaultExpandedKeys"
                  :data="deptOptions"
                  check-strictly
                  :default-checked-keys="formParams.deptIds"
                  :props="defaultProps"
                  :filter-node-method="filterNode"
                />
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :disabled="formParams.deptIds.length === 0">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">

import { TreeLong } from "@/api/department/types";
import { deepClone } from "@/utils";
import { ElMessageBox } from 'element-plus'
// const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import type { ElTree } from 'element-plus'
// import { de } from "element-plus/es/locale";
const emits = defineEmits(['update:visible', 'update:deptIds', 'update:deptNames', 'success'])
const formParamsRef = ref()
// const btnLoading = ref(false)
const defaultExpandedKeys = ref<string[]>([])
const loading = ref(false)
const deptOptions = ref<TreeLong[]>([])
const initForm = {
    deptIds: [] as string[],
    deptNames: [] as string[],
}
const defaultProps = {
    children: 'children',
    label: 'label',
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()
const formParams = ref(initForm)
const oldFromInfo = ref({})
interface Tree {
    [key: string]: any
}

watch(() => props.visible, val => {
    if (val) {
        deptOptions.value = props.parentDeptOptions as unknown as TreeLong[]
        defaultExpandedKeys.value = deptIds.value.length>0?deptIds.value as string[]: deptOptions.value.length > 0 ? [ deptOptions.value[0].id] : []
        formParams.value.deptIds = deptIds.value as string[]
        formParams.value.deptNames = deptNames.value as string[]
        oldFromInfo.value = deepClone(formParams.value)
    }
})


// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    deptIds: {
        type: Array,
        default: () => []
    },
    deptNames: {
        type: Array,
        default: () => []
    },
    title: {
        type: String,
        default: '参与部门'
    },
    parentDeptOptions: {
        type: Array,
        default: () => []
    },
})




// 弹窗组件显示隐藏
const visibleAdd = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})


// 弹窗组件显示隐藏
const deptIds = computed({
    get() {
        return props.deptIds
    },
    set(value) {
        emits('update:deptIds', value)
    }
})

// 弹窗组件显示隐藏
const deptNames = computed({
    get() {
        return props.deptNames
    },
    set(value) {
        emits('update:deptNames', value)
    }
})


watch(filterText, (val) => {
    treeRef.value!.filter(val)
})
// 筛选部门
const filterNode = (value: string, data: Tree) => {
    if (!value) return true
    return data.label.includes(value)
}

const checkChange = (node, checked) => {

  const setChecked = (arr, ifChecked) => {
    arr?.map((item) => {
      treeRef.value.setChecked(item.id, ifChecked);
      if (item?.children?.length) {
        setChecked(item?.children, ifChecked);
      }
    });
  };
  //如果为取消
  if (checked === false) {
    //如果当前节点有子集
    //循环子集将他们的选中取消
    //修复子父级勾选子集勾选问题
    setChecked(node?.children ?? [], false);
  } else {
    //否则(为选中状态)
    //判断父节点packageType 是否为First
    setChecked(node?.children ?? [], true);
  }

    const array = treeRef.value!.getCheckedNodes()
    formParams.value.deptIds = array.map((item: any) => item.id)
    formParams.value.deptNames = array.map((item: any) => item.label)
}


const back = () => {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
        if (typeof formParams.value[key] !== 'object') {
            console.log(JSON.stringify(String(formParams.value[key])), JSON.stringify(String(oldFromInfo.value[key])))
            if (
                JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
            ) {
                bool = true;
                return;
            }
        } else {
            if (
                JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
            ) {
                bool = true;
                return;
            }
        }

    });
    if (bool) {
        ElMessageBox.confirm(
            '未保存的内容将丢失',
            '确定要返回吗？',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async () => {
                closeDialog()
            })
            .catch(() => {
            })
    } else {
        closeDialog()
    }
}



const closeDialog = () => {
    loading.value = true
    formParamsRef.value.resetFields()
    formParams.value = initForm
    loading.value = false
    visibleAdd.value = false
}

const handleSubmit = () => {
    deptIds.value = formParams.value.deptIds
    deptNames.value = formParams.value.deptNames
    closeDialog()
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;

    .box-title {
        color: #272C47;
        font-size: 20px;
        margin: 18px 0;
        font-weight: bold;

    }
}

.tips {
    color: #999999;
    margin-left: 16px;
}

:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}

.dept-container {
    // margin: 30px;
    // box-sizing: border-box;
    width: 80%;
    margin: 0 auto;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    height: 50vh;
    // position: relative;
    // display: flex;
    padding: 8px 0;
    overflow-y: auto;

    .left {
        width: 100%;
    }
}

.divider {
    // height: 100%;
    // width: 1px;
    // background-color: var(--el-border-color);
}
</style>
