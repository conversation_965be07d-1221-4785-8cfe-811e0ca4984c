<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" :close-on-click-modal="false" append-to-body :show-close="false">
      <div class="box-container" v-loading="loading">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="上级部门" prop="parentId" v-if="formParams.parentId!==0">
                <el-tree-select
                  style="width: 100%;"
                  v-model="formParams.parentId"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  placeholder="选择上级部门"
                  check-strictly
                  :default-expand-all="true"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="部门名称" prop="deptName">
                <el-input v-model="formParams.deptName" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- <div class="box-container" v-else>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">上级部门</div>
              <div class="right">
                <span>{{ oldFromInfo.deptName }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">姓名</div>
              <div class="right">
                <span>{{ oldFromInfo.deptName }}</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="18">
            <div class="user-msg">
              <div class="left">添加时间</div>
              <div class="right">
                <span v-formatTime="oldFromInfo.createTime"></span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">关闭</el-button>
          <el-button type="primary" v-if="isEdit" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { reactive, ref } from 'vue'
import { getDeptTreeList, updateDept, checkNameUnique } from "@/api/department/index"
import { TreeLong, DeptFormVo, DeptVo } from "@/api/department/types";
import { ElMessageBox } from 'element-plus'
import {departmentTreeDisable} from '@/utils'
import { useUserStore } from '@/store/modules/user';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success'])
const userStore = useUserStore();
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '查看'
  },
})
const initForm = {
  deptName: '',
  parentId: ''
}

const initFormParams = (val: DeptVo) => {
  formParams.value = { id: val.id, parentId: val.parentId, deptName: val.deptName };
  oldFromInfo.value = { id: val.id, parentId: val.parentId, deptName: val.deptName };
};

defineExpose({
  initFormParams,
});

const formParams = ref<DeptFormVo>(initForm)
import type { FormRules } from 'element-plus'
interface RuleForm {
  deptName: string
  parentId: string
}

const loading = ref(false)

const checkTextLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('名称不能超过64个字符'))
  } else {
    callback()
  }
}

const checkSameNameLimit = async (rule: any, value: any, callback: any) => {
  if (formParams.value.deptName.length > 0 && formParams.value.parentId) {
    const res = await checkNameUnique({ name: formParams.value.deptName, id: formParams.value.id as string ,parentId:formParams.value.parentId})
    if (!res.data) {
      callback(new Error('部门已存在'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}


// const editFlag = ref(props.isEdit)
const btnLoading = ref(false)
const oldFromInfo = ref<DeptVo>({} as DeptVo)
const deptOptions = ref<TreeLong[]>([])

watch(() => props.visible, val => {
  if (val) {
    console.log(props.isEdit)
    _getDeptTreeList()
  }
})


const _getDeptTreeList = async () => {
  loading.value = true
  const res = await getDeptTreeList()
  if (res.data) {
    deptOptions.value = userStore.cpFlag ? departmentTreeDisable(res.data) : res.data
  }
  loading.value = false
}

const formParamsRef = ref()

// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const rules = reactive<FormRules<RuleForm>>({
  deptName: [
    { required: true, message: '部门名称不能为空', trigger: 'blur' },
    {
      validator: checkTextLimit,
      trigger: 'blur'
    },
    {
      validator: checkSameNameLimit,
      trigger: 'blur'
    },
  ],
  parentId: [
    { required: true, message: '请选择上级部门', trigger: ['blur', 'change'] },
  ],
})
const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
      ) {
        bool = true;
        return;
      }
    }

  });

  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}



const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      await updateDept(formParams.value).finally(() => {
          btnLoading.value = false;
        });

        proxy.$modal.msgSuccess('保存成功');
        closeDialog()
        emits('success')
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}

const closeDialog = () => {
  formParamsRef.value.resetFields()
  formParams.value = initForm
  visibleAdd.value = false
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.user-msg {
  margin-left: 32px;
  display: flex;
  align-items: flex-start;
  // margin-bottom: 24px;

  .left {
    width: 100px;
    margin-right: 16px;
    font-weight: bold;
  }

  .right {
    flex: 1;
  }
}


:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}
</style>
