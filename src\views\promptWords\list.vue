<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">提示词管理</span>
        </div>
      </template>
      <el-table v-loading="loading" :data="promptTemplateList" @row-click="handleEdit">
        <!-- <el-table-column label="编号" align="center" type="index" width="150" /> -->
        <!-- <el-table-column type="selection" width="55" align="left" /> -->
        <el-table-column label="名称" align="left" prop="name" />

        <!-- <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span v-formatTime="scope.row.createTime"></span>
          </template>
        </el-table-column> -->
        <el-table-column label="调试" align="left" prop="checkFlag" width="100px">
          <template #default="scope">
            <div class="flex">
              <div class="dot" v-if="scope.row.checkFlag !== null" :style="{ backgroundColor: colorMap.get(scope.row.checkFlag) }"></div>
              {{ scope.row.checkFlag === null ? '待调试' : statusMap.get(scope.row.checkFlag) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="left" prop="onlineFlag" width="150px">
          <template #default="scope">
            <div v-if="!scope.row.onlineFlag">未上线</div>
            <div v-else-if="scope.row.newPrompt&&scope.row.onlineFlag">有修改未发布</div>
            <div v-else>已发布</div>
          </template>
        </el-table-column>

        <el-table-column label="编辑时间" align="left" prop="updateTime">
          <template #default="scope">
            <span v-formatTime="scope.row.updateTime"></span>
          </template>
        </el-table-column>
        <el-table-column width="200" label="操作" align="left" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="gap-12">
              <el-button link type="primary" @click.stop="handleEdit(scope.row)" text>编辑</el-button>
              <el-button
                link
                type="primary"
                :disabled="!(scope.row.checkFlag &&( (scope.row.newPrompt&&scope.row.onlineFlag)||!scope.row.onlineFlag))"
                @click.stop="onLineFn(scope.row)"
                text
                >发布</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination
        v-show="total > 0"
        v-if="queryParams.status !== 4"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      /> -->
      <el-dialog
        ref="formDialogRef"
        title="发布"
        v-model="visibleFlag"
        width="600px"
        append-to-body
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form ref="formParamsRef" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="更新内容" prop="content">
                <el-input
                  v-model="form.content"
                  placeholder="请输入"
                  show-word-limit
                  :autosize="{ minRows: 8, maxRows: 10 }"
                  maxlength="150"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getPromptTemplate, onlinePrompt } from '@/api/prompt';
import { PromptTemplateVo } from '@/api/prompt/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import type { FormRules } from 'element-plus'
const promptTemplateList = ref<PromptTemplateVo[]>([]);
const loading = ref(true);
const btnLoading = ref(false);
const formParamsRef = ref()
const visibleFlag = ref(false)
const currentRowId = ref('')
// const total = ref(0);
const emits = defineEmits(['change-type']);
const statusMap = new Map([
  [true, '正常'],
  [false, '异常']
])
const colorMap = new Map([
  [true, '#2DDF29'],
  [false, '#FF1C1C']
])
const form = ref({
  content: ''
})
const rules = reactive<FormRules<{ content: string }>>({
  content: [
    { required: true, message: '请输入更新内容', trigger: 'blur' },
    {
      min: 1,
      max: 150,
      message: '最多150个字符',
      trigger: ["blur"]
    }
  ],
})

/**编辑 */
const handleEdit = (row: any) => {
  emits('change-type', { flagType: 'detail', isEdit: true, id: row.id })
}
/**发布 */
const onLineFn = (row: any) => {
  currentRowId.value = row.id
  visibleFlag.value = true
}
const closeDialog = () => {
  currentRowId.value = ''
  formParamsRef.value.resetFields()
  formParamsRef.value.content = ''
  visibleFlag.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      btnLoading.value = true
      await onlinePrompt({ id: currentRowId.value, content: form.value.content }).then(() => {
        proxy.$modal.msgSuccess('发布成功');
        closeDialog()
        getList()
      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await getPromptTemplate();
  promptTemplateList.value = res.data;
  loading.value = false;
}



onMounted(() => {
  getList();
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}

:global(.script-type-dialog) {
  background: none;
  box-shadow: none;
}

.script-type {
  &-head {
    color: #fff;
    text-align: center;
    font-size: 24px;
  }

  &-content {
    cursor: pointer;
  }

  &-container {
    width: 80%;
    margin: 0 auto;
  }

  &-box {
    width: 280px;
    height: 310px;
    border-radius: 9px;
    text-align: center;
    background-color: #fff;
    overflow: hidden;
  }

  &-icon {
    width: 93px;
    height: 93px;
    display: block;
    margin: 40px auto 17px;
  }

  &-title {
    font-size: 20px;
    font-weight: 500;
    width: 187px;
  }

  &-tip {
    font-size: 15px;
    margin-top: 20px;
    margin-left: 46px;
    margin-right: 46px;
  }

  &-close {
    display: block;
    width: 32px;
    height: 32px;
    margin: 68px auto 0;
    cursor: pointer;
  }
}

:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 90px - 100px);
  }
}

:deep(.el-table__row) {
  cursor: pointer;
}

.gap-12 {
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  gap: 0 12px;
}

.flex {
  display: flex;
  align-items: center;
}

.dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  margin-right: 8px;
}
</style>
