import dayjs from 'dayjs';

export default {
  beforeMount(el: any, { value }: any) {
    if (value) {
      if (typeof value === 'string') {
        const format = 'MM月DD日 HH:mm';
        if (dayjs(value).isValid()) {
          if (dayjs(value).isSame(dayjs(), 'year')) {
            el.innerText = dayjs(value).format(format);
          } else {
            el.innerText = dayjs(value).format('YYYY年' + format);
          }
        } else {
          el.innerText = '';
        }
      } else {
        const { date, format = 'MM月DD日 HH:mm', fix = '' } = value;
        if (dayjs(date).isValid()) {
          if (dayjs(date).isSame(dayjs(), 'year')) {
            el.innerText = dayjs(date).format(format);
          } else {
            el.innerText = dayjs(date).format('YYYY年' + format);
          }
        } else {
          el.innerText = fix;
        }
      }
    } else {
      el.innerText = '';
    }
  },
  beforeUpdate(el: any, { value }: any) {
    if (value) {
      if (typeof value === 'string') {
        const format = 'MM月DD日 HH:mm';
        if (dayjs(value).isValid()) {
          if (dayjs(value).isSame(dayjs(), 'year')) {
            el.innerText = dayjs(value).format(format);
          } else {
            el.innerText = dayjs(value).format('YYYY年' + format);
          }
        } else {
          el.innerText = '';
        }
      } else {
        const { date, format = 'MM月DD日 HH:mm', fix = '' } = value;
        if (dayjs(date).isValid()) {
          if (dayjs(date).isSame(dayjs(), 'year')) {
            el.innerText = dayjs(date).format(format);
          } else {
            el.innerText = dayjs(date).format('YYYY年' + format);
          }
        } else {
          el.innerText = fix;
        }
      }
    } else {
      el.innerText = '';
    }
  }
};
