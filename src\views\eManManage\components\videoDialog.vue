<template>
  <div>
    <el-dialog v-model="visibleDialog" width="700" align-center :show-close="false">
      <template #title>
        {{ title }}
        <div class="tips">视频仅供参考，实际效果以小程序为准。</div>
      </template>
      <div class="video-container">
        <video ref="videoPlayer" v-if="visibleDialog" :controls="controls" :autoplay="autoplay" :loop="loop" style="height: 60vh;">
          <source :src="videoSource" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog" type="primary">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
const emits = defineEmits(['update:visible',])
const videoSource = ref(''); // 视频文件路径
const videoPlayer = ref();
const controls = ref(true); // 是否显示视频控件
const autoplay = ref(false); // 是否自动播放
const loop = ref(false); // 是否循环播放
import { ElLoading } from 'element-plus'


// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '3D形象预览'
    },
    videoUrl: {
        type: String,
        default: ''
    },
})

watch(() => props.visible, val => {
    if (val) {
        nextTick(() => {
            const loadingInstance = ElLoading.service({
                lock: true,
                text: '加载视频中',
                target: '.video-container',
            })
            if (videoPlayer.value) {
                videoSource.value = props.videoUrl
                loadingInstance.close()
                videoPlayer!.value.play();
            }else{
                loadingInstance.close()
            }
        })
    }
})

const closeDialog = () => {
    if (videoPlayer.value) {
        videoPlayer.value.pause();
        videoPlayer.value.currentTime = 0; // 可选：将视频 currentTime 重置为 0
        videoSource.value = ''
        videoPlayer.value = null
    }
    visibleDialog.value = false
}




// 弹窗组件显示隐藏
const visibleDialog = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})
onBeforeMount(() => {
    if (videoPlayer.value) {
        videoPlayer.value.destroy();
        videoPlayer.value.currentTime = 0; // 可选：将视频 currentTime 重置为 0
    }
})
</script>

<style scoped lang="scss">
.video-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.tips {
    margin-top: 12px;
    color: #919090;
    font-size: 14px;
}
</style>
