<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            创建闯关</span
          >
          <div class="right-button">
            <el-button plain type="info" @click="back()">取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" ref="scrollDiv">
        <el-form ref="formParamsRef" v-loading="formLoading" :model="formParams" :rules="rules" label-width="90px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="9">
              <el-form-item label="活动时间" prop="timeArray">
                <el-date-picker
                  v-model="formParams.timeArray"
                  type="daterange"
                  range-separator="-"
                  ::clearable="false"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="margin-right: 12px;width: 280px;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="参与部门" prop="deptIds">
                <div class="flex" style="align-items: flex-start;">
                  <el-button @click="showChooseDeptFn">选择</el-button>
                  <div class="wrapper">
                    <input id="exp1" class="exp" type="checkbox" />
                    <div class="text">
                      <label class="btn" for="exp1"></label>
                      {{ deptOptions.length > 0 && deptOptions[0].label ===
                        formParams.deptNames[0] ?
                        formParams.deptNames[0] : formParams.deptNames.join("、") }}
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />
          <div class="qa" v-for="(it, i) in formParams.levelStages" :key="i">
            <el-row :gutter="24" class="flex" style="display: flex;align-items: flex-start;" v-for="(item, index) in it.detail" :key="index">
              <el-col :span="10">
                <el-form-item :label="`关卡${i + 1}`" :prop="'levelStages.' + i + '.detail.' + index + '.scriptId'" :rules="setRules.scriptId">
                  <el-select v-model="item.scriptId" placeholder="请选择">
                    <template #label="{ label }">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(Number(label.slice(0, 1))) }"
                        ></div>
                        <span>{{ label.slice(1, label.length) }}</span>
                      </div>
                    </template>
                    <el-option v-for="it in scriptOptions" :key="it.id" :label="it.status + it.name" :value="it.id">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(it.status) }"
                        ></div>
                        {{ it.name }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :label="`通关条件`" :prop="'levelStages.' + i + '.detail.' + index + '.conditionText'" :rules="setRules.conditionText">
                  <div class="condition">
                    <span v-if="item.conditionText === ''">待设置</span>
                    <span v-else>{{ item.conditionText }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="`开启时间`" :prop="'levelStages.' + i + '.openTime'" :rules="setRules.openTime">
                  <div class="condition">
                    <span v-if="it.openTime === ''">待设置</span>
                    <span v-else>{{ Dayjs(it.openTime).format('YYYY.MM.DD HH:mm') }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <!-- <el-icon color="#dddddd" v-if="index === 0 && formParams.qaList.length === 1">
                <Delete />
              </el-icon> -->
              <div class="delete">
                <el-tooltip content="设置条件" placement="top">
                  <img src="@/assets/icons/png/setting.png" @click="handleAddCondition(i, index)" />
                </el-tooltip>

                <el-popover
                  trigger="click"
                  v-if="formParams.levelStages.length > 1"
                  placement="top"
                  :ref="(el: refItem) => handleSetPopMap(el, it.popId)"
                  :width="300"
                  popper-class="popover"
                >
                  <div style="display: flex;flex-direction: column;gap:8px 0;">
                    <span class="title" style="font-size: 16px;">确定删除此关卡吗？</span>
                    <!-- <span class="message">删除后此关卡的脚本无需解锁即可使用，脚本解锁状态将重置。</span> -->
                  </div>
                  <div style="text-align: right; margin: 0;margin-top: 30px;">
                    <el-button @click="cancelRemove(it.popId)">取消</el-button>
                    <el-button type="primary" @click="deleteItem(it.popId, item, i)">确定</el-button>
                  </div>
                  <template #reference>
                    <el-icon>
                      <img src="@/assets/icons/png/delete.png" @click="showDelete(it.popId, item, i)" />
                    </el-icon>
                  </template>
                </el-popover>
              </div>
            </el-row>
          </div>
          <el-button type="primary" icon="Plus" @click="handleAdd">添加关卡</el-button>
        </el-form>
      </div>
      <SetCondition
        :script-id="currentScriptId"
        :current-condition="currentConditionArray"
        :current-open-time="currentOpenTime"
        @success="setConditionFn"
        v-model:visible="showLevelDialog"
        title="通关设置"
      />
      <ChooseDept
        :parent-dept-options="deptOptions"
        v-model:deptIds="formParams.deptIds"
        v-model:deptNames="formParams.deptNames"
        v-model:visible="showChooseDept"
        title="参与部门"
      />
    </el-card>
  </div>
</template>

<script setup name="AddLevelGroup" lang="ts">
import SetCondition from "./components/setCondition.vue";
import ChooseDept from "./components/chooseDept.vue";
import { TreeLong } from "@/api/department/types";
import { reactive, ref } from 'vue'
import { ComponentPublicInstance } from "vue";
import { ElMessageBox } from 'element-plus'
import type { FormRules } from 'element-plus'
import { LevelFormBo, ScriptDetailVo, LevelStageUnlockConditionBo } from "@/api/eventLevel/types";
import { getLevelScriptList, addLevelScriptGroup } from "@/api/eventLevel";
import { getDeptTreeList } from "@/api/department/index"
import Dayjs from "dayjs";
// import { FormValidators } from '@/utils/validate.js'
// import { } from "@/utils/index"
type refItem = Element | ComponentPublicInstance | null;
const deletePopverRefMap = ref({});
const showChooseDept = ref(false)
const deptOptions = ref<TreeLong[]>([])

const scrollDiv = ref()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
import { deepClone } from "@/utils/index"
const formLoading = ref<boolean>(false)
const visible = ref(false)
const initForm = {
  name: '',
  timeArray: [],
  startDate: '',
  endDate: '',
  deptIds: [],
  deptNames: [],
  levelStages: [
    {
      index: 0,
      popId: new Date().getTime(),
      openTime: '',
      detail: [
        {
          scriptId: '',
          conditionText: '', // 拼接condition
          condition: null,
        }
      ]
    }
  ]
} as unknown as LevelFormBo
const formParams = ref(deepClone(initForm))
const scriptOptions = ref<ScriptDetailVo[]>([])
const btnLoading = ref(false)
const showLevelDialog = ref(false)
const currentIndex = ref<number | null>(null)
const currentParentIndex = ref<number | null>(null)

const currentScriptId = ref<string>('')
const currentOpenTime = ref<string>('')
const currentConditionArray = ref<LevelStageUnlockConditionBo[]>([])
const colorMap = new Map([
  [2, '#5EEE3A'],
  [1, '#D9D9D9'],
])

const props = defineProps({
  listType: {
    type: Number,
    default: null
  }
})

//监听日期选择
watch(
  () => formParams.value.timeArray,
  (val) => {
    if (val && val.length > 0) {
      formParams.value.startDate = val[0];
      formParams.value.endDate = val[1];
    } else {
      formParams.value.startDate = '';
      formParams.value.endDate = '';
    }
  }
);


// const checkSameScriptLimit = async (rule: any, value: any, callback: any) => {
//   const res = await scriptLevelDuplicateCheck({ scriptId: value, levelId: null })
//   if (res.data) {
//     callback(new Error('关卡脚本之间不能重复，且不能与其它闯关组重复'))
//   } else {
//     const questionArray = formParams.value.levelStages?.filter(item => item.scriptId === value) || []
//     if (questionArray.length > 1) {
//       callback(new Error('关卡脚本之间不能重复，且不能与其它闯关组重复'))
//     } else {
//       callback()
//     }
//   }

// }

const handleSetPopMap = (el: refItem, item: any) => {
  if (el) {
    deletePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelRemove = (id: any) => {
  deletePopverRefMap.value[`Pop_Ref_${id}`].hide()
}

const showChooseDeptFn = () => {
  showChooseDept.value = true
}



const rules = reactive<FormRules<LevelFormBo>>({
  name: [
    { required: true, message: '请输入闯关名称', trigger: 'blur' },
    {
      min: 1,
      max: 50,
      message: '最多50个字符',
      trigger: ["blur"]
    }
  ],
  timeArray: [
    { required: true, message: '请选择活动时间', trigger: 'change' },
  ],
  deptIds: [
    { required: true, message: '请选择参与部门', trigger: 'change' },
  ]
})
const setRules = reactive({
  scriptId: [
    {
      required: true,
      message: '请设置关卡脚本',
      trigger: ['change']
    },
  ],
  conditionText: [
    {
      required: true,
      message: '请设置通关条件',
      trigger: ['change']
    }
  ],
  openTime: [
    {
      required: true,
      message: '请设置开启时间',
      trigger: ['change']
    }
  ],

})

const handleAdd = () => {
  formParams.value.levelStages.push({
    popId: new Date().getTime(),
    openTime: '',
    detail: [{
      scriptId: '',
      conditionText: '',// 拼接condition
      condition: [{
        scriptId: '',
        scriptName: '',
        time: null,
        score: null
      }],
    }]
  })
  nextTick(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
  });
}

//条件选择弹窗
const handleAddCondition = (parentIndex: number, index: number) => {
  showLevelDialog.value = true
  currentIndex.value = index
  currentParentIndex.value = parentIndex
  currentScriptId.value = formParams.value.levelStages[parentIndex].detail[index].scriptId
  currentOpenTime.value = formParams.value.levelStages[parentIndex].openTime
  currentConditionArray.value = formParams.value.levelStages[parentIndex].detail[index].condition && formParams.value.levelStages[parentIndex].detail[index].condition![0].scriptId !== '' ? formParams.value.levelStages[parentIndex].detail[index].condition as LevelStageUnlockConditionBo[] : []
}
const _getLevelScriptList = async () => {
  formLoading.value = true
  const res = await getLevelScriptList();
  formLoading.value = false
  scriptOptions.value = res.data;
}
//获取弹窗内容
const setConditionFn = (val: LevelStageUnlockConditionBo[], openTime: string) => {
  console.log(openTime)
  if (!currentIndex.value && currentIndex.value !== 0) return
  formParams.value.levelStages[currentParentIndex.value as number].detail[currentIndex.value as number].condition = val
  console.log(val)
  let text = ''
  val.forEach(item => {
    text += (`获得${item.time}次${item.score}分及以上`)
  })
  formParams.value.levelStages[currentParentIndex.value as number].detail[currentIndex.value as number].conditionText = text
  formParams.value.levelStages[currentParentIndex.value as number].openTime = openTime
  proxy.$refs['formParamsRef'].validateField('levelStages.' + currentParentIndex.value + '.detail.' + currentIndex.value + '.conditionText')
  proxy.$refs['formParamsRef'].validateField('levelStages.' + currentParentIndex.value + '.openTime')

  currentScriptId.value = ''
}


const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(initForm[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(initForm[key])
      ) {
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}


watch(() => formParams.value.deptIds, () => {
  proxy.$refs['formParamsRef'].validateField('deptIds')

})


const deleteItem = (popId: any, item: any, index: number) => {
  formParams.value.levelStages.splice(index, 1)
  cancelRemove(popId)
}

const showDelete = (popId: any, item: any, index: any) => {
  currentIndex.value = index
  console.log(item)
  item.scriptId || item.conditionText ? visible.value = true : deleteItem(popId, item, index)
}

// //检查权限方法
// const _scriptUnlockPermissionCheck = async (levelStage: LevelStageBo) => {
//   formLoading.value = true
//   const unlockConditionScriptIds = levelStage.condition?.map(item => item.scriptId) || []
//   const res = await scriptUnlockPermissionCheck({ scriptId: levelStage.scriptId, unlockConditionScriptIds: unlockConditionScriptIds })
//   formLoading.value = false
//   if (!res.data) {
//     formParams.value.levelStages[currentIndex.value as number].errorFlag = true
//   } else {
//     formParams.value.levelStages[currentIndex.value as number].errorFlag = false
//   }
// }

// 检查权限
// const checkPermission = (value: any, index: any) => {
//   formParams.value.levelStages.forEach((element: any, index: number) => {
//     proxy.$refs['formParamsRef'].validateField('levelStages.' + index + '.scriptId')
//   });
// }


// 提交
const handleSubmit = () => {
  btnLoading.value = true;
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      submitFn()
    } else {
      btnLoading.value = false;
      return false;
    }
  });
}
// 提交方法
const submitFn = async () => {
  const formData = deepClone(formParams.value)
  console.log(formData)

  formData.levelStages?.forEach((element: any, index: number) => {
    let array: any = []
    element.detail.forEach((item: any) => {
      array = [...array, ...item.condition]
    });
    element.openTime = element.openTime + ':00'
    element.condition = array
    element.orderNum = index + 1
  });
  formData.deptIds = formData.deptIds.join(',')
  await addLevelScriptGroup(formData).then((res) => {
    emits('change-type', { flagType: 'list', id: '', listType: 1 })
    proxy.$modal.msgSuccess('保存成功');
  }).finally(() => {
    btnLoading.value = false;
  });
}
const _getDeptTreeList = async () => {
  const res = await getDeptTreeList()
  if (res.data) {
    deptOptions.value = res.data
  }
  deptOptions.value = res.data
}
_getDeptTreeList()
_getLevelScriptList()
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  // margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.condition {
  color: #aaaaaa;
}

@media screen and (max-width: 1600px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1601px) {
  .box-container {
    padding: 0 8% 100px 8%;
  }
}

.box-container {
  // display: flex;
  // flex-direction: column;
  // gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  box-sizing: border-box;
  height: calc(100vh - 84px - 32px - 100px);
  overflow-y: auto;
  margin-top: 30px;
  width: 100%;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  .el-card__body {

    // overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}


.error {
  color: #DE9827;
  font-size: 12px;
  margin-bottom: 18px;
  margin-left: 90px;
  font-family: var(--el-font-family);
}


:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 84px - 32px - 100px);
  }
}

.delete {
  display: flex;
  align-items: center;
  gap: 0 16px;
  height: 30px;

  img {
    width: 20px;
    cursor: pointer;
    // margin-bottom: 18px;
  }
}

.wrapper {
  flex:1;
  display: flex;
  margin: 0 auto;
  // width: 800px;
  overflow: hidden;
  border-radius: 8px;
  // padding: 15px ;
  // box-shadow: 20px 20px 60px #bebebe,
  //   -20px -20px 60px #ffffff;
}
.text {
  font-size: 14px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  /* display: flex; */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
}
.text::before {
  content: '';
  height: calc(100% - 24px);
  float: right;
}
.text::after {
  content: '';
  width: 999vw;
  height: 999vw;
  position: absolute;
  box-shadow: inset calc(100px - 999vw) calc(30px - 999vw) 0 0 #fff;
    margin-left: -100px;
}
.btn{
  float: right;
  clear: both;
  margin-left: 10px;
  font-size: 14px;
  padding: 0 8px;
  // background: #3F51B5;
  line-height: 14px;
  border-radius: 4px;
  color:  #333333;
  cursor: pointer;
  /* margin-top: -30px; */
}
.btn::before{
  content:'展开'
}
.exp{
  display: none;
}
.exp:checked+.text{
  -webkit-line-clamp: 999;
}
.exp:checked+.text::after{
  visibility: hidden;
}
.exp:checked+.text .btn::before{
  content:'收起'
}



.random-num {
  color: #333;
}
</style>
