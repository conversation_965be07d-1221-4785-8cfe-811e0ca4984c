<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">脚本数据</span>
          <div class="search-container">
            <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
              <div class="box-title flex">
                <el-form-item prop="deptIds">
                  <el-tree-select
                    v-model="queryParams.deptIds"
                    :data="deptOptions"
                    :props="{ value: 'id', label: 'label', children: 'children', disabled: 'selected' }"
                    node-key="id"
                    check-strictly
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="2"
                    multiple
                    show-checkbox
                    style="width: 250px"
                    :default-expanded-keys="defaultExpandedKeys"
                    placeholder="请选择部门"
                    popper-class="dept-popper"
                    :default-expand-all="false"
                  />
                </el-form-item>
                <el-form-item prop="time">
                  <el-date-picker
                    :disabled-date="disabledDate"
                    v-model="queryParams.timeArray"
                    type="daterange"
                    :clearable="false"
                    range-separator="-"
                    :shortcuts="shortcuts"
                    value-format="YYYY-MM-DD"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="margin-right: 12px;width: 280px;"
                  />
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </template>
      <div class="box-container">
        <div class="box-title bold">整体数据</div>
        <div class="box-content flex">
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/5.png" class="icon" />使用人数</div>
            <div class="item-count">
              <span class="num">{{ yesterdayScriptData.chatPersonNum || 0 }}</span>
              <span class="unit">人</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/1.png" class="icon" />平均得分</div>
            <div class="item-count">
              <span class="num">{{ yesterdayScriptData.averageScore || 0 }}</span
              ><span class="unit">分</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/3.png" class="icon" />平均每次练习时长</div>
            <div class="item-count">
              <span class="num" v-if="yesterdayScriptData.averageMinTime > 0">{{ yesterdayScriptData.averageMinTime
              }}</span>
              <span class="unit" v-if="yesterdayScriptData.averageMinTime > 0">分</span>
              <span class="num">{{ yesterdayScriptData.averageTime }}</span>
              <span class="unit">秒</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/4.png" class="icon" />平均练习次数</div>
            <div class="item-count">
              <span class="num">{{ Math.round(yesterdayScriptData.averageNum * 10) / 10 || 0 }}</span
              ><span class="unit">次</span>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-container" style="justify-content: space-between;">
        <div style="width: 50%;">
          <div class="box-title flex" style="flex-wrap: wrap;">
            <div class="bold">得分分布</div>
          </div>
          <LineChart ref="lineRef2" :width="'100%'" height="350px"></LineChart>
        </div>
        <div style="width: 50%;">
          <div class="box-title flex" style="flex-wrap: wrap;">
            <div class="bold">核心数据</div>
            <div class="search-container">
              <el-form :model="queryParams" ref="queryFormRef" :inline="true" @submit.prevent>
                <el-form-item prop="keyword">
                  <div class="flex">
                    <el-radio-group v-model="filterText" @change="setChartData">
                      <el-radio-button
                        v-for="item in filterArray"
                        style="font-size: 12px;"
                        :key="item.label"
                        :label="item.label"
                        :value="item.label"
                      />
                    </el-radio-group>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <LineChart ref="lineRef1" :width="'100%'" height="350px"></LineChart>
        </div>
      </div>
      <div class="box-container">
        <div class="box-title flex">
          <span class="bold">脚本数据</span>
        </div>
        <el-table v-loading="loading" :data="usedScriptList">
          <el-table-column label="序号" align="center" type="index" width="100" />
          <el-table-column label="名称" align="center" prop="name" />
          <el-table-column label="类型" align="center" prop="type" width="150">
            <template #default="scope">
              <span v-if="scope.row.type === 1">技巧类</span>
              <span v-else-if="scope.row.type === 2">答题类</span>
              <span v-else-if="scope.row.type === 3">幻灯片演练</span>
            </template>
          </el-table-column>
          <el-table-column label="评分标准" align="center" prop="scoringStandardName" width="180" />
          <!-- <el-table-column label="创建时间" align="center" prop="createTime" /> -->
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
              <span v-if="scope.row.status === 1">待上线</span>
              <span v-else-if="scope.row.status === 2">已上线</span>
              <span v-else-if="scope.row.status === 3">已作废</span>
            </template>
          </el-table-column>
          <el-table-column label="使用次数" align="center" prop="usedCount" width="100" />
          <!-- <el-table-column label="操作" align="center" prop="type" /> -->
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button link type="primary" text @click="goToScriptDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="scriptData" lang="ts">
import { onBeforeRouteLeave } from "vue-router";
import LineChart from './components/lineChat.vue'
import * as echarts from 'echarts';
import dayjs from 'dayjs'
import { getScriptStatics, scriptDataAnalyse } from "@/api/dataBoard";
import { ScriptChatResult, scriptDataAnalyseVo } from "@/api/dataBoard/types";
import { TreeLong } from "@/api/department/types";
import { useUserStore } from '@/store/modules/user';
import { deepClone, treeToFlat } from "@/utils/index";
import cache from '@/plugins/cache';
const defaultExpandedKeys = ref<string[]>([])
const userStore = useUserStore();
const router = useRouter();
const lineRef1 = ref(null)
const lineRef2 = ref(null)
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startTime: '',
    deptIds: [],
    endTime: '',
    timeArray: [] as string[]
  },
});
const { queryParams } = toRefs(data);



const usedScriptList = ref<ScriptChatResult[]>([]);
const chatDataList = ref<scriptDataAnalyseVo[]>([])
const scoreList = ref<number[]>([])

const loading = ref(false);
const total = ref(0);

const yesterdayScriptData = ref<scriptDataAnalyseVo & {
  averageMinTime: number;
}>({
  refDate: '',
  chatPersonNum: 0,
  averageScore: 0,
  averageTime: 0,
  averageNum: 0,
  averageMinTime: 0,
})


const shortcuts = [
  {
    text: '今天',
    value: () => {
      const currentDate = dayjs();
      return [currentDate, currentDate]
    },
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day');
      return [yesterday, yesterday]
    },
  },
  {
    text: '近3天',
    value: () => {
      const startDate = dayjs().subtract(2, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近7天',
    value: () => {
      const startDate = dayjs().subtract(6, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近30天',
    value: () => {
      const startDate = dayjs().subtract(30, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
]


const deptOptions = ref<TreeLong[]>([])
const filterText = ref('使用人数')

// '使用人数', '平均得分', '平均每次练习时长', '平均练习次数'
const filterArray = [{
  key: 'chatPersonNum',
  unit: '人',
  label: '使用人数'
}, {
  key: 'averageScore',
  unit: '分',
  label: '平均得分'
}, {
  key: 'averageDataTime',
  unit: '分',
  label: '平均每次练习时长'
}, {
  key: 'averageNum',
  unit: '次',
  label: '平均练习次数'
}]

const disabledDate = (val: any) => {
  return val > new Date();
};
//监听
watch(
  () => [queryParams.value.timeArray, queryParams.value.deptIds],
  ([newTimeArray, newDeptId], [oldTimeArray, oldDeptId]) => {
    queryParams.value.startTime = newTimeArray && newTimeArray.length > 0 ? newTimeArray[0] : '';
    queryParams.value.endTime = newTimeArray && newTimeArray.length > 0 ? newTimeArray[1] : '';
    // queryParams.value.pageNum = 1
    _scriptDataAnalyse()
    getList()
  }
);


const setChartData = () => {
  const obj = filterArray.find(item => item.label === filterText.value)
  // 折线图
  lineRef1!.value.setOptions({
    xAxis: [
      {
        type: 'category',
        interval: 0,
        data: chatDataList.value.map(item => item.refDate),
        axisLabel: {
          color: '#86909C',
          formatter: function (value, index) {
            return value.replace('2024-', '')
          }
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'solid' // 分割线线型。
          }
        }
      },
    ],
    yAxis: [
      {
        axisLabel: {
          color: '#86909C',
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'dashed' // 分割线线型。
          }
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let html = ''
        let value = ''
        if (filterText.value === '平均每次练习时长') {
          const time = chatDataList.value[params[0].dataIndex].averageTime
          const min = Math.floor(Number(time) / 60)
          const seconds = Number(time) % 60; // 计算剩余秒数
          value = min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
        } else {
          value = Math.round(params[0].value * 10) / 10 + obj!.unit
        }
        html += `<div style="color: #333333;font-size: 14px;border-radius: 2px; backdrop-filter: blur(10px);">
                        <div>
                          <div>${params[0].name}</div>
                            <span style='margin-right:20px'>${obj!.label}</span>
                            <span>${value}</span>
                          </div>
                        </div>
                      </div>`
        return html
      }

    },

    series: [
      {
        data: chatDataList.value.map(item => item[obj.key]),
        type: 'line',
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        itemStyle: {
          color: '#4F66FF',
          // borderColor: '#fffff',
          // borderWidth: 1
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 1,
              color: '#FFFFFF'
            },
            {
              offset: 0,
              color: '#D9DEFF'
            }
          ])
        },
        lineStyle: {
          width: 3
        }
      }

    ]
  })

}

const _getDeptTreeList = async () => {
  const res = await userStore.getDeptTreeSelectedListFn()
  if (res) {
    deptOptions.value = res
    defaultExpandedKeys.value= res.length>0?[res[0].id]:[]
    // const array1 = treeToFlat(deepClone(res))
    // const filterArray = array1.filter((item: any) => item.selected === false)
    // const deptIds = filterArray.length > 0 ? [filterArray[0].id] : []
    // queryParams.value.deptIds = deptIds
  }
}

const goToScriptDetail = (row: ScriptChatResult) => {
  cache.session.setJSON('scriptDataQuery', queryParams.value)
  router.push({
    name: 'Script-detail',
    query: {
      scriptId: row.id,
      scriptName: row.name,
      type: row.type,
      startTime: queryParams.value.startTime,
      deptIds: queryParams.value.deptIds,
      endTime: queryParams.value.endTime
    }
  });
}
const handleDate = () => {
  if (cache.session.getJSON('scriptDataQuery')) {
    queryParams.value = cache.session.getJSON('scriptDataQuery')
    cache.session.remove('scriptDataQuery')
  } else {
    const endDate = dayjs().format('YYYY-MM-DD');
    const startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
    queryParams.value.timeArray = [startDate, endDate];
  }

}




/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await getScriptStatics(queryParams.value);
  usedScriptList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
const classifyByInterval = (arr: number[], intervals: any) => {
  const classes = {};
  for (const num of arr) {
    let classified = false;
    for (const [value, interval] of intervals) {
      if (num >= value && num <= value + interval) {
        if (!classes[value]) {
          classes[value] = [];
        }
        classes[value].push(num);
        classified = true;
        break;
      }
    }
    if (!classified) {
      if (!classes['other']) {
        classes['other'] = [];
      }
      classes['other'].push(num);
    }
  }
  return classes;
}

const setBarChartData = () => {
  const width = window.innerWidth * 0.04;
  const intervalArray = [];
  for (let i = 0; i < 10; i++) {
    if (i === 0) {
      intervalArray.push([i * 10, 10])
    } else {
      intervalArray.push([i * 10 + 1, 9])
    }
  }
  const result = classifyByInterval(scoreList.value, intervalArray);
  // console.log(result)
  // console.log(result)
  const xAxisData = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
  const yAxisData: { name: number, value: number }[] = []
  let keys = Object.keys(result)
  xAxisData.forEach(element => {
    let obj = { name: 0, value: 0 }
    obj.name = element;
    if (element === 10) {
      if (keys.includes(String(element - 10))) {
        obj.value = result[element - 10].length;
      } else {
        obj.value = 0;
      }
    } else {
      if (keys.includes(String(element - 9))) {
        obj.value = result[element - 9].length;
      } else {
        obj.value = 0;
      }
    }

    yAxisData.push(obj)
  });
  // console.log(yAxisData)



  // 折线图
  lineRef2.value.setOptions({
    xAxis: [
      {
        type: 'category',
        interval: 0,
        data: xAxisData,
        axisLabel: {
          color: '#86909C',
          padding: [0, 0, 0, width]
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'solid' // 分割线线型。
          }
        }
      },
    ],
    yAxis: [
      {
        axisLabel: {
          color: '#86909C',
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'dashed' // 分割线线型。
          }
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let html = ''
        html += `<div style="color: #333333;font-size: 14px;border-radius: 2px; backdrop-filter: blur(10px);">
                        <div>
                          <div>${params[0].name - 10 > 0 ? params[0].name - 10 + 1 : params[0].name - 10}-${params[0].name}分</div>
                            <span style='margin-right:20px'>${params[0].value}次</span>
                          </div>
                        </div>
                      </div>`
        return html
      }

    },

    series: [
      {
        data: yAxisData.map(item => item.value),
        type: 'bar',
        itemStyle: {
          color: '#4F66FF',
          borderColor: '#fff',
          borderWidth: 3
        },
        // areaStyle: {
        //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //     {
        //       offset: 1,
        //       color: '#FFFFFF'
        //     },
        //     {
        //       offset: 0,
        //       color: '#D9DEFF'
        //     }
        //   ])
        // },
        // lineStyle: {
        //   width: 3
        // }
      }

    ]
  })

}


/**数据 */
const _scriptDataAnalyse = async () => {
 const pageLoading = ElLoading.service({
    lock: true,
    text: '正在加载',
  })
  const res = await scriptDataAnalyse({ startTime: queryParams.value.startTime + ' 00:00:00', endTime: queryParams.value.endTime + ' 23:59:59', deptIds: queryParams.value.deptIds })
  pageLoading.close()
  const min = Number(Math.floor(Number(res.data.scriptRangeDataSummary.averageTime) / 60))
  const seconds = Number(res.data.scriptRangeDataSummary.averageTime) % 60; // 计算剩余秒数
  res.data.scriptRangeDataSummary.averageTime = seconds
  yesterdayScriptData.value = { ...res.data.scriptRangeDataSummary, averageMinTime: min };
  (res.data.scriptRangeData || []).forEach((item: scriptDataAnalyseVo) => {
    item.averageDataTime = Math.round(Number(item.averageTime) / 60 * 100) / 100; //保留2位小数
  })
  chatDataList.value = res.data.scriptRangeData
  scoreList.value = res.data.scoreList
  setChartData()
  setBarChartData()
}

onMounted(() => {
  handleDate()
  _getDeptTreeList()
})
onBeforeRouteLeave((to, from) => {
  // const query = cache.session.getJSON('pointQuery')
  // if (query) {
  //   cache.session.remove('pointQuery')
  // }
  if (to.path === '/dataBoard/script-detail') {
    cache.session.setJSON('scriptDataQuery', queryParams.value)
  } else {
    const query = cache.session.getJSON('scriptDataQuery')
    if (query) {
      cache.session.remove('scriptDataQuery')
    }
  }

})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  align-items: center
}

.box-wrap {
  height: 100%;
  overflow-y: auto;
}

.chart-container {
  display: flex;
  gap: 12px 0;
  align-items: center;
  padding: 0 30px 30px 30px;

  .box-title {
    justify-content: space-between;
    width: 100%;
  }
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-end;
  margin: 0 auto;

  padding: 0 0 30px 0;
  width: 100%;

  .box-title {
    justify-content: space-between;
    width: 100%;
  }

  .box-content {
    width: 100%;
    justify-content: space-between;
    gap: 0 24px;

    .box-item {
      background-color: rgb(244, 246, 255);
      flex: 1;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;

      .item-title {
        font-weight: bold;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 0 8px;
      }

      .icon {
        width: 24px;
        height: 24px;
      }

      .unit {
        font-size: 14px;
        font-weight: normal;
      }

      .item-count {
        width: 100%;
        text-align: center;
        font-size: 28px;
        margin: 24px 0;
        font-weight: bold;
        letter-spacing: 2px;

      }
    }
  }
}

.num {
  font-family: 'inter';
}

:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 84px - 32px - 50px);
    overflow: auto;
  }
}

:deep(.el-table) {
  min-height: 450px
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}

.bold {
  font-weight: bold;
}
</style>
