<template>
  <div class="box-container" style="margin-top: 12px;">
    <div class="box-title bold">整体数据</div>
    <div class="box-content flex">
      <div class="box-item">
        <div class="item-title">
          <img src="@/assets/icons/png/5.png" class="icon" />
          使用人数
        </div>
        <div class="item-count">
          <span class="num">{{ scriptAnalyseData.chatPersonNum || 0 }}</span>

          <span class="unit">人</span>
        </div>
      </div>
      <div class="box-item">
        <div class="item-title"><img src="@/assets/icons/png/1.png" class="icon" />平均得分</div>
        <div class="item-count">
          <span class="num">{{ scriptAnalyseData.averageScore || 0 }}</span
          ><span class="unit">分</span>
        </div>
      </div>
      <div class="box-item">
        <div class="item-title"><img src="@/assets/icons/png/3.png" class="icon" />平均每次练习时长</div>
        <div class="item-count">
          <span class="num" v-if="scriptAnalyseData.averageMinTime > 0">{{ scriptAnalyseData.averageMinTime
          }}</span>
          <span class="unit" v-if="scriptAnalyseData.averageMinTime > 0">分</span>
          <span class="num">{{ scriptAnalyseData.averageTime }}</span>
          <span class="unit">秒</span>
        </div>
      </div>
      <div class="box-item">
        <div class="item-title"><img src="@/assets/icons/png/4.png" class="icon" />平均练习次数</div>
        <div class="item-count">
          <span class="num">{{ Math.round(scriptAnalyseData.averageNum * 10) / 10 || 0 }}</span
          ><span class="unit">次</span>
        </div>
      </div>
    </div>
  </div>
  <div class="chart-container" style="justify-content: space-between;">
    <div style="width: 50%;">
      <div class="box-title flex" style="flex-wrap: wrap;">
        <div class="bold">得分分布</div>
      </div>
      <LineChart ref="lineRef2" :width="'100%'" height="350px"></LineChart>
    </div>
    <div style="width: 50%;">
      <div class="box-title flex" style="flex-wrap: wrap;">
        <div class="bold">核心数据</div>
        <div class="search-container">
          <el-form :model="queryParams" ref="queryFormRef" :inline="true" @submit.prevent>
            <el-form-item prop="keyword">
              <div class="flex">
                <el-radio-group v-model="filterText" @change="setChatData">
                  <el-radio-button v-for="item in filterArray" style="font-size: 12px;" :key="item.label" :label="item.label" :value="item.label" />
                </el-radio-group>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <LineChart ref="lineRef1" :width="'100%'" height="350px"></LineChart>
    </div>
  </div>
  <div class="box-container">
    <div class="box-title flex" style="justify-content: space-between">
      <span class="bold">{{ scriptType === 2 ? '各题平均得分' : '各维度平均得分' }}</span>
      <span class="extend" v-if="scriptType === 2 && questionAverageScoreList.length > 10 && !extendFlag" @click="extendFlag = true"
        >展开
        <el-icon color="#2F7BF4">
          <ArrowDown />
        </el-icon>
      </span>
    </div>

    <div class="qa-container" v-if="scriptType === 2">
      <div class="qa-list">
        <div
          class="qa-item"
          v-for="(item, index) in questionAverageScoreList"
          :key="index"
          v-show="extendFlag ? index < questionAverageScoreList.length : index < 10"
        >
          <div class="left">
            <div class="score-left">
              <div class="score bold">
                <span class="num"> {{ item.averageScore }}</span>
                <span class="unit">分</span>
              </div>
              <div class="question bold">{{ item.question }}</div>
            </div>
            <div class="num">{{ item.num }}次</div>
          </div>
          <div class="right">
            <div class="score-rate" :style="'width:' + item.averageScore + '%'"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="qa-container" v-else>
      <div class="qa-list">
        <div class="skill-item" v-for="(item, index) in skillAverageScoreList" :key="index">
          <div class="left">
            <div class="question bold">{{ item.name }}</div>
            <div class="score bold">
              {{ Math.round(item.averageScore * 10) / 10 }}
              <span class="unit">/{{ item.fullScore }}分</span>
            </div>
          </div>
          <div class="right">
            <div class="score-rate" :style="'width:' + (item.averageScore * 100 / item.fullScore) + '%'"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="scriptTab">
import LineChart from './lineChat.vue'
import * as echarts from 'echarts';
// import dayjs from 'dayjs'
import { scriptAnalyse } from "@/api/dataBoard";
import { scriptDataAnalyseVo, QuestionAverageScoreVo, SkillAverageScoreVo } from "@/api/dataBoard/types";
// import { TreeLong } from "@/api/department/types";
// const router = useRouter();
// const route = useRoute();
const lineRef1 = ref(null)
const lineRef2 = ref(null)
const scoreList = ref<number[]>([])
const chatDataList = ref<scriptDataAnalyseVo[]>([])
const extendFlag = ref(false)
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const questionAverageScoreList = ref<QuestionAverageScoreVo[]>([])
const skillAverageScoreList = ref<SkillAverageScoreVo[]>([])
const props = defineProps({
  deptIds: {
    type: Array,
    default: ()=>[]
  },
  scriptId: {
    type: String,
    default: ''
  },
  timeArray: {
    type: Array,
    default: () => []
  },
  type: {
    type: Number,
    default: 0
  }
})

const scriptAnalyseData = ref<scriptDataAnalyseVo & {
  averageMinTime: number;
}>({
  refDate: '',
  chatPersonNum: 0,
  averageScore: 0,
  averageTime: 0,
  averageNum: 0,
  averageMinTime: 0,
})
const data = reactive({
  queryParams: {
    startTime: '',
    deptIds: [],
    endTime: '',
    scriptId: '',
    timeArray: [] as string[]
  },
});
const { queryParams } = toRefs(data);
const scriptType = ref(0)

const filterText = ref('使用人数')

// '使用人数', '平均得分', '平均每次练习时长', '平均练习次数'
const filterArray = [{
  key: 'chatPersonNum',
  unit: '人',
  label: '使用人数'
}, {
  key: 'averageScore',
  unit: '分',
  label: '平均得分'
}, {
  key: 'averageDataTime',
  unit: '分',
  label: '平均每次练习时长'
}, {
  key: 'averageNum',
  unit: '次',
  label: '平均练习次数'
}]


//监听
watch(
  () => props,
  (val: any) => {
    console.log(val)
    if (val.timeArray && val.timeArray.length > 0) {
      queryParams.value.startTime = val.timeArray[0] + ' 00:00:00';
      queryParams.value.endTime = val.timeArray[1] + ' 23:59:59';
    } else {
      queryParams.value.startTime = '';
      queryParams.value.endTime = '';
    }
    if (val.deptIds) queryParams.value.deptIds = val.deptIds
    if (val.scriptId) queryParams.value.scriptId = val.scriptId
    if (val.type) scriptType.value = Number(val.type)

    _scriptAnalyse()
    // getChatList()
    // getList()
  }, {
  deep: true
}
);

const setChatData = () => {
  const obj = filterArray.find(item => item.label === filterText.value)
  // 折线图
  lineRef1!.value.setOptions({
    xAxis: [
      {
        type: 'category',
        interval: 0,
        data: chatDataList.value.map(item => item.refDate),
        axisLabel: {
          color: '#86909C',
          formatter: function (value: string) {
            return value.replace('2024-', '')
          }
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'solid' // 分割线线型。
          }
        }
      },
    ],
    yAxis: [
      {
        axisLabel: {
          color: '#86909C',
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'dashed' // 分割线线型。
          }
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let html = ''
        let value = ''
        if (filterText.value === '平均每次练习时长') {
          const time = chatDataList.value[params[0].dataIndex].averageTime
          const min = Math.floor(Number(time) / 60)
          const seconds = Number(time) % 60; // 计算剩余秒数
          value = min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
        } else {
          value = Math.round(params[0].value * 10) / 10 + obj!.unit
        }
        html += `<div style="color: #333333;font-size: 14px;border-radius: 2px; backdrop-filter: blur(10px);">
                        <div>
                          <div>${params[0].name}</div>
                            <span style='margin-right:20px'>${obj!.label}</span>
                            <span>${value}</span>
                          </div>
                        </div>
                      </div>`
        return html
      }

    },

    series: [
      {
        data: chatDataList.value.map(item => item[obj.key]),
        type: 'line',
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        itemStyle: {
          color: '#4F66FF',
          // borderColor: '#fff',
          // borderWidth: 3
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 1,
              color: '#FFFFFF'
            },
            {
              offset: 0,
              color: '#D9DEFF'
            }
          ])
        },
        lineStyle: {
          width: 3
        }
      }

    ]
  })

}

const classifyByInterval = (arr: number[], intervals: any) => {
  const classes = {};
  for (const num of arr) {
    let classified = false;
    for (const [value, interval] of intervals) {
      if (num >= value && num <= value + interval) {
        if (!classes[value]) {
          classes[value] = [];
        }
        classes[value].push(num);
        classified = true;
        break;
      }
    }
    if (!classified) {
      if (!classes['other']) {
        classes['other'] = [];
      }
      classes['other'].push(num);
    }
  }
  return classes;
}

const setBarChartData = () => {
  const width = window.innerWidth * 0.04;
  const intervalArray = [];
  for (let i = 0; i < 10; i++) {
    if (i === 0) {
      intervalArray.push([i * 10, 10])
    } else {
      intervalArray.push([i * 10 + 1, 9])
    }
  }
  const result = classifyByInterval(scoreList.value, intervalArray);
  // console.log(result)
  const xAxisData = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
  const yAxisData: { name: number, value: number }[] = []
  let keys = Object.keys(result)
  xAxisData.forEach(element => {
    let obj = { name: 0, value: 0 }
    obj.name = element;
    if (element === 10) {
      if (keys.includes(String(element - 10))) {
        obj.value = result[element - 10].length;
      } else {
        obj.value = 0;
      }
    } else {
      if (keys.includes(String(element - 9))) {
        obj.value = result[element - 9].length;
      } else {
        obj.value = 0;
      }
    }

    yAxisData.push(obj)
  });
  // console.log(yAxisData)



  // 折线图
  lineRef2.value.setOptions({
    xAxis: [
      {
        type: 'category',
        interval: 0,
        data: xAxisData,
        axisLabel: {
          color: '#86909C',
          padding: [0, 0, 0, width]
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'solid' // 分割线线型。
          }
        }
      },
    ],
    yAxis: [
      {
        axisLabel: {
          color: '#86909C',
        },
        splitLine: {
          show: true, // 显示分割线。
          lineStyle: {
            // 分割线样式。
            color: '#F2F2F5', // 分割线颜色。
            width: 1, // 分割线宽度。
            type: 'dashed' // 分割线线型。
          }
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let html = ''
        html += `<div style="color: #333333;font-size: 14px;border-radius: 2px; backdrop-filter: blur(10px);">
                        <div>
                          <div>${params[0].name - 10 > 0 ? params[0].name - 10 + 1 : params[0].name - 10}-${params[0].name}分</div>
                            <span style='margin-right:20px'>${params[0].value}次</span>
                          </div>
                        </div>
                      </div>`
        return html
      }

    },

    series: [
      {
        data: yAxisData.map(item => item.value),
        type: 'bar',
        itemStyle: {
          color: '#4F66FF',
          borderColor: '#fff',
          borderWidth: 3
        },
        // areaStyle: {
        //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //     {
        //       offset: 1,
        //       color: '#FFFFFF'
        //     },
        //     {
        //       offset: 0,
        //       color: '#D9DEFF'
        //     }
        //   ])
        // },
        // lineStyle: {
        //   width: 3
        // }
      }

    ]
  })

}


const _scriptAnalyse = async () => {
  proxy?.$modal.loading("正在加载");
  const res = await scriptAnalyse(queryParams.value);
  proxy?.$modal.closeLoading();
  const min = Number(Math.floor(Number(res.data.averageTime) / 60));
  const seconds = Number(res.data.averageTime) % 60; // 计算剩余秒数
  res.data.averageMinTime = min;
  res.data.averageTime = seconds;
  (res.data.coreDatalist || []).forEach((item: scriptDataAnalyseVo) => {
    item.averageDataTime = Math.round(Number(item.averageTime) / 60 * 100) / 100; //保留2位小数
  })
  chatDataList.value = res.data.coreDatalist;
  scoreList.value = res.data.scoreList;
  questionAverageScoreList.value = res.data.questionAverageScoreList || []
  console.log(questionAverageScoreList.value)
  skillAverageScoreList.value = res.data.skillAverageScoreList || []
  setChatData()
  setBarChartData()
  scriptAnalyseData.value = {
    chatPersonNum: res.data.usePerson,
    averageScore: res.data.averageScore,
    averageTime: res.data.averageTime,
    averageNum: res.data.averageNum,
    averageMinTime: res.data.averageMinTime,
  }
}
</script>
<style scoped lang="scss">
.chart-container {
  display: flex;
  gap: 12px 0;
  align-items: center;
  // padding: 0 30px 30px 30px;
  // margin-bottom: 12px;
  padding-bottom: 30px;

  .box-title {
    justify-content: space-between;
    width: 100%;
  }
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-end;
  margin: 0 auto;

  padding-bottom: 30px;
  // padding: 0 30px 30px 30px;
  width: 100%;
  // margin-bottom: 12px;

  .box-title {
    justify-content: space-between;
    width: 100%;
  }

  .box-content {
    width: 100%;
    justify-content: space-between;
    gap: 0 24px;
    // padding-bottom: 30px;

    .box-item {
      background-color: rgb(244, 246, 255);
      flex: 1;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;

      .item-title {
        font-weight: bold;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 0 8px;
      }

      .icon {
        width: 24px;
        height: 24px;
      }

      .unit {
        font-size: 14px;
        font-weight: normal;
      }

      .item-count {
        width: 100%;
        text-align: center;
        font-size: 28px;
        margin: 24px 0;
        font-weight: bold;
        letter-spacing: 2px;

      }
    }
  }
}

.num {
  font-family: 'inter';
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}

.bold {
  font-weight: bold;
}

.qa-container {
  width: 100%;
}

.qa-item {
  width: 100%;
  // height: 67px;
  // box-sizing: border-box;
  padding: 8px 48px;
  background-color: #F2F7FF;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0 24px;
  margin-bottom: 4px;

  .left {
    width: 58%;
    display: flex;
    color: #272C47;
    align-items: center;
    justify-content: space-between;

    // gap: 0 24px;
    .score-left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 0 24px;
      width: 90%;
    }

    .num {
      font-family: 'inter';

    }

    .score {
      font-size: 28px;
      display: flex;
      width: 70px;
      align-items: center;
      // letter-spacing: 3px;
    }

    .unit {
      font-size: 14px;
      margin-top: 10px;
      font-weight: normal;
      margin-left: 3px;
    }

    .question {
      font-size: 16px;
      flex: 1;

    }

    .num {}
  }

  .right {
    width: 37%;
    background-color: #ffffff;
    height: 16px;

    .score-rate {
      height: 100%;
      background-color: #2F7BF4;
    }
  }
}

.skill-item {
  width: 100%;
  // height: 67px;
  box-sizing: border-box;
  padding: 8px 48px;
  background-color: #F2F7FF;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0 24px;
  margin-bottom: 4px;

  .left {
    width: 35%;
    display: flex;
    color: #272C47;
    align-items: center;
    gap: 0 24px;

    .score {
      font-size: 28px;
      display: flex;
      font-family: 'inter';
      align-items: center;
    }

    .unit {
      font-size: 14px;
      margin-top: 10px;
      color: #272C47;
      font-weight: normal;
      margin-left: 4px;
    }

    .question {
      font-size: 16px;
      width: 40%;
    }

  }

  .right {
    width: 65%;
    background-color: #ffffff;
    height: 16px;

    .score-rate {
      height: 100%;
      background-color: #2F7BF4;
    }
  }
}

.extend {
  color: #2F7BF4;
  display: flex;
  align-items: center;
  gap: 0 4px;
  cursor: pointer;
}

.flex {
  display: flex;
  align-items: center
}
</style>
