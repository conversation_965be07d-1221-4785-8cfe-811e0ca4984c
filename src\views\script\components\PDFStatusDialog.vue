<template>
  <el-dialog
    v-model="visibleShow"
    width="380"
    align-center
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onClose"
  >
    <div class="container">
      <Vue3Lottie width="200px" height="200px" :animation-data="loadJson" />
      <div class="title">{{ loading ? '幻灯片处理中' : '生成失败' }}</div>
      <div class="tip" v-if="loading">预计需要2-3分钟时间，请耐心等待</div>
      <el-button class="btn" v-if="!loading" type="primary" @click="handleRegenerate">重试</el-button>
      <el-button class="btn" v-if="loading" plain @click="visibleShow = false">最小化</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { getScriptDetail, pptParseRetry } from '@/api/script'
import loadJson from "@/assets/icons/json/loading.json";
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  parseFileId: {
    type: String,
    default: ''
  },
  params: {
    type: Object
  }
})
const emits = defineEmits(['update:visible', 'onSuccess'])
const timer = ref()
const visibleShow = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const state = reactive({
  loading: true
})

const { loading } = toRefs(state)

const requestResult = () => {
  timer.value = setInterval(async () => {
    try {
      const res = await getScriptDetail({ id: props.parseFileId })
      //  解析状态 0-未解析 1-解析中 2-解析失败 3-解析完成
      if (res.data.parseStatus === 3) {
        // 生成成功
        proxy.$modal.msgSuccess('幻灯片处理完成')
        clearInterval(timer.value)
        visibleShow.value = false
      } else if (res.data.parseStatus === 2) {
        loading.value = false
        // emits('onSuccess')
      }

    } catch (error) {
      clearInterval(timer.value)
      loading.value = false
    }
  }, 10000)
}

const handleRegenerate = async () => {
  state.loading = true
  try {
    try {
      await pptParseRetry({ id: props.parseFileId })
      requestResult()
    } catch (error) {
      console.log(error);
    }

  } catch (error) {
    console.log(error);
  }

}
const onClose = () => {
  clearInterval(timer.value)
  visibleShow.value = false
  emits('onSuccess')
}

// const close = () => {
//   clearInterval(timer.value)
//   visibleShow.value = false
//   emits('onSuccess')
// }
watch(() => props.parseFileId, val => {
  if (timer.value) {
    timer.value = null
  }
  if(props.parseFileId){
    requestResult()
  }
})
// const onOpen = () => {
//   console.log(props.parseFileId)
//   if (props.parseFileId) {
//     requestResult()
//   }
// }
</script>

<style scoped lang="scss">
.container {
  text-align: center;
}

.title {
  font-size: 16px;
}

.tip {
  font-size: 14px;
  margin-top: 20px;
  margin-left: 46px;
  margin-right: 46px;
  color: #999999;
}

.btn {
  margin-top: 20px;
}
</style>
