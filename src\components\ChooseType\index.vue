<template>
  <div class="p-2">
    <el-dialog class="choose-type-dialog" v-model="visibleDialog" width="800" align-center :show-close="false">
      <template #title>
        <div class="choose-type-head">{{ title }}</div>
      </template>
      <div class="choose-type-container">
        <el-row :gutter="25">
          <el-col :span="12" v-for="(item, index) in chooseTypeArray" :key="index">
            <div class="choose-type-box" @click="chooseAddType(item.type)">
              <img class="choose-type-icon" :src="item.img" :style="item.style" />
              <el-button type="primary" size="large" class="choose-type-title">{{ item.text }}</el-button>
              <div class="choose-type-tip" type="info">{{ item.description }}</div>
            </div>
          </el-col>
          <!-- <el-col :span="12">
            <div class="choose-type-box" @click="chooseAddType(2)">
              <img class="choose-type-icon" src="@/assets/images/eman2.png" />
              <el-button type="primary" size="large" class="choose-type-title">企业智脑</el-button>
              <div class="choose-type-tip" type="info">适合学习场景，如学术经理、顾问等咨询角色</div>
            </div>
          </el-col> -->
        </el-row>

        <img class="choose-type-close" @click="closeAddTypeDialog" src="@/assets/images/icon_cancel.svg" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">

// import { ElMessageBox, ElLoading } from 'element-plus'
// import { getMaleAvatarList, getFemaleAvatarList } from "@/api/eman/common";
const emits = defineEmits(['update:visible', 'change-choose-type'])



// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新增'
    },
    chooseTypeArray: {
        type: Array,
        default: () => [{}]
    },
})

// watch(() => props.visible, val => {
//     if (val) {
//     }
// })

const chooseAddType = (type: number) => {
      emits('change-choose-type', type)
}
const closeAddTypeDialog = () => {
    visibleDialog.value = false;
}


// 弹窗组件显示隐藏
const visibleDialog = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})
</script>

<style scoped lang="scss">
:global(.choose-type-dialog) {
    background: none;
    box-shadow: none;
}

.choose-type {
    &-head {
        color: #fff;
        text-align: center;
        font-size: 24px;
    }

    &-content {
        cursor: pointer;
    }

    &-container {
        width: 80%;
        margin: 0 auto;
    }

    &-box {
        width: 280px;
        height: 310px;
        border-radius: 9px;
        text-align: center;
        background-color: #fff;
        overflow: hidden;
    }

    &-icon {
        width: 93px;
        height: 93px;
        display: block;
        margin: 40px auto 17px;
    }

    &-title {
        font-size: 20px;
        font-weight: 500;
        width: 187px;
    }

    &-tip {
        font-size: 15px;
        margin-top: 20px;
        margin-left: 46px;
        margin-right: 46px;
    }

    &-close {
        display: block;
        width: 32px;
        height: 32px;
        margin: 68px auto 0;
        cursor: pointer;
    }
}
</style>
