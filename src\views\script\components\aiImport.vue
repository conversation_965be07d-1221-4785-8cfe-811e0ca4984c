<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      :title="title"
      v-model="visibleFlag"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
    >
      <template #title>
        {{ title }}
        <div style="font-size: 14px;color: #999999;margin-top: 12px;">请上传文件，AI将自动从文件里整理出问题与答案</div>
      </template>
      <div class="box-container">
        <!-- <FileAIUpload v-model="fileIdList"></FileAIUpload> -->
        <div class="upload-file">
          <el-upload
            multiple
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            v-model:file-list="fileList"
            drag
            name="files"
            :on-error="handleUploadError"
            :on-exceed="handleExceed"
            :on-change="handleChange"
            :on-success="handleUploadSuccess"
            :show-file-list="true"
            :limit="limit"
            :on-remove="handleRemove"
            :headers="headers"
            class="upload-file-uploader"
            ref="fileUploadRef"
            :auto-upload="false"
          >
            <!-- 上传按钮 -->
            <!-- <el-button type="primary">选取文件</el-button> -->
            <div class="upload-container">
              <img src="@/assets/icons/png/upload.png" />
              <div class="right">
                <div>将文档拖到此处，或点击上传</div>
                <div class="tips">
                  支持PDF、Word文档（docx、doc）、Excel表格（xlsx、xls）、PPT文件（PPT、PPTX）、TXT，最多支持1个文件，大小不超过50MB，文件中的图片不支持识别。
                </div>
              </div>
            </div>
          </el-upload>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" :loading="importLoading" @click="handleSubmit" :disabled="fileList.length === 0">开始生成</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { globalHeaders } from "@/utils/request";
import { propTypes } from '@/utils/propTypes';
const currentUploadRequest = ref(null)
import { ElMessageBox } from 'element-plus'
const emits = defineEmits(['update:visible', 'setAiGenerateData'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const fileList = ref([])
const number = ref(0);
// const uploadList = ref<any[]>([]);
const importLoading = ref(false)

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/biz/ai/coze/aiGenerateQuestionWithFile"); // 上传文件服务器地址
const headers = ref(globalHeaders());
const fileUploadRef = ref<ElUploadInstance>();
const currentFile = ref<any>(null)


// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'AI生成'
  },
  scriptId: {
    type: String,
    default: ''
  },
  modelValue: [String, Object, Array],
  // 数量限制
  limit: propTypes.number.def(1),
  // 大小限制(MB)
  fileSize: propTypes.number.def(50),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def(['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf']),
  // 是否显示提示
  isShowTip: propTypes.bool.def(true),

})


// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})




// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {

  // proxy?.$modal.loading("正在上传文件，请稍候...");
  currentUploadRequest.value = file.request
  currentFile.value = file
  number.value++;
  return true;
}

// 文件个数超出
const handleExceed = () => {
  proxy?.$modal.msgError(`上传文件数量不能超过${props.limit}个!`);
}

const handleChange = (file:any) => {
  // emit("update:modelValue", fileList.value);
    // 校检文件类型
    if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
      importLoading.value = false
      fileUploadRef.value?.handleRemove(file);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      fileUploadRef.value?.handleRemove(file);
      return false;
    }
  }
}


// 上传失败
const handleUploadError = (file: any) => {
  if (number.value > 0) {
    number.value--;
  }
  fileUploadRef.value?.handleRemove(file);
  // emit("update:modelValue", fileList.value);
  proxy?.$modal.msgError("上传文件失败");
}
const handleRemove = () => {
  if (number.value > 0) {
    number.value--;
  }
}
// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  // console.log(res)
  importLoading.value = false
  if (res.code === 200) {
    uploadedSuccessfully(res.data);
  } else {
    number.value--;
    // proxy?.$modal.closeLoading();
    proxy?.$modal.msgError('解析失败');
    importLoading.value = false
    fileUploadRef.value?.handleRemove(file);
    currentFile.value = null
    fileList.value=[]
  }
}


// 上传结束处理
const uploadedSuccessfully = (data: any) => {
  if (number.value > 0) {
    number.value = 0;
    fileList.value=[]
    data.currentFile = currentFile.value
    emits('setAiGenerateData', data)
    currentFile.value = null
    visibleFlag.value = false
  }
}



const closeDialog = () => {
  if (fileList.value.length > 0) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要取消吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        fileList.value = []
        importLoading.value=false
        currentFile.value = null
        visibleFlag.value = false
      })
      .catch(() => {
      })
  } else {
    visibleFlag.value = false
  }

}

const handleSubmit = () => {
  importLoading.value = true
  fileUploadRef.value!.submit()


  // const array = fileIdList.value.map(item => item.id)
  // emits('sendFileIdList', array)
  // fileIdList.value=[]
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
}

.upload-file {
  width: 100%;
}

.rotating-image {
  animation: rotate 2s linear infinite;
}

:deep(.el-upload) {
  width: 100%;
}

.upload-file-uploader {
  margin-bottom: 5px;
}

.el-upload-list__item {
  // border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
  padding: 8px;
}

.el-icon-document {
  display: flex;
  align-items: center;
  gap: 0 12px;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.upload-container {
  margin: 0 auto;
  display: flex;
  align-items: flex-start;
  gap: 0 16px;
  width: 100%;
  border-radius: 4px;

  img {
    width: 48px;
    height: 48px;
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;

    .tips {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
      color: #999999;
    }
  }
}
</style>
