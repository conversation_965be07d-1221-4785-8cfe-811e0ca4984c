<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" v-loading="loading">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="disableTextInputFlag">
                <template #label>
                  <span class="label-text flex">禁用文字输入（答题类、技巧类）</span>
                  <el-tooltip content="禁用后该脚本在小程序将无法输入文字进行对话。" placement="top" popper-class="popper">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;  margin-left: 8px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="formParams.disableTextInputFlag" class="ml-2" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="typoCorrectFlag">
                <template #label>
                  <span class="label-text flex">语音识别优化（答题类）</span>
                  <el-tooltip
                    content="开启后，AI将对学员的回答语音识别结果进行优化，减少错别字对评分报告的影响，仅答题类脚本生效。"
                    placement="top"
                    popper-class="popper"
                  >
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;  margin-left: 8px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="formParams.typoCorrectFlag" class="ml-2" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="typoCorrectFlag">
                <template #label>
                  <span class="label-text flex">对话求助</span>
                  <el-tooltip placement="top" popper-class="popper">
                    <template #content>
                      <div style="flex-direction: column;" class="flex">
                        <span> 开启后，学员在对话过程中可随时向AI进行求助：</span>
                        <span>1.技巧类脚本，AI将根据上下文生成有技巧的回复话术；</span>
                        <span>2.答题类脚本，AI将根据问题的参考答案生成启发性的提示。</span>
                        <span>3.幻灯片演练，AI将根据每页幻灯片的内容生成讲解内容。</span>
                      </div>
                    </template>
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;  margin-left: 8px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="formParams.aiHelpFlag" class="ml-2" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="requirementShowFlag">
                <template #label>
                  <span class="label-text flex">演练要求或关键词（幻灯片演练）</span>
                  <el-tooltip placement="top" content="开启后，学员在进行幻灯片演练时，可在练习时查看演练要求或关键词内容。" popper-class="popper">
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;  margin-left: 8px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="formParams.requirementShowFlag" class="ml-2" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { batchUpdateDisableTextInputFlag } from "@/api/script/index"
import { deepClone } from "@/utils/index";
import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;


const emits = defineEmits(['update:visible', 'success'])
const formParamsRef = ref()
const btnLoading = ref(false)
const loading = ref(false)

let initForm = {
    disableTextInputFlag: false,
    typoCorrectFlag: false,
    aiHelpFlag:false,
    requirementShowFlag:false
}



const formParams = ref(deepClone(initForm))
// import type { FormRules } from 'element-plus'
// interface RuleForm {
//     type: number,
//     dataScope: string
// }

// const rules = reactive<FormRules<RuleForm>>({
//     type: [
//         { required: true, message: '请选择部门', trigger: 'change' },
//     ],
// })

watch(() => props.visible, val => {
    if (val) {
        // disableTextInputFlag
        console.log(props.typoCorrectFlag)
        initForm.disableTextInputFlag = props.inputFlag
        initForm.typoCorrectFlag = props.typoCorrectFlag
        initForm.aiHelpFlag = props.aiHelpFlag
        initForm.requirementShowFlag = props.requirementShowFlag
        formParams.value.disableTextInputFlag = props.inputFlag
        formParams.value.typoCorrectFlag = props.typoCorrectFlag
        formParams.value.aiHelpFlag = props.aiHelpFlag,
        formParams.value.requirementShowFlag = props.requirementShowFlag

    }
})


// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '设置'
    },
    ids: {
        type: Array,
        default: () => []
    },
    inputFlag: {
        type: Boolean,
        default: false
    },
    typoCorrectFlag: {
        type: Boolean,
        default: false
    },
    aiHelpFlag: {
        type: Boolean,
        default: false
      },
      requirementShowFlag: {
        type: Boolean,
        default: false
      }
})


// 弹窗组件显示隐藏
const visibleAdd = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})


const back = () => {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
        if (typeof formParams.value[key] !== 'object') {
            if (
                JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(initForm[key]))
            ) {
                bool = true;
                return;
            }
        } else {
            if (
                JSON.stringify(formParams.value[key]) !== JSON.stringify(initForm[key])
            ) {
                bool = true;
                return;
            }
        }

    });
    if (bool) {
        ElMessageBox.confirm(
            '未保存的内容将丢失',
            '确定要返回吗？',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async () => {
                closeDialog()
            })
            .catch(() => {
            })
    } else {
        closeDialog()
    }
}



const closeDialog = () => {
    loading.value = true
    formParamsRef.value.resetFields()
    loading.value = false
    visibleAdd.value = false
}

const handleSubmit = () => {
    proxy.$refs['formParamsRef'].validate(async (valid: any) => {
        if (valid) {
            const params = {
                scriptIdList: props.ids as string[],
                disableTextInputFlag: formParams.value.disableTextInputFlag,
                typoCorrectFlag: formParams.value.typoCorrectFlag,
                aiHelpFlag: formParams.value.aiHelpFlag,
                requirementShowFlag: formParams.value.requirementShowFlag,

            };
            btnLoading.value = true
            await batchUpdateDisableTextInputFlag(params).then((res) => {
                proxy.$modal.msgSuccess('保存成功');
                closeDialog()
                emits('success')
            }).finally(() => {
                btnLoading.value = false;
            });
        } else {
            console.log('error submit!!');
            return false;
        }
    });
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;

    .box-title {
        color: #272C47;
        font-size: 20px;
        margin: 18px 0;
        font-weight: bold;

    }
}

.tips {
    color: #999999;
    margin-left: 120px;
}

:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}

.dept-container {
    // margin: 30px;
    // box-sizing: border-box;
    width: 70%;
    margin: 0 auto;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    min-height: 30vh;
    // position: relative;
    display: flex;
    padding: 8px 0;

    .left {
        width: 100%;

    }
}

.flex {
    display: flex;
    gap: 0 10px;
    align-items: center;
}

.label-tip {
    align-self: center;
    color: #999999;
}

.divider {
    // height: 100%;
    // width: 1px;
    // background-color: var(--el-border-color);
}
</style>
