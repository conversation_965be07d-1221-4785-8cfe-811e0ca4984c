export interface ChatReportScoringStandardVo {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 主键
   */
  id?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 状态，1-启用，0-禁用
   */
  status?: number;
  /**
   * 系统默认标准
   */
  system?: boolean;
  /**
   * 类型，1-技巧类，2-答题类
   */
  type?: number;
}

export interface ChatReportScoringStandardDetailVo {
  /**
   * 评分维度
   */
  dimensions: string;
  system: boolean;
  /**
   * 主键
   */
  id: number;
  /**
   * 名称
   */
  name: string;
}
/**
 * ChatReportScoringStandardBo
 */
export interface ChatReportScoringStandardBo {
  /**
   * 评分维度
   */
  dimensions?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 类型，1-技巧类，2-答题类
   */
  type?: number;
  [property: string]: any;
}
