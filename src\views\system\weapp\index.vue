
<template>
  <div class="p2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">小程序设置</span>
        </div>
      </template>
      <el-table v-loading="loading" :data="list" :show-header="false">
        <el-table-column label="" align="left" prop="name" />
        <el-table-column label="" align="left" prop="position" />
        <el-table-column label="" align="left" prop="description" />
        <el-table-column label="" align="right">
          <template #default="scope">
            <el-text class="table-action-btn" type="primary" @click="handleEdit(scope.row)">修改</el-text>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <AiPracticePageDialog v-model:visible="data.aiPractisePageVisible" :value="settings.aiPractisePage" @on-success="onSuccess" />
    <ChatPageDialog v-model:visible="data.chatPageVisible" :value="settings.chatPage" @on-success="onSuccess" />
    <IndexPageDialog v-model:visible="data.indexPageVisible" :value="settings.indexPage" @on-success="onSuccess" />
  </div>
</template>
<script setup lang="ts">
import AiPracticePageDialog from "./components/aiPracticePageDialog.vue";
import ChatPageDialog from "./components/chatPageDialog.vue";
import IndexPageDialog from "./components/indexPageDialog.vue";
import {getWeappSetting, updateWeappSetting} from '@/api/system/tenant'
import { WeappSettingParam } from "@/api/system/tenant/types";
const loading = ref(true)


const settings = reactive<WeappSettingParam>({
  indexPage: 'CHAT_PAGE', // AI_PRACTISE,CHAT_PAGE
  aiPractisePage: 'SCRIPT', // PRODUCT, SCRIPT
  chatPage: 'SCENE_PRACTISE_AND_SMART_BRAIN' // SCENE_PRACTISE_AND_SMART_BRAIN，SMART_BRAIN
})

const typeText = {
  AI_PRACTISE: {
    position: 'AI陪练',
    description: '“AI陪练”页面为小程序首页'
  },
  CHAT_PAGE: {
    position: '对话',
    description: '“对话”页面为小程序首页'
  },
  PRODUCT: {
    position: '产品列表',
    description: '以“产品”维度进行分类展示'
  },
  SCRIPT: {
    position: '脚本列表',
    description: '以“脚本”维度进行分类展示'
  },
  SCENE_PRACTISE_AND_SMART_BRAIN: {
    position: '情景演练+企业智脑',
    description: '显示“情景演练+企业智脑”类E人'
  },
  SMART_BRAIN: {
    position: '企业智脑',
    description: '显示“企业智脑”类E人'
  }
}

const list = computed(() => [
  {
    key: 'indexPage',
    name: '首页设置',
    position: typeText[settings.indexPage].position,
    value: settings.indexPage,
    description: typeText[settings.indexPage].description
  },
  {
    key: 'aiPractisePage',
    name: 'AI陪练页面设置',
    position: typeText[settings.aiPractisePage].position,
    value: settings.aiPractisePage,
    description: typeText[settings.aiPractisePage].description
  },
  {
    key: 'chatPage',
    name: '对话页面设置',
    position:  typeText[settings.chatPage].position,
    value: settings.chatPage,
    description: typeText[settings.chatPage].description
  }
])

const data = reactive({
  indexPageVisible: false,
  aiPractisePageVisible: false,
  chatPageVisible: false
})

const handleEdit = (row:any) => {
  console.log(row)
  switch (row.key) {
    case 'indexPage':
      data.indexPageVisible = true
      break;
    case 'aiPractisePage':
      data.aiPractisePageVisible = true
      break;
    case 'chatPage':
      data.chatPageVisible = true
      break;
  }
}

const onSuccess = (key:string, val:string) => {
  console.log('onAccompanyPraticeSuccess', key, val)
  settings[key] = val
  updateWeappSetting({
    ...settings
  }).then(() => {
    initData()
  })
}


const initData = () => {
  loading.value = false
  getWeappSetting().then((res) => {
    if(res.data) {
      settings.aiPractisePage = res.data.aiPractisePage
      settings.chatPage = res.data.chatPage
      settings.indexPage = res.data.indexPage
    }

  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {

  initData()
})
</script>

<style scoped lang="scss">
.table-action-btn {
  cursor: pointer;
  padding: 0 10px;
}
</style>
