<template>
  <el-dialog
    ref="formDialogRef"
    title="选择通知对象"
    v-model="visibleFlag"
    width="750px"
    append-to-body
    :close-on-click-modal="false"
    :show-close="true"
  >
    <div class="box-container">
      <div class="flex">
        <el-button @click="getList">刷新</el-button>
        <div class="tips">已选择的 {{multipleSelection.length}} 人（仅统计有该脚本权限的且练习次数为0且账号激活的成员）</div>
      </div>
      <el-table v-loading="loading" :data="userList" ref="tableRef" height="500" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="姓名" align="left" prop="name" width="130" />
        <el-table-column label="部门" align="left" prop="deptName" />
        <el-table-column label="练习次数" align="left" width="100">0</el-table-column>
        <el-table-column label="激活状态" align="left" prop="description" width="100">
          <template #default="scope">
            <div class="sms flex">
              <span> {{ scope.row.activateFlag ? '是' : '否' }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <div class="button-container">
          <el-checkbox v-model="checkAll" label="全选" :indeterminate="isIndeterminateModel" @change="handleCheckAllChange" />&nbsp;&nbsp;&nbsp;
        </div> -->
        <div class="button-container">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" :disabled="multipleSelection.length === 0" @click="handleSubmit">确 定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ChooseUser" lang="ts">
import { unPractiseUserList } from '@/api/script';
import { userResult } from '@/api/user/types'
import { ElTable } from 'element-plus'
// import { useFormItem } from 'element-plus'
const emits = defineEmits(['update:modelValue', 'update:visible'])
const props = defineProps<{
  modelValue: userResult[],
  /* 脚本Id */
  scriptId: string,
  visible: boolean
}>()
const tableRef = ref<InstanceType<typeof ElTable>>()
const userList = ref<userResult[]>([]);
const loading = ref(false)
const multipleSelection = ref<userResult[]>([])
const scrollElem = ref()
const selectedItem = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    // formItem?.validate('blur')
  }
})

// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

watch(() => props.visible, (val) => {
  if (val) {
    getList()
  }
})



const getList = async () => {
  loading.value = true;
  const res = await unPractiseUserList({ scriptId: props.scriptId });
  userList.value = res.data;
  loading.value = false;
  //设置选择
  multipleSelection.value = selectedItem.value.length > 0 ? selectedItem.value : [...res.data]
  if (selectedItem.value.length > 0 && selectedItem.value.length < res.data.length) {
    toggleSelection(multipleSelection.value)
  } else {
    tableRef.value!.toggleAllSelection()
  }

}

/**多选 */
const handleSelectionChange = (val: userResult[]) => {
  multipleSelection.value = val
}

/**设置多选 */
const toggleSelection = (rows?: userResult[]) => {
  console.log(rows)
  if (rows) {
    rows.forEach((el) => {
      nextTick(() => {
        const row = userList.value!.find(e => e.id === el.id); // row是当前表格数据
        row && tableRef.value!.toggleRowSelection(row, true);
      })
    })
  } else {
    tableRef.value!.clearSelection()
  }
}

const close = () => {
  closeDialog()
}

const handleSubmit = () => {
  selectedItem.value = multipleSelection.value
  closeDialog()
}



const closeDialog = () => {
  multipleSelection.value = []
  tableRef.value!.clearSelection()
  visibleFlag.value = false
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.module.scss';

.knowledge-select {
  width: 100%;
}

.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

}


.flex {
  display: flex;
  align-items: center;
  gap: 0 10px;
}
</style>
