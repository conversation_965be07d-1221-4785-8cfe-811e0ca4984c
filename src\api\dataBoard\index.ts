import request from '@/utils/request';

import {
  ChatTableQuery,
  UserChatResult,
  ScriptChatResult,
  DataAnalyseVo,
  UserDataDetailAnalyseVo,
  ChatReportDetailVo,
  ChatHistoryDetailVo,
  ScriptAnalyseVo,
  ScriptDataListAnalyseVo,
  ChatReportVo,
  PPTSummaryVo
} from './types';
import axios, { AxiosPromise } from 'axios';

// 查询学员信息列表
export function getUserChatStatics(query: ChatTableQuery): AxiosPromise<UserChatResult[]> {
  return request({
    url: '/biz/kanban/getUserChatStatics',
    method: 'get',
    params: query
  });
}

// 查询脚本信息列表
export function getScriptStatics(query: ChatTableQuery): AxiosPromise<ScriptChatResult[]> {
  return request({
    url: '/biz/kanban/getScriptStatics',
    method: 'get',
    params: query
  });
}
export function getUserDataAnalyse(query: {
  scriptTypes: number[];
  startTime: string;
  endTime: string;
  deptIds: string[];
}): AxiosPromise<DataAnalyseVo> {
  return request({
    url: '/biz/kanban/userDataAnalyse',
    method: 'get',
    params: query
  });
}
//脚本数据昨天4个统计
export function getYesterdayScriptDataAnalyse(query: { startTime: string; endTime: string; deptId: string }): AxiosPromise<any> {
  return request({
    url: '/biz/kanban/yesterdayScriptDataAnalyse',
    method: 'get',
    params: query
  });
}

export function getScriptRangeDataAnalyse(query: { startTime: string; endTime: string; deptId: string }): AxiosPromise<any[]> {
  return request({
    url: '/biz/kanban/scriptRangeDataAnalyse',
    method: 'get',
    params: query
  });
}
//学员详情分析
export function getUserDataDetailAnalyse(query: {
  startTime: string;
  endTime: string;
  userId: number;
  scriptId?: string;
  scriptTypes: number[];
}): AxiosPromise<UserDataDetailAnalyseVo> {
  return request({
    url: '/biz/kanban/userDataDetailAnalyse',
    method: 'get',
    params: query
  });
}

//学员脚本对话记录
export function getUserChatRecord(query: {
  startTime: string;
  endTime: string;
  userId?: number;
  script?: number;
  scriptId?: string;
}): AxiosPromise<ChatReportVo[]> {
  return request({
    url: '/biz/kanban/userChatRecord',
    method: 'get',
    params: query
  });
}

//报告详情
export function getChatReport(id: number): AxiosPromise<any> {
  return request({
    url: '/biz/chatReport/' + id,
    method: 'get'
  });
}

//重新生成报告
export function reGenerateReport(id: number): AxiosPromise<any> {
  return request({
    url: '/biz/chatReport/' + id,
    method: 'post'
  });
}

//对话记录
export function getChatHistory(id: number): AxiosPromise<ChatHistoryDetailVo[]> {
  return request({
    url: '/biz/chatHistory/' + id,
    method: 'get'
  });
}

//脚本详情分析
export function scriptAnalyse(query: {
  startTime: string;
  endTime: string;
  scriptId: string;
  deptIds: string[];
  timeArray?: string[];
}): AxiosPromise<ScriptAnalyseVo> {
  return request({
    url: '/biz/kanban/scriptAnalyse',
    method: 'get',
    params: query
  });
}

export function scriptDataAnalyse(query: {
  startTime: string;
  endTime: string;
  deptIds: string[];
  timeArray?: string[];
}): AxiosPromise<ScriptDataListAnalyseVo> {
  return request({
    url: '/biz/kanban/scriptDataAnalyse',
    method: 'get',
    params: query
  });
}

export function getFetchPCMData(url: string): Promise<any> {
  return new Promise<void>((resolve, reject) => {
    axios
      .get(url, {
        responseType: 'arraybuffer'
      })
      .then(function (response: any) {
        // 处理ArrayBuffer数据
        resolve(response.data);
      })
      .catch(function (error: any) {
        reject();
      });
  });
}

export function getPPTSummaryDetail(chatId: string): AxiosPromise<PPTSummaryVo> {
  return request({
    url: `/biz/chatHistory/pptSummary/${chatId}`,
    method: 'GET'
  });
}
