<template>
  <el-dialog
    v-model="visibleShow"
    width="300"
    align-center
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onClose"
    @open="onOpen"
  >
    <div class="container">
      <Vue3Lottie width="200px" height="200px" :animation-data="loadJson" />
      <div class="title">{{ loading? '脚本生成中' : '生成失败' }}</div>
      <div class="tip" v-if="loading">预计需要1分钟时间，请耐心等待</div>
      <el-button class="btn" v-if="!loading" type="primary" @click="handleRegenerate">重试</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { getGenerateResult, regenerateScript } from '@/api/script'
import loadJson from "@/assets/icons/json/loading.json";
const props = defineProps({
  visible: {
    type: <PERSON><PERSON>an,
    default: false
  },
  scriptId: {
    type:String,
    default: ''
  },
  params: {
    type: Object
  }
})
const emits = defineEmits(['update:visible', 'onSuccess'])
const timer = ref()
const visibleShow = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const state = reactive({
  loading: true
})

const {loading} = toRefs(state)

const requestResult = () => {
  timer.value = setInterval(async () => {
    try {
     const res = await getGenerateResult(props.scriptId)
     if(res.data !== null) {
        clearInterval(timer.value)
        if(res.data.generateSuccess) {
          // 生成成功
          emits('onSuccess', props.scriptId)
        } else {
          loading.value = false
        }
      }
    } catch (error) {
      clearInterval(timer.value)
      loading.value = false
    }
  }, 10000)
}

const handleRegenerate = async () => {
  state.loading = true
  try {
    try {
      await regenerateScript({...props.params, scriptId: props.scriptId})
      requestResult()
    } catch (error) {
      console.log(error);
    }

  } catch (error) {
    console.log(error);
  }

}
const onClose = () => {
  clearInterval(timer.value)
}

const onOpen = () => {
  if (props.scriptId) {
    requestResult()
  }
}
</script>

<style scoped lang="scss">
.container {
  text-align: center;
}
.title {
  font-size: 16px;
}
.tip {
  font-size: 14px;
  margin-top: 20px;
  margin-left: 46px;
  margin-right: 46px;
  color: #999999;
}
.btn {
  margin-top: 20px;
}
</style>
