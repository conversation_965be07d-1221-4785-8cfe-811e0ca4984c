<template>
  <v-chart ref="chart" :style="{ height, width }" :option="option" :autoresize="true" />
</template>

<script setup>
import { use } from 'echarts/core'
import { SVGRenderer } from 'echarts/renderers' // 图表默认为SVG渲染，数据量大时使用Canvas渲染器
import { <PERSON><PERSON><PERSON>,Bar<PERSON><PERSON>,PictorialBarChart } from 'echarts/charts'
import VChart from 'vue-echarts'
const chart = ref(null)
// import { merge } from 'lodash'
import {
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  MarkAreaComponent,
  MarkLineComponent,
  DataZoomComponent
} from 'echarts/components'
const { proxy } = getCurrentInstance()
const props = defineProps({
  width: {
    type: String,
    default: '100px'
  },
  height: {
    type: String,
    default: '100px'
  }
})

use([
  SV<PERSON>enderer,
  LineChart,
  TitleComponent,
  <PERSON><PERSON><PERSON>,
  GridComponent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  LegendComponent,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  Mark<PERSON><PERSON><PERSON>omponent,
  <PERSON><PERSON>oomComponent
])
const width = window.innerWidth * 0.04;
const option = ref({
  tooltip: {
    trigger: 'axis'
    // axisPointer: {
    //   type: 'cross',
    //   label: {
    //     backgroundColor: '#6a7985'
    //   }
    // }
  },
  grid: {
    top: 30,
    bottom: '1px',
    left: 40,
    right: 30,
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false
    },
    axisLine: {
      lineStyle: {
        color: '#E8E8E8'
      }
    },
    axisLabel: {
      fontSize: 14,

      color: '#666666'
    }
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
    axisLabel: {
      fontSize: 14,
      color: 'rgba(102,102,102,0.85)',
      fontWeight: 400
    },
    splitLine: {
      lineStyle: {
        color: '#E8E8E8'
      }
    }
  },
  series: [
    {
      data: [],
      type: 'line',
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#34C7A5',
        borderColor: '#fff',
        borderWidth: 1
      },
      lineStyle: {
        width: 2
      }
    }
  ]
})

const setOptions = params => {
  option.value = Object.assign(option.value, params)
}
onMounted(() => {
  // 实现宽度自适应屏幕
  window.addEventListener('resize', resizeTheChart())
})
onBeforeUnmount(() => {
  if (chart.value) {
    // return;
    // console.log('图表销毁')
    chart.value.dispose();
    chart.value = null;
  }

  // 实现宽度自适应屏幕
  window.removeEventListener('resize', resizeTheChart())
})
function resizeTheChart() {
  if (proxy.$refs.chart) {
    // 实现宽度自适应屏幕
    proxy.$refs.chart.resize()
  }
}

defineExpose({
  setOptions
})
</script>

<style scoped></style>
