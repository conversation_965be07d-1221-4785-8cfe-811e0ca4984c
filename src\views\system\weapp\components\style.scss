.sub-title {
  font-size: 14px;
  color: #9E9E9E;
}
.select-box {
  display: flex;
  justify-content: center;
  gap: 25px;
}
.select-item {
  &-name {
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 8px;
    color: #000000;
  }
  &-preview {
    width: 259px;
    height: 316px;
    border-radius: 12px;
    border-width: 2px;
    border-style: solid;
    border-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f1f1f1;
    cursor: pointer;
    position: relative;

    &.active {
      border-color: var(--el-color-primary);
    }
  }

  &-img {
    width: auto;
    height: 90%;
  }

  &-default {
    color: #fff;
    background-color: var(--el-color-primary);
    position: absolute;
    right: 0;
    top: 0;
    padding: 3px 10px;
    font-size: 12px;
    border-radius: 0 8px 0 12px;
  }
}
// .el-button, .el-button:focus:not(.el-button:hover){
//   border: none !important;
// }