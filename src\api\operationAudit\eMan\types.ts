import { ProfessionalCertificationVo } from '../professionalCertification/types';

export interface eManQuery extends PageQuery {
  keyword: string;
  status: string;
}
export interface eManListResult {
  id: number | string | undefined;
  username: string;
  auditTime: string;
  name: string;
  professionalCertificationStatus: string | number;
  status: string;
  submitTime: string;
}
/**职业详情 */
export interface eManCertificationVo {
  id: number | string | undefined;
  name: string;
  avatar: string;
  background: string;
  title: string;
  tone: string;
  occupation: string;
  department: string;
  organization: string;
  userId: number | string | undefined;
  skill: string;
  personality: '';
  status: number | string;
  gender: number;
  professionalCertificationVo: ProfessionalCertificationVo & { auditRemark: string };
}
