/**
 * PromptTemplateVo
 */
export interface PromptTemplateVo {
  /**
   * 是否检测通过
   */
  checkFlag: boolean;
  createTime: Date;
  id: number;
  /**
   * 名称
   */
  name: string;
  /**
   * 新的提示词
   */
  newPrompt: string;
  /**
   * 上线状态
   * false-未上线
   * true + newPrompt为空 已发布
   * true + newPrompt不为空 有修改未发布
   */
  onlineFlag: boolean;
  /**
   * 现有提示词
   */
  prompt: string;
  /**
   * 类型
   */
  type: number;
  updateTime: Date;
}
/**
 * PromptTemplateDetailVo
 */
export interface PromptTemplateDetailVo {
  promptTemplate: PromptTemplateVo;
  promptTemplateHistory: PromptTemplateHistoryVo[];
  [property: string]: any;
}

/**
 * PromptTemplateVo
 */
export interface PromptTemplateVo {
  /**
   * 是否检测通过
   */
  checkFlag: boolean;
  createTime: Date;
  id: number;
  /**
   * 名称
   */
  name: string;
  /**
   * 新的提示词
   */
  newPrompt: string;
  /**
   * 上线状态
   * false-未上线
   * true + newPrompt为空 已发布
   * true + newPrompt不为空 有修改未发布
   */
  onlineFlag: boolean;
  /**
   * 现有提示词
   */
  prompt: string;
  /**
   * 类型
   */
  type: number;
  updateTime: Date;
  [property: string]: any;
}

/**
 * PromptTemplateHistoryVo
 */
export interface PromptTemplateHistoryVo {
  /**
   * 更新内容
   */
  content: string;
  createBy: string;
  createTime: Date;
  /**
   * 提示词
   */
  prompt: string;
  [property: string]: any;
}
export interface PromptPreviewVo {
  emanId: number | string;
  prompt: string;
  scriptId: number | string;
  type: number;
  userInput: string;
  [property: string]: any;
}
