<template>
  <div class="knowledge-select">
    <el-button type="primary" @click="handleChoose">添加</el-button>
    <div class="chosen-list">
      <div class="chosen-list-item" v-for="item in selectedItem" :key="item">
        <div class="flex">
          <img src="@/assets/icons/png/file.png" v-if="item.type === 1" />
          <img src="@/assets/icons/png/fileimg.png" v-if="item.type === 2" />

          <span class="chosen-list-name">{{ item.name }}</span>
        </div>

        <el-icon class="chosen-list-delete" @click="deleteItem(item)">
          <Close />
        </el-icon>
      </div>
    </div>
  </div>

  <el-dialog
    ref="formDialogRef"
    title="选择知识库"
    v-model="visibleDialog"
    width="750px"
    append-to-body
    :close-on-click-modal="false"
    :show-close="true"
  >
    <div class="box-container">
      <div
        class="list-wrapper"
        ref="scrollElem"
        style="overflow: auto;"
        :infinite-scroll-distance="1"
        v-infinite-scroll="load"
        :infinite-scroll-disabled="noMore"
      >
        <el-table :loading="loading" :data="knowledgeBaseList" ref="tableRef">
          <el-table-column label="名称" align="left" prop="name" />
          <el-table-column label="文件数量" align="left" prop="fileNum" />
          <el-table-column label="类型" align="left" prop="type">
            <template #default="scope">
              {{ scope.row.type === 2 ? '图片' : '文本' }}
            </template>
          </el-table-column>
          <el-table-column label="描述" align="left" prop="description">
            <template #default="scope">
              <div class="description">
                {{ scope.row.description || '-' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200" label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                :type="hoverId === scope.row.id ? 'danger' : 'default'"
                v-if="isIn(scope.row.id)"
                class="btn-chosen btn-action"
                @click="handleDelete(scope)"
                @mouseenter="hoverId = scope.row.id"
                @mouseleave="hoverId = null"
                >{{ hoverId === scope.row.id ? '移除' : '已添加' }}</el-button
              >
              <el-button class="btn-action" type="primary" v-else @click="handleSelect(scope)">添加</el-button>
            </template>
          </el-table-column>
        </el-table>
        <p class="nomore" v-if="knowledgeBaseList.length > 0 && noMore">已加载全部内容</p>
      </div>
    </div>
  </el-dialog>
</template>

<script setup name="chooseKnowledge" lang="ts">
import { getKnowledgeBaseList } from '@/api/knowledgeBase';
import { KnowledgeBaseVo, knowledgeListQuery } from '@/api/knowledgeBase/types'
import { useFormItem } from 'element-plus'
const emits = defineEmits(['update:modelValue'])


const props = defineProps<{ modelValue: KnowledgeBaseVo[] }>()
const total = ref(1)
const visibleDialog = ref(false)

const { formItem } = useFormItem()

const knowledgeBaseList = ref<KnowledgeBaseVo[]>([]);
const noMore = ref(false)
const loading = ref(false)
const scrollElem = ref()

const hoverId = ref<number | string | null>(null)

const selectedItem = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    formItem?.validate('blur')
  }
})



const data = reactive<PageQueryData<knowledgeListQuery>>({
  queryParams: {
    pageNum: 0,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);

const deleteItem = (data: KnowledgeBaseVo) => {
  selectedItem.value = selectedItem.value.filter(item => item.id !== data.id)
}

const load = async () => {
  if (loading.value) return;
  if (knowledgeBaseList.value.length < total.value) {
    loading.value = true;
    queryParams.value.pageNum += 1;
    const res = await getKnowledgeBaseList(queryParams.value);
    total.value = res.total
    knowledgeBaseList.value = [...knowledgeBaseList.value, ...res.rows];
    if (knowledgeBaseList.value.length === res.total) {
      noMore.value = true;
    }
    loading.value = false;
  }
}

const handleChoose = () => {
  visibleDialog.value = true
  nextTick(() => {
    scrollElem.value.scrollTo({ top: 0, behavior: 'smooth' });
  })
}

const handleSelect = (payload: any) => {
  // 判断是否已选择，没有则添加
  if (!selectedItem.value) {
    selectedItem.value = [payload.row]
  } else {
    if (!selectedItem.value.find(item => item.id === payload.row.id)) {
      selectedItem.value.push(payload.row)
    }
  }

}
const handleDelete = (payload: any) => {
  deleteItem(payload.row)
}

const isIn = (id: number) => {
  return selectedItem.value && selectedItem.value.find(item => item.id === id)
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.module.scss';

.knowledge-select {
  width: 100%;
}

.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

}


.list-wrapper {
  height: 500px;
  overflow: auto;
}

.flex {
  display: flex;
  gap: 0 10px;
}

.chosen-list {
  margin-top: 10px;

  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    background-color: rgb(244, 246, 255);
    margin-bottom: 10px;
    border-radius: 4px;

    img {
      width: 30px;
      height: 30px
    }
  }

  &-delete {
    cursor: pointer;
    font-size: 18px;
  }
}

.btn-chosen {
  &:hover {
    // color: $--color-danger;
  }
}

.btn-action {
  width: 60px;
  box-sizing: border-box;
}

.nomore {
  text-align: center;
  font-size: 12px;
  color: #999999;
}

.description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 定义文本的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
