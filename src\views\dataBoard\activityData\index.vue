<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">闯关数据</span>
        </div>
      </template>
      <div class="box-container">
        <el-table v-loading="loading" :data="dataList" @row-click="gotoActivityGroup">
          <el-table-column label="闯关名称" align="left" prop="name" />

          <el-table-column label="关卡数量" align="left" prop="scriptList" width="100">
            <template #default="scope">
              {{ scope.row.scriptList.length }}
            </template>
          </el-table-column>

          <el-table-column label="活动时间" align="left" prop="startDate" width="380">
            <template #default="scope">
              {{ scope.row.startDate + '-' +  scope.row.endDate }}
            </template>
          </el-table-column>
          <el-table-column label="进度" align="left" prop="filterPoints" width="150">
            <template #default="scope">
              {{ progressMap.get(scope.row.progress) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" align="left" prop="filterPoints" width="150">
            <template #default="scope">
              {{ statusMap.get(scope.row.status) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button link type="primary" text @click="gotoActivityGroup(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getLevelList } from "@/api/eventLevel";
import { LevelVo } from '@/api/script/types';
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const router = useRouter();
const dataList = ref<LevelVo[]>([])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const statusMap = new Map([
  [1, '待上线'],
  [2, '已上线'],
])
const progressMap = new Map([
  [0, '进行中'],
  [1, '待开始'],
  [2, '已结束']
])


const loading = ref(false);
const total = ref(0);
const { queryParams } = toRefs(data);




const gotoActivityGroup = (row: LevelVo) => {
  router.push({
    name: 'Activity-group', query: {
      id: row.id,
    }
  });
}

const getList = async () => {
  const res = await getLevelList(queryParams.value)
  dataList.value = res.rows;
  total.value = res.total;
  loading.value = false;
  proxy?.$modal.closeLoading();
}



onUnmounted(() => {
  // cache.session.remove('pointQuery')
})
onMounted(() => {
  proxy?.$modal.loading("正在加载");
  getList()
})
</script>
<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  align-items: center
}

.export {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-end;
  margin: 0 auto;
  // padding: 0 30px 30px 30px;
  width: 100%;

}

.box-title {
  justify-content: flex-end;

  // width: 100%;
  :deep(.el-form-item--default) {
    margin-bottom: 0px;
  }

  :deep(.el-form-item) {
    margin-right: 16px;
  }
}
</style>
