<template>
  <div class="level-container">
    <div
      class="list-wrapper"
      ref="scrollElem"
      style="overflow: auto;"
      :infinite-scroll-distance="1"
      v-infinite-scroll="getList"
      :infinite-scroll-disabled="noMore"
    >
      <div class="list">
        <div class="level-item column box" @click="detailLevelGroup(item)" v-for="(item, index) in LevelList" :key="index">
          <div class="title">{{ item.name }}</div>
          <div class="num">{{ item.scriptList.length }}个关卡</div>
          <div class="level-detail flex" v-for="(it, i) in item.scriptList.slice(0, 6)" :key="i">
            <div class="dot" :style="{ backgroundColor: colorMap.get(it.status) }"></div>
            <span>{{ it.name }}</span>
          </div>

          <!-- <div class="level-detail flex">
          <div class="dot" :style="{ backgroundColor: '#34E831' }"></div>
          脚本名称1
        </div> -->
          <div class="level-detail flex" v-if="item.scriptList.length > 6">
            <!-- <div class="dot" :style="{ backgroundColor: '#34E831' }"></div> -->
            ……
          </div>
          <div class="level-detail"></div>
          <div class="level-detail"></div>
          <div class="level-detail"></div>
          <div class="bottom flex">
            <div class="left flex" style="color: #999999;">{{ item.status === 1 ? '待上线' : '已上线' }}</div>
            <div class="right flex">
              <span @click.stop>
                <el-popover trigger="click" placement="top" :ref="(el: refItem) => handleSetPopMap(el, item.id)" :width="300" popper-class="popover">
                  <div style="display: flex;flex-direction: column;gap:8px 0;">
                    <span class="title">{{ item.status === 1 ? '确定上线吗？' : '确定下线吗？' }}</span>
                    <span class="message">{{ item.status === 1 ? '上线后学员需完成任务以解锁脚本' :
      '下线后所有脚本无需解锁即可使用。重新上线后，用户的解锁状态会保持上线前的状态，已解锁的保持已解锁，未解锁的保持未解锁。' }}</span>
                  </div>
                  <div style="text-align: right; margin: 0;margin-top: 30px;">
                    <el-button @click="cancelRemove(item.id)">取消</el-button>
                    <el-button type="primary" @click.stop="confirm(item)">确定</el-button>
                  </div>
                  <template #reference>
                    <div v-if="item.status === 2">
                      <el-tooltip content="下线" placement="top">
                        <img src="@/assets/icons/png/online.png" class="icon" />
                      </el-tooltip>
                    </div>
                    <div v-else-if="item.status === 1">
                      <el-tooltip content="上线" placement="top">
                        <img src="@/assets/icons/png/downline.png" class="icon" />
                      </el-tooltip>
                    </div>
                  </template>
                </el-popover>
              </span>
              <div @click.stop="editLevelGroup(item)">
                <el-tooltip content="编辑" placement="top">
                  <img src="@/assets/icons/png/edit.png" class="icon" />
                </el-tooltip>
              </div>
              <span @click.stop>
                <el-popover
                  trigger="click"
                  placement="top"
                  :ref="(el: refItem) => handleSetDeletePopMap(el, item.id)"
                  :width="300"
                  popper-class="popover"
                >
                  <div style="display: flex;flex-direction: column;gap:8px 0;">
                    <span class="title">确定要删除该闯关吗？</span>
                    <span class="message"
                      >该操作无法恢复，请谨慎操作。删除后所有脚本无需解锁即可使用，所有用户的脚本解锁状态将重置，不论是否已解锁，下次设置新的闯关时，都需要重新解锁。</span
                    >
                  </div>
                  <div style="text-align: right; margin: 0;margin-top: 30px;">
                    <el-button @click="cancelDelete(item.id)">取消</el-button>
                    <el-button type="primary" @click.stop="confirmDelete(item)">确定</el-button>
                  </div>
                  <template #reference>
                    <div v-show="item.status === 1">
                      <el-tooltip content="删除" placement="top">
                        <img src="@/assets/icons/png/del.png" class="icon" />
                      </el-tooltip>
                    </div>
                  </template>
                </el-popover>
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- <p class="nomore" v-if="noMore">已加载全部内容</p> -->
    </div>
  </div>
</template>

<script setup lang="ts" name="levelgroup">
import { getLevelList, onlineLevelScriptGroup, offlineLevelScriptGroup, getOfflineScript, deleteLevel } from '@/api/script';
import { LevelVo } from '@/api/script/types';
import { ElLoading } from 'element-plus'
const emits = defineEmits(['change-type']);
const LevelList = ref<LevelVo[]>([]);
const loading = ref(true);
const total = ref(1);
const noMore = ref(false)
const requestLoading = ref(false)
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// const dropdown1 = ref<DropdownInstance>()

type refItem = Element | ComponentPublicInstance | null;
const onOrOffLinePopverRefMap = ref({});
const deletePopverRefMap = ref({});


const handleSetPopMap = (el: refItem, item: string) => {
  if (el) {
    onOrOffLinePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelRemove = (id) => {
  onOrOffLinePopverRefMap.value[`Pop_Ref_${id}`].hide()
}


const handleSetDeletePopMap = (el: refItem, item: string) => {
  if (el) {
    deletePopverRefMap.value[`Pop_Ref_${item}`] = el;
  }
}
const cancelDelete = (id) => {
  deletePopverRefMap.value[`Pop_Ref_${id}`].hide()
}



const colorMap = new Map([
  [2, '#5EEE3A'],
  [1, '#D9D9D9'],
])
const data = reactive<PageQueryData>({
  queryParams: {
    pageNum: 0,
    pageSize: 30,
  }
});

const { queryParams } = toRefs(data);

const initList = () => {
  queryParams.value.pageNum = 0
  total.value = 1
  // noMore.value = false
  requestLoading.value = false
  LevelList.value = []
  getList()
}




/** 查询列表 */
const getList = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    target: document.querySelector('.level-container') // 设置加载动画区域
  })
  if (requestLoading.value) return;
  if (LevelList.value.length < total.value) {
    requestLoading.value = true;
    queryParams.value.pageNum += 1;
    const res = await getLevelList(queryParams.value);
    total.value = res.total
    LevelList.value = [...LevelList.value, ...res.rows];
    if (LevelList.value.length === res.total) {
      noMore.value = true;
    }
    requestLoading.value = false;
  }
  loading.close()
}


const addLevelGroup = () => {
  emits('change-type', { flagType: 'add-level-group' })
}

const detailLevelGroup = (item: any) => {
  emits('change-type', { flagType: 'edit-level-group', isEdit: false, id: item.id })
}


const editLevelGroup = (item: any) => {
  emits('change-type', { flagType: 'edit-level-group', isEdit: true, id: item.id })
}

const confirm = async (item: any) => {
  onOrOffLine(item)
}


const onOrOffLine = async (item: LevelVo) => {
  if (item.status === 1) {
    const res = await getOfflineScript({ levelId: item.id })
    if (res.data.length > 0) {
      let text = '可能会导致后续关卡无法完成。'
      res.data.forEach((element: string) => {
        text += `<li>${element}</li>`
      });
      ElMessageBox.confirm(
        text,
        '以下脚本未上线，确定上线闯关吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'none',
        }
      )
        .then(async () => {
          loading.value = true;
          await onlineLevelScriptGroup({ id: item.id })
          proxy.$modal.msgSuccess('上线成功');
          initList()

        })
        .catch(() => {
        })

    } else {
      await onlineLevelScriptGroup({ id: item.id })
      proxy.$modal.msgSuccess('上线成功');
      initList()
    }
  } else {
    await offlineLevelScriptGroup({ id: item.id })
    proxy.$modal.msgSuccess('下线成功');
    initList()
  }
}



/**删除 */
const confirmDelete = async (item) => {
  await deleteLevel(item.id)
  proxy.$modal.msgSuccess('删除成功');
  initList()
}
// onMounted(() => {
//   getList();
// })
</script>
<style scoped lang="scss">
.flex {
  display: flex;
}

.column {
  flex-direction: column;
}

.level-container {
  width: 100%;
}

.list {
  display: flex;
  margin: 24px 0;
  width: 100%;
  gap: 1vw;
  padding: 0 16px;
  flex-wrap: wrap;

  .box {
    width: 32.4%;
    height: 300px;
    display: flex;
    border-radius: 12px;
    box-shadow: 0 0 10px 1px #dddddd;
    border: 2px solid #fff;
    cursor: pointer;

    &:hover {
      border: 2px solid #4F66FF;
      background: #fbfcff;

    }
  }

  .add-group {
    align-items: center;
    gap: 20px 0;
    flex-direction: column;
    color: #4F66FF;
    justify-content: center;
    box-shadow: 0 0 10px 1px #dddddd;

    // &:hover {
    //   animation: float 1s 1;
    // }
  }

  .level-item {
    box-sizing: border-box;
    padding: 10px 16px 0 16px;
    position: relative;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .num {
    margin: 8px 0;
    color: #979797;
    font-size: 14px;
  }

  .level-detail {
    align-items: center;
    gap: 0 8px;
    color: #979797;
    font-size: 14px;

    span {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .bottom {
    border-top: 1px solid #dddddd;
    justify-content: space-between;
    position: absolute;
    width: calc(100% - 32px);
    bottom: 0;
    box-sizing: border-box;
    padding: 10px;
    left: 16px;

    .left {
      align-items: center;
      font-size: 14px;
    }

    .right {
      gap: 0 8px;
    }
  }

  .icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }

  .dot {
    width: 9px;
    height: 9px;
    border-radius: 50%;
  }
}

.list-wrapper {
  height: calc(100vh - 84px - 110px - 100px);
  overflow: auto;
}

.nomore {
  text-align: center;
  font-size: 12px;
  color: #999999;
}
</style>
