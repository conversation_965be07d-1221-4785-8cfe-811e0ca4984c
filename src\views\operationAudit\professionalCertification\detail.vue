<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            职业认证审核</span
          >
          <div class="right-button" v-show="auditForm.status === '' && auditDetail.status === 2">
            <el-button plain type="info" @click="refuseDialogVisible = true">驳回</el-button>
            <el-popconfirm width="220" confirm-button-text="确定" cancel-button-text="取消" @confirm="_audit('3')" title="确定审核通过吗?">
              <template #reference>
                <el-button type="primary">通过</el-button>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </template>
      <div class="box-container">
        <div class="box-title">职业认证</div>
        <div class="user-msg">
          <div class="left">姓名</div>
          <div class="right">
            <span>{{ auditDetail.name }}</span>
            <div v-if="sameList.length > 0">
              （有{{ sameList.length }}个同名<span style="color:rgb(112,178,255);cursor: pointer;" @click="open()">查看</span>）
            </div>
            <div v-else>（无同名）</div>
          </div>
        </div>
        <div class="user-msg">
          <div class="left">科室</div>
          <div class="right">
            <span>{{ auditDetail.department }}</span>
          </div>
        </div>
        <div class="user-msg">
          <div class="left">职称</div>
          <div class="right">
            <span>{{ auditDetail.title }}</span>
          </div>
        </div>
        <div class="user-msg">
          <div class="left">第一执业医院</div>
          <div class="right">
            <span>{{ auditDetail.organization }}</span>
          </div>
        </div>
        <div class="user-msg">
          <div class="left">资质凭证</div>
          <div class="right" style="flex-wrap: wrap;">
            <el-image
              v-for="(item, index) in auditDetail.attachmentList"
              :key="index"
              style="width: 100px; height: 100px;margin: 15px;"
              :src="item.fileLocation"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="auditDetail.attachmentList.map((item: any) => item.fileLocation)"
              :initial-index="index"
              fit="cover"
            />
          </div>
        </div>
      </div>
      <el-divider />
      <div class="box-container">
        <div class="box-title">E主信息</div>
        <div class="user-msg">
          <div class="left">昵称</div>
          <div class="right">
            <span>{{ auditDetail.userName }}</span>
          </div>
        </div>
        <div class="user-msg">
          <div class="left">手机号</div>
          <div class="right">
            <span>{{ auditDetail.phoneNumber }}</span>
          </div>
        </div>
        <div class="user-msg">
          <div class="left">注册时间</div>
          <div class="right">
            <span v-formatTime="auditDetail.registerTime"></span>
          </div>
        </div>
      </div>
      <el-divider />
      <div class="box-container">
        <div class="box-title">审核日志</div>
        <el-table v-loading="loading" :data="auditList">
          <el-table-column label="时间" align="left" prop="operateTime" width="200px">
            <template #default="scope">
              <span v-formatTime="scope.row.operateTime"></span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" align="left" prop="operatorName" width="200px" />
          <el-table-column label="操作" align="left" prop="operateName" width="200px" />
          <el-table-column label="备注" align="left" prop="remark">
            <template #default="scope">
              {{ !scope.row.remark ? '无' : scope.row.remark }}
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
      <template #footer>
        <div class="footer">
          <!-- <el-button text type="primary" @click="back()">返回首页</el-button> -->
        </div>
      </template>
    </el-card>
  </div>

  <el-dialog v-model="refuseDialogVisible" title="审核驳回" width="500" align-center>
    <div class="refuse-list">
      <el-radio-group v-model="radio1" class="refuse-list">
        <div v-for="(item, index) in audit_remark" :key="index">
          <el-radio :label="item.value" size="large"></el-radio>
        </div>
        <div>
          <el-radio label="其他" size="large" class="other"></el-radio>
          <el-input v-model="refuseReason" style="width: 340px" v-show="radio1 === '其他'" placeholder="请输入驳回原因" />
        </div>
      </el-radio-group>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="refuseDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="checkRadio()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog v-model="auditResultDialogVisible" :show-close="false" width="300" center align-center>
    <div class="result">
      <svg-icon v-if="auditForm.status === '3'" icon-class="check-one" class="status-success" />
      <svg-icon v-if="auditForm.status === '4'" icon-class="close-one" class="status-error" />

      <span v-if="auditForm.status === '3'"> 审核已通过 </span>
      <span v-if="auditForm.status === '4'"> 审核已驳回 </span>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button>返回首页</el-button> -->
        <el-button type="primary" @click="auditResultDialogVisible = false"> 关闭 </el-button>
        <el-button type="primary" v-if="props.from==='eMan'" text @click="backeMan()"> 返回E人审核 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="professionalCertification" lang="ts">
import { getAuditDetail, getAuditRecordList, sameNameCheck, audit } from '@/api/operationAudit/professionalCertification';
import { AuditVo, ProfessionalCertificationVo, AuditQueryVo, AuditFormVo } from "@/api/operationAudit/professionalCertification/types";
// import router from '@/router';
import router from '@/router';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import { ElLoading } from 'element-plus'
const { audit_remark } = toRefs<any>(proxy?.useDict("audit_remark"));
const emits = defineEmits(['change-type'])
// const route = useRoute();
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  from:{
    type: String,
    default: ''
  },
})

const radio1 = ref('')
const refuseReason = ref('')
const auditList = ref<AuditVo[]>([]);
const loading = ref(true);
const refuseDialogVisible = ref(false) //驳回弹出框
const auditResultDialogVisible = ref(false) //审核结果弹出框
const total = ref(0);
const sameList = ref<string[]>([])
const queryParams = reactive<AuditQueryVo>({
  pageNum: 1,
  pageSize: 5,
  mainId: props.id
});
const auditDetail = ref<ProfessionalCertificationVo>({
  id: '',
  name: '',
  department: '',
  title: '',
  status: '',
  userId: '',
  userName: '',
  phoneNumber: '',
  registerTime: '',
  organization:'',
  attachmentList: []
})


const auditForm = ref<AuditFormVo>({
  id: props.id,
  status: '',
  remark: '',
})
const back = () => {
  //  router.back()
  emits('change-type', { flagType: 'List', isReload: true })
}

const backeMan=()=>{
   router.back()

}

/**驳回理由弹窗 */
const checkRadio = () => {
  console.log(radio1.value)
  if (!radio1.value && !refuseReason.value) {
    ElMessageBox.alert('请选择驳回理由', '提示', {
      confirmButtonText: '关闭'
    })
    return
  }
  _audit('4')
  refuseDialogVisible.value = false
}

/**检查同名弹窗 */
const open = () => {
  const text = sameList.value.join("</br>")
  ElMessageBox({
    title:'同名用户账号',
    message:text,
    dangerouslyUseHTMLString:true,
    confirmButtonText: '关闭'
  })
}

const _audit = async (status: string) => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  auditForm.value.status = status
  auditForm.value.remark = radio1.value === '其他' ? refuseReason.value : radio1.value
  await audit(auditForm.value)
  loading.close()
  //驳回
  auditResultDialogVisible.value = true
  getList()
}

/** 查询审核列表 */
const getList = async () => {
  loading.value = true;
  const res = await getAuditRecordList(queryParams);
  auditList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

const _getAuditDetail = async () => {
  const res = await getAuditDetail({ id: props.id });
  auditDetail.value = res.data
}

const _sameNameCheck = async () => {
  const res = await sameNameCheck({ id: props.id });
  sameList.value = res.data
}

onMounted(() => {
  _getAuditDetail();
  _sameNameCheck();
  getList();
})
</script>
<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;

}
.flex{
  display: flex;
  gap:0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.user-msg {
  display: flex;
  align-items: flex-start;

  .left {
    width: 200px;
    color: #272C47;
  }

  .right {
    display: flex;
    align-items: center;
    color: #67686F;
    gap: 0 10px;
  }
}

:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}

:deep(.el-table) {
  .el-table__inner-wrapper {
    height: auto;
  }
}

.refuse-list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px 0;
  width: 90%;
  margin: 0 auto;
}

.other {
  display: flex;
  align-items: center;
}

.result {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 12px 0;
}
.status-success {
  width: 60px;
  height: 60px;
  color: var(--el-color-success);
}
.status-error {
  width: 60px;
  height: 60px;
  color: var(--el-color-error);
}
</style>
