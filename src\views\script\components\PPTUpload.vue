<template>
  <div>
    <el-dialog
      class="dialog-container"
      fullscreen
      ref="formDialogRef"
      v-model="visibleFlag"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :show-close="false"
      id="drop-area"
      align-center
    >
      <div class="fullscreen-container" v-show="uploadFlag">
        <div class="upload-file-container"></div>
        <el-upload
          drag
          :multiple="limit !== 1"
          :action="uploadFileUrl"
          :before-upload="handleBeforeUpload"
          name="file"
          :on-error="handleUploadError"
          :on-exceed="handleExceed"
          :on-success="handleUploadSuccess"
          :show-file-list="true"
          :limit="limit"
          :on-remove="handleRemove"
          :headers="headers"
          class="upload-file-uploader"
          ref="fileUploadRef"
        >
          <div class="text-container">
            <div class="full-title">将图片拖动到此即可上传</div>
            <div class="full-tips">支持JPG、JPEG、PNG格式，单图尺寸不超过3000px，不小于300px，大小不超过10MB。</div>
          </div>
        </el-upload>
      </div>

      <el-card>
        <template #header>
          <div class="card-title">
            {{ title }}
          </div>
        </template>
        <div class="box-container">
          <div class="upload-inner" :style="{ zIndex: uploadFlag ? 4 : 2 }">
            <el-form ref="formParamsRef" label-width="100px" label-position="left">
              <div class="merge-method" @click.stop>
                <el-form-item label-width="150px" label-position="left">
                  <template #label>
                    <span class="label-text">上传文件</span>
                  </template>
                  <div class="upload-file">
                    <!-- 上传按钮 -->
                    <!-- <el-button type="primary">选取文件</el-button> -->
                    <el-upload
                      :multiple="limit !== 1"
                      :action="uploadFileUrl"
                      :before-upload="handleBeforeUpload"
                      :file-list="fileList"
                      name="file"
                      :on-error="handleUploadError"
                      :on-exceed="handleExceed"
                      :on-success="handleUploadSuccess"
                      :show-file-list="true"
                      :limit="limit"
                      :on-remove="handleRemove"
                      :headers="headers"
                      ref="fileUploadRef"
                    >
                      <div class="upload-container">
                        <img src="@/assets/icons/png/upload.png" />
                        <div class="right">
                          <div style="color: #000000;">将图片拖到此处，或点击上传</div>
                          <div class="tips">支持JPG、JPEG、PNG格式，单图尺寸不超过3000px，不小于300px，大小不超过10MB。</div>
                        </div>
                      </div>
                    </el-upload>
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" :loading="importLoading" @click="handleSubmit" :disabled="fileList.length === 0">确 定</el-button>
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup name="PPTUpload" lang="ts">
import { globalHeaders } from "@/utils/request";
import { propTypes } from '@/utils/propTypes';
import { ElMessageBox } from 'element-plus'
import { delOss } from "@/api/system/oss";
import { deepClone } from "@/utils";
const emits = defineEmits(['update:visible', 'success'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const fileList = ref<any[]>([])
const number = ref(0);
const uploadList = ref<any[]>([]);
const importLoading = ref(false)

let loading: any = null
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/resource/oss/upload"); // 上传文件服务器地址
const headers = ref(globalHeaders());
const fileUploadRef = ref<ElUploadInstance>();

const uploadFlag = ref(false) //ture 全屏
// let uploadLoadingInstance: any = null

// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type:Number,
    default: 1
  },
  // 大小限制(MB)
  fileSize: propTypes.number.def(10),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def(['jpg','jpeg', 'png', 'JPG', 'JPEG', 'PNG']),
  // 是否显示提示
  isShowTip: propTypes.bool.def(true),

})


// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})
const getImageSize = (file:any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = e => {
      const image = new Image()

      image.onload = () => {
        console.log(image.width, image.height, 'image size')
        resolve({
          width: image.width,
          height: image.height
        })
      }
      image.onerror = () => {
        reject(new Error('Invalid image file'))
      }
      image.src = e.target.result
    }
    reader.readAsDataURL(file)
  })
};

// 上传前校检格式和大小
const handleBeforeUpload = async (file: any) => {
  console.log('handleBeforeUpload', file)
  importLoading.value
  uploadFlag.value = false;
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    console.log(isTypeOk)
    if (!isTypeOk) {
      importLoading.value = false;
      // uploadFlag.value = false;
      proxy?.$modal.msgError(`文件格式不正确, 请上传JPG、JPEG、PNG格式文件!`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      importLoading.value = false;
      uploadFlag.value = false;
      proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
    const { width, height } = await getImageSize(file)
    if((width > 3000 || width < 300)||(height > 3000 || width < 300)) {
      proxy?.$modal.msgError(`图片尺寸不正确，不超过3000px，不小于300px!`);
      return false;
    }
  }

  loading=ElLoading.service({
    lock: true,
    text: '正在上传,请稍候...',
  })

  number.value++;
  console.log(number.value, 'handleBeforeUpload')
  return true;
}

// 文件个数超出
const handleExceed = () => {
  importLoading.value = false;
  proxy?.$modal.msgError(`上传文件数量不能超过${props.limit}个!`);
}




// 上传失败
const handleUploadError = (file: any) => {
  if (number.value > 0) {
    number.value--;
  }
  // fileUploadRef.value?.handleRemove(file);
  // fileList.value = []
  // emit("update:modelValue", fileList.value);
  uploadFlag.value = false;
  importLoading.value = false;
  proxy?.$modal.msgError("上传文件失败");
}
const handleRemove = (file: UploadFile) => {
  if (file.status === 'success') {
    let ossId = file.ossId;
    delOss(ossId);
  }

}
// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  // uploadLoadingInstance?.close()

  console.log(res, file, 'handleUploadSuccess')
  importLoading.value = false
  //  loading.close();
  uploadFlag.value = false;
  if (res.code === 200) {
    uploadList.value.push({ name: res.data.fileName, url: res.data.url, ossId: res.data.ossId });
    uploadedSuccessfully();
  } else {
    number.value--;
     loading.close();
    proxy?.$modal.msgError(res.msg);
    fileUploadRef.value?.handleRemove(file);
  }
}


// 上传结束处理
const uploadedSuccessfully = () => {
  // console.log(deepClone(fileList.value), number.value, uploadList.value, 'uploadedSuccessfully')
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
     loading.close();
  }
}



const closeDialog = () => {
  if (fileList.value.length > 0) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要取消吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        fileList.value = []
        uploadList.value = []
        visibleFlag.value = false
      })
      .catch(() => {
      })
  } else {
    visibleFlag.value = false
  }

}

const handleSubmit = async () => {
  emits('success', fileList.value)
  visibleFlag.value = false;
  fileList.value = []
  visibleFlag.value = false
}
onMounted(() => {
  // console.log(draggingCounter.value)
  var last = null;
  let elem = document.getElementById('drop-area')
  document.addEventListener('dragenter', function (e) {
    last = e.target; // 记录最后进入的元素
    // console.log('dragenter', e.target);
    // elem.classList.add('content');
    uploadFlag.value = true
  });
  document.addEventListener('dragleave', function (e) {
    // 如果此时退的元素是最后进入的元素，说明是真正退出了`drop-area`元素
    if (last === e.target) {
      // console.log('dragleave', e.target);
      // elem.classList.remove('content');
      uploadFlag.value = false

      e.stopPropagation();
      e.preventDefault();//事件冒泡和浏览器默认事件还是要阻止的不然不触发dropEvent事件
    }
  });

})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 20px;
  // font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  //display: flex;
  //flex-direction: column;
  position: relative;
  //max-height:0px;
}
:deep(.el-dialog) {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-card {
    margin-top: 0;
  }
}
.fullscreen-container {
  width: 100%;
  height: 100%;
  position: fixed !important;
  z-index: 9999;
  left: 0;
  top: 0;

  :deep(.el-upload) {
    width: 100%;
    height: 100%;
  }
}

// 背景
.upload-file-container {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  filter: blur(5px);
  backdrop-filter: blur(5px);
  position: absolute !important;
}

.upload-file-uploader {
  margin-bottom: 5px;

  position: absolute;
  z-index: 10000;
  width: 100%;
  height: 100%;

}

.text-container {
  width: 95%;
  margin: 0 auto;
  border-radius: 40px;
  height: 100%;
  border: 1px dashed #c1ced6;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .full-tips,
  .full-title {
    color: #ffffff;
  }

  .full-title {
    font-size: 24px;
    font-weight: bold;
  }

  .full-tips {
    width: 500px;
    text-align: center;
    margin-top: 24px;
    font-size: 20px;
  }
}


.flex {
  display: flex;
  align-items: center;
}

.upload-file {
  width: 100%;
}

.upload-inner {
  //position: absolute;
  padding: 16px;
  width: 100%;
  // z-index: 4;
}

.rotating-image {
  animation: rotate 2s linear infinite;
}



.upload-container {
  margin: 0 auto;
  display: flex;
  align-items: flex-start;
  gap: 0 16px;
  width: 100%;
  border-radius: 4px;
  border: 1px dashed #c1ced6;
  box-sizing: border-box;
  padding: 16px;

  img {
    width: 48px;
    height: 48px;
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;

    .tips {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
      color: #999999;
      line-height: 28px;
    }
  }
}

.file-container {
  padding: 12px;
  width: 100%;
  background-color: var(--el-color-primary-light-9);
  box-sizing: border-box;

  .left {
    width: 90%;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}

:deep(.is-dragover) {
  // border: 1px dashed #4f66ff;
  // background-color: rgba($color: #a0abed, $alpha: 0.1) !important;
}

:deep(.el-upload) {
  width: 100%;
  // height: 250px;
}
:deep(.el-upload-list) {
  max-height: 500px;
  overflow-y: auto;
}

:deep(.el-upload-dragger) {
  height: 100%;
  border: none;
  background: none;
}

.dialog-container {
  background-color: transparent !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
