<template>
  <div class="p-2">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <template v-slot:header>
            <div class="clearfix">
              <span>个人信息</span>
            </div>
          </template>
          <div>
            <div class="text-center">
              <userAvatar />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                企业名称
                <div class="pull-right">{{ state.tenant.companyName }}</div>
              </li>
              <li class="list-group-item">
                账号
                <div class="pull-right">{{ state.user.userName }}</div>
              </li>
              <li class="list-group-item">
                用户昵称
                <div class="pull-right">{{ state.user.nickName }}</div>
              </li>
              <li class="list-group-item">
                所属角色
                <div class="pull-right">{{ state.roleGroup }}</div>
              </li>
              <li class="list-group-item">
                创建日期
                <div class="pull-right">{{ state.user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <template v-slot:header>
            <div class="clearfix">
              <span>修改密码</span>
            </div>
          </template>
          <div class="card-body-reset">
            <resetPwd />
          </div>
          <!-- <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="userForm" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd />
            </el-tab-pane>
            <el-tab-pane label="第三方应用" name="thirdParty">
              <thirdParty :auths="state.auths" />
            </el-tab-pane>
          </el-tabs> -->
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Profile" lang="ts">
import UserAvatar from "./userAvatar.vue";
import UserInfo from "./userInfo.vue";
import ResetPwd from "./resetPwd.vue";
import ThirdParty from "./thirdParty.vue";
import { getAuthList } from "@/api/system/social/auth";
import { getUserProfile } from "@/api/system/user";
import {getInfo} from '@/api/login'
const activeTab = ref("userinfo");
const state = ref<Record<string, any>>({
    tenant: {},
    user: {},
    roleGroup: '',
    postGroup: '',
    auths: []
});

const userForm = ref({});

const getUser = async () => {
    const res = await getUserProfile();

    state.value.user = res.data.user;
    userForm.value = { ...res.data.user }
    state.value.roleGroup = res.data.roleGroup;
    state.value.postGroup = res.data.postGroup;
};

const getInfos = async () => {
  const res = await getInfo()
  state.value.tenant = res.data.tenant;
}

const getAuths = async () => {
    const res = await getAuthList();
    state.value.auths = res.data;
};

onMounted(() => {
    getUser();
    getInfos();
    getAuths();
})
</script>
<style lang="scss" scoped>
.card-body-reset {
  padding-top: 50px;
  padding-bottom: 50px;
}
</style>
