import request from '@/utils/request';
import { EmanDetailVo, EmanQuery, EmanFormVo } from './types';
import { AxiosPromise } from 'axios';

// 查询E人列表
export function getEManList(query: EmanQuery): AxiosPromise<EmanDetailVo[]> {
  return request({
    url: '/biz/eman',
    method: 'get',
    params: query
  });
}
/**上下线 */
export function onlineEMan(id: string | number): AxiosPromise<EmanDetailVo[]> {
  return request({
    url: `/biz/eman/${id}/online`,
    method: 'post'
  });
}
export function offlinEMan(id: string | number): AxiosPromise<EmanDetailVo[]> {
  return request({
    url: `/biz/eman/${id}/offline`,
    method: 'post'
  });
}
/**删除 */
export function deleteEMan(id: string | number): AxiosPromise<EmanDetailVo[]> {
  return request({
    url: `/biz/eman/${id}`,
    method: 'delete'
  });
}
/**详情 */
export function getEmanDetail(id: string | number): AxiosPromise<EmanDetailVo> {
  return request({
    url: `/biz/eman/${id}`,
    method: 'get'
  });
}
/**创建 */
export function createEman(data: EmanFormVo): AxiosPromise<any> {
  return request({
    url: `/biz/eman`,
    method: 'post',
    data
  });
}
/**修改 */
export function updateEman(data: EmanFormVo): AxiosPromise<any> {
  return request({
    url: `/biz/eman/${data.id}`,
    method: 'put',
    data
  });
}
