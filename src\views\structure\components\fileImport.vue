<template>
  <div>
    <el-dialog ref="formDialogRef" v-model="visibleFlag" width="800px" append-to-body :close-on-click-modal="false" :show-close="false">
      <template #title>
        {{ title }}
      </template>
      <div class="box-container" v-if="statusName === 'import'">
        <div class="box">
          <div class="box-title">1.下载导入模板</div>
          <div class="box-tips">根据提示信息完善表格内容</div>
          <el-button type="primary" @click="open">下载模板表格</el-button>
        </div>
        <div class="box">
          <div class="box-title">2.上传完善后的模板</div>
          <div class="box-tips">更新模板中的信息后上传</div>
          <el-upload
            multiple
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            :limit="1"
            :on-change="handleUploadChange"
            :drag="fileList.length === 0"
            v-model:file-list="fileList"
            :on-error="handleUploadError"
            :on-success="handleUploadSuccess"
            :on-exceed="handleExceed"
            :show-file-list="false"
            :headers="headers"
            class="upload-file-uploader"
            ref="fileUploadRef"
            :auto-upload="false"
          >
            <!-- 上传按钮 -->
            <!-- <el-button type="primary">选取文件</el-button> -->
            <div class="upload-container" v-if="fileList.length === 0">
              <img src="@/assets/icons/png/upload.png" />
              <div class="right">
                <div>将文档拖到此处，或点击上传</div>
              </div>
            </div>
            <div class="upload-container" v-else>
              <img src="@/assets/icons/png/excel.png" />
              <div class="right">
                <div>{{ fileList[0].name }}</div>
                <el-button type="primary">重新选择</el-button>
              </div>
            </div>
          </el-upload>
          <!-- <FileUpload :isShowTip="false" ></FileUpload> -->
        </div>
      </div>
      <div class="box-container" v-else-if="statusName === 'create-dept'">
        <span>系统中未发现有下述部门，是否自动创建</span>
        <div class="dept-list">
          <div class="box" v-for="(item, index) in deptList" :key="index">{{ item }}</div>
        </div>
      </div>
      <div class="box-container" v-else-if="statusName === 'import-result'">
        <div class="result-container">
          <div class="result">
            <el-icon color="#52C41A" size="18">
              <SuccessFilled />
            </el-icon>
            导入成功{{ successNum }}人
          </div>
          <div class="result">
            <el-icon color="#F5222D" size="18">
              <CircleCloseFilled />
            </el-icon>
            导入失败{{ failureNum }}人
          </div>
        </div>
        <div class="error">点击<span class="link" @click="_export">下载错误报告</span>，修改后重新导入</div>
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="statusName === 'import'">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="importLoading" :disabled="fileList.length === 0">导入</el-button>
        </div>
        <div class="dialog-footer" v-if="statusName === 'create-dept'">
          <el-button @click="back">取消</el-button>
          <el-button @click="importFn(true)" :loading="skipLoading">跳过并继续</el-button>
          <el-button type="primary" @click="importFn(false)" :loading="createLoading">创建</el-button>
        </div>
        <div class="dialog-footer" v-if="statusName === 'import-result'">
          <!-- <el-button type="primary">取消</el-button> -->
          <el-button type="primary" @click="complete">完成</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { genFileId, ElMessageBox } from 'element-plus'
import type { UploadRawFile } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import { userImportData, exportErrorData } from "@/api/user";
// import { downloadByData } from '@/utils'
const emits = defineEmits(['update:visible', 'success']);
import { globalHeaders, download } from "@/utils/request";
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/biz/dept/importCheck"); // 上传文件服务器地址
const headers = ref(globalHeaders());
// const fileFlag = ref<boolean>(true);
const fileType = ref<string[]>(['xls', 'xlsx'])
const fileName = ref<string>('')
const currentFile = ref<any>(null)
const fileUploadRef = ref()
const importLoading = ref(false)
const createLoading = ref(false)
const skipLoading = ref(false)
const fileList = ref([])
const failureNum = ref<number>(0)
const successNum = ref<number>(0)
const errorListCacheId = ref<string>('')


const deptList = ref<string[]>([])
const statusName = ref('import')
// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '导入'
  },
  scriptId: {
    type: String,
    default: ''
  },
})
const titleMap = new Map([
  ['import', '批量导入'],
  ['create-dept', '创建新部门'],
  ['import-result', '导入结果']
])

const title = computed(() => titleMap.get(statusName.value))

// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const open = () => {
  window.open(import.meta.env.VITE_APP_OSS_PREFIX + '/excel_template/%E7%94%A8%E6%88%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx')
}

const _export = async () => {
  // const res = await exportErrorData({ errorListCacheId: errorListCacheId.value })
  // console.log(res)
  download('/biz/user/exportErrorData', { errorListCacheId: errorListCacheId.value }, '用户导入错误报告.xlsx')
}

const complete = () => {
  closeDialog()
  emits('success')
}


const closeDialog = () => {
  if (currentFile.value) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要取消吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        visibleFlag.value = false
        initData()
      })
      .catch(() => {
      })
  } else {
    visibleFlag.value = false
    initData()
  }
}

const back = () => {
  statusName.value = 'import';
  nextTick(() => {
    fileUploadRef.value!.handleStart(currentFile.value)
  })
}


// 上传结束处理
const uploadedSuccessfully = () => {
  // fileFlag.value = false
  currentFile.value = null
  fileName.value = ''
  fileList.value = []
  importLoading.value = false
  // proxy?.$modal.closeLoading();
}



// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {

  // 校检文件大小
  // if (props.fileSize) {
  //   const isLt = file.size / 1024 / 1024 < props.fileSize;
  //   if (!isLt) {
  //     proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
  //     return false;
  //   }
  // }

  fileName.value = file.name
  currentFile.value = file
  // proxy?.$modal.loading("正在上传文件，请稍候...");
  // number.value++;
  return true;
}

const handleUploadChange = (file: any) => {
  if (fileType.value.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join("/")}格式文件!`);
      fileUploadRef.value?.handleRemove(file);
      return false;
    }
    importLoading.value = false
  }
}

const handleExceed = (files: any) => {
  fileUploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  fileUploadRef.value!.handleStart(file)
}


// 上传失败
const handleUploadError = () => {
  proxy?.$modal.msgError("上传文件失败");
}

// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  if (res.code === 200) {
    if (res.data.length === 0) {
      //返回的是新增部门 新增为0->导入
      _userImportData(false)
      uploadedSuccessfully();
    } else {
      statusName.value = 'create-dept'
      deptList.value = res.data
      createLoading.value = false
      importLoading.value = false
    }
  } else {
    // number.value--;
    // proxy?.$modal.closeLoading();
    proxy?.$modal.msgError(res.msg);
    fileUploadRef.value?.handleRemove(file);
    uploadedSuccessfully();
  }
}

const importFn = (flag: boolean) => {
  if (flag) skipLoading.value = true
  else createLoading.value = true
  _userImportData(flag)
}
const handleSubmit = async () => {
  importLoading.value = true
  fileUploadRef.value!.submit()
}
const _userImportData = async (flag: boolean) => {
  const res = await userImportData({ file: currentFile.value, skipDept: flag })

  if (res.data.failureNum === 0) {
    //失败人数为0，导入成功
    proxy?.$modal.msgSuccess("导入成功");
    emits('success')
    initData() //初始化数据
    visibleFlag.value = false
  } else {
    initData() //初始化数据
    failureNum.value = res.data.failureNum
    successNum.value = res.data.successNum
    errorListCacheId.value = res.data.errorListCacheId
    statusName.value = 'import-result'
  }
}




const initData = () => {
  currentFile.value = null
  fileName.value = ''
  fileList.value = []
  importLoading.value = false
  skipLoading.value = false
  createLoading.value = false
  statusName.value = 'import'
}
</script>

<style scoped lang="scss">
.box-container {
  display: flex;
  flex-direction: column;
  gap: 8px 0;
}

.dept-list {
  display: flex;
  flex-direction: column;
}

.result-container {
  display: flex;
  align-items: center;
  background: rgba(247, 249, 255, 0.8);
  justify-content: center;
  padding: 32px;
  border-radius: 4px;
  gap: 0 32px;
}

.link {
  color: #4F66FF;
  cursor: pointer;
}

.result {
  display: flex;
  align-items: center;
  gap: 0 4px;
}

.box {
  padding: 16px 32px;
  background: rgba(247, 249, 255, 0.8);

  &-title {
    font-weight: bold;
    font-size: 16px;
  }

  &-tips {
    font-size: 12px;
    color: #999999;
    margin: 12px 0;
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}



.upload-container {
  margin: 0 auto;
  display: flex;
  gap: 0 16px;
  width: 100%;

  border-radius: 4px;

  img {
    width: 48px;
    height: 48px;
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    gap: 8px 0;

    .tips {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
      color: #999999;
    }

  }
}
</style>
