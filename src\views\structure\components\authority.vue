<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      :title="title"
      v-model="visibleAdd"
      width="700px"
      append-to-body
      @closed="onClosed"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" label-width="120px" v-loading="loading" label-position="left">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="小程序数据看板" prop="deptId">
                <el-radio-group v-model="formParams.type" style="display: flex;align-items: flex-start;">
                  <el-radio :value="0" :label="0">
                    <div class="flex">
                      <span>不开启</span>
                    </div>
                  </el-radio>
                  <el-radio :value="1" :label="1">
                    <div class="flex">
                      <span>开启</span>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="formParams.type === 1">
            <el-col :span="24">
              <el-form-item>
                <el-select v-model="formParams.dataScope">
                  <el-option v-for="item in dataScopeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="tips" style="margin-bottom: 22px; " v-if="formParams.type === 1">
            {{dataScopeOptions.find(item => item.value === formParams.dataScope)?.description}}
          </div>
          <div class="dept-container" v-if="formParams.type === 1 && formParams.dataScope === '2'">
            <div class="left">
              <!-- <el-input v-model="filterText" placeholder="搜索部门" /> -->
              <el-tree
                show-checkbox
                @check="checkChange"
                ref="treeRef"
                node-key="id"
                v-loading="treeLoading"
                :data="deptOptions"
                :default-expanded-keys="defaultExpandedKeys"
                :default-checked-keys="formParams.userDeptIds"
                :props="defaultProps"
                :default-expand-all="false"
              />
            </div>
          </div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="添加到白名单" prop="whiteFlag">
                <el-switch v-model="formParams.whiteFlag" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="报告分享权限" prop="shareFlag">
                <el-switch v-model="formParams.shareFlag" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="formParams.shareFlag">
            <el-col :span="24">
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item prop="shareFlag">
                    <template #label>
                      <span class="label-text">报告查看权限</span>
                      <el-tooltip content="若对该权限进行修改，全员的查看权限也将同步更新。" placement="top">
                        <el-icon class="label-tip">
                          <img src="@/assets/icons/svg/help1.svg" class="help" />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <el-select v-model="formParams.shareReportPermission" @change="changeShareReportPermission">
                      <el-option v-for="item in dataShareOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <!-- <div class="tips" style="margin-bottom: 22px; ">设为白名单后将不参与练习数据统计，且不占用席位数。</div> -->
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
// import user from "@/api/system/user";
import { batchUpdateDataScope, getUserDetail } from "@/api/user/index"
import { deepClone } from "@/utils/index";
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user';
import { getDeptTreeList } from "@/api/department";
import { TreeLong } from "@/api/department/types";
const userStore = useUserStore();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success'])
const formParamsRef = ref()
const btnLoading = ref(false)
const loading = ref(false)
const deptOptions = ref<TreeLong[]>([])
const defaultExpandedKeys = ref<string[]>([])
const treeRef = ref()
const defaultProps = {
  children: 'children',
  label: 'label',
}
const treeLoading = ref(false)
let initForm = {}

/** 数据范围选项*/
const dataScopeOptions = [
  { value: "1", label: "全部部门的数据", description: '可在小程序查看全部部门的数据' },
  { value: "4", label: "所在部门（含子部门）的数据", description: '可在小程序查看所在部门（含子部门）的数据' },
  { value: "2", label: "指定部门数据", description: '可在小程序查看指定部门的数据' }

]

/** 数据范围选项*/
const dataShareOptions = [
  { value: 1, label: "仅组织架构内成员可查看" },
  { value: 2, label: "公开，获得链接的人均可查看" }

]

const formParams = ref(deepClone(initForm))
// import type { FormRules } from 'element-plus'
// interface RuleForm {
//     type: number,
//     dataScope: string
// }

// const rules = reactive<FormRules<RuleForm>>({
//     type: [
//         { required: true, message: '请选择部门', trigger: 'change' },
//     ],
// })

watch(() => props.visible, val => {
  if (val) {
    if (props.ids.length === 1) {
      treeRef.value?.setCheckedKeys([]) //重置
      _getUserDetail()
    } else {
      initForm = {
        type: 0,
        dataScope: '4',
        whiteFlag: false,
        shareFlag: false,
        userDeptIds: [],
        shareReportPermission: 1
      }
      formParams.value = deepClone(initForm)
    }
  }
})
watch(() => formParams.value.dataScope, val => {
  if (val === '2') {
    _getDeptTreeList()
  }
})

const changeShareReportPermission = (value: any) => {
  if (value === 2) {
    ElMessageBox.alert('该功能开启后，获得报告分享链接的任何人员都能查看该报告。请注意信息安全，防止相关信息泄露。', '提示', {
      confirmButtonText: '确定'
    })
  }

}

const _getUserDetail = async () => {
  loading.value = true
  const { data } = await getUserDetail({ id: props.ids[0] as string });
  loading.value = false
  initForm = {
    type: data.kanbanFlag as unknown as boolean ? 1 : 0,
    dataScope: data.dataScope,
    whiteFlag: data.whiteFlag,
    shareFlag: data.shareFlag,
    userDeptIds: data.userDeptIds || [],
    shareReportPermission: data.shareReportPermission||1
  }
  formParams.value = deepClone(initForm)
}

const _getDeptTreeList = async () => {
  treeLoading.value = true
  const res = await getDeptTreeList()
  treeLoading.value = false
  if (res.data) {
    deptOptions.value = res.data
    defaultExpandedKeys.value = res.data.length > 0 ? [res.data[0].id] : []
  }
  // deptOptions.value = res.data
}

const checkChange = () => {
  const array = treeRef.value!.getCheckedNodes()
  formParams.value.userDeptIds = array.map((item: any) => item.id)
}

// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '设置'
  },
  ids: {
    type: Array,
    default: () => []
  },
  dataScope: {
    type: Object,
    default: () => { }
  },
})


// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})






const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(initForm[key]))
      ) {
        bool = true;
        return;
      }
    } else {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(initForm[key])
      ) {
        bool = true;
        return;
      }
    }

  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}



const closeDialog = () => {
  loading.value = true
  loading.value = false
  visibleAdd.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        kanbanFlag: formParams.value.type === 1,
        userIdList: props.ids as string[],
        dataScope: formParams.value.dataScope,
        userDeptIds: formParams.value.userDeptIds,
        whiteFlag: formParams.value.whiteFlag,
        shareFlag: formParams.value.shareFlag,
        shareReportPermission: formParams.value.shareReportPermission
      };
      btnLoading.value = true
      await batchUpdateDataScope(params).then((res) => {
        proxy.$modal.msgSuccess('保存成功');
        closeDialog()
        emits('success')
      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
const onClosed = () => {

  console.log('onClosed');
  formParamsRef.value.resetFields()
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.tips {
  color: #999999;
  margin-left: 120px;
}

:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}

.dept-container {
  // margin: 30px;
  // box-sizing: border-box;
  width: 80%;
  margin-left: 120px;
  // margin: 0 auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  min-height: 30vh;
  // position: relative;
  display: flex;
  padding: 8px 0;
  overflow: auto;
  max-height: 50vh;

  .left {
    width: 100%;

  }
}

.divider {
  // height: 100%;
  // width: 1px;
  // background-color: var(--el-border-color);
}

:deep(.el-tree) {
  // .el-tree-node__content {
  //   display: block !important;
  // }
  display: inline-block;
  min-width: 100%;

  .el-tree-node__children {
    overflow-x: visible !important;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}

.help {
  width: 16px;
  margin-left: 8px;
}
</style>
