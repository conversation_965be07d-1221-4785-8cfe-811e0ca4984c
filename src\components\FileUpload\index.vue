<template>
  <div class="upload-file">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      drag
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :on-remove="handleRemove"
      :show-file-list="true"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUploadRef"
    >
      <!-- 上传按钮 -->
      <!-- <el-button type="primary">选取文件</el-button> -->
      <div class="upload-container">
        <img src="@/assets/icons/png/upload.png" />
        <div class="right">
          <div>{{ title }}</div>
          <div class="tips" v-if="showTip">{{ fileTypeText }}</div>
        </div>
      </div>
    </el-upload>
    <!-- 上传提示
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
<template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
的文件
</div> -->
    <!-- 文件列表 -->
    <!-- <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>
        </div>
      </li>
    </transition-group> -->
  </div>
</template>

<script setup lang="ts">
import { listByIds, delOss } from "@/api/system/oss";
import { propTypes } from '@/utils/propTypes';
import { globalHeaders } from "@/utils/request";
import { string } from "vue-types";

const props = defineProps({
    modelValue: [String, Object, Array],
    // 数量限制
    limit: propTypes.number.def(100),
    // 大小限制(MB)
    fileSize: propTypes.number.def(50),
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: propTypes.array.def(["doc", "xls", "ppt", "txt", "pdf"]),
    fileTypeText: propTypes.string.def('支持的文件格式：PDF、Word文档（DOC、DOCX)、Excel表 格(xLSX)、PPT (PPT、PPTX)、cSV、各类纯文本格式等'),
    // 是否显示提示
    isShowTip: propTypes.bool.def(true),
    title: {
        type: string,
        default: '将文档拖到此处，或点击上传'
    }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref<any[]>([]);

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/resource/oss/upload"); // 上传文件服务器地址
const headers = ref(globalHeaders());

const fileList = ref<any[]>([]);
const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
);

const fileUploadRef = ref<ElUploadInstance>();

watch(() => props.modelValue, async val => {
    if (val) {
        let temp = 1;
        // 首先将值转为数组
        let list = [];
        if (Array.isArray(val)) {
            list = val;
        } else {
            const res = await listByIds(val as string)
            list = res.data.map((oss) => {
                const data = { name: oss.originalName, url: oss.url, ossId: oss.ossId };
                return data;
            });
        }
        // 然后将数组转为对象数组
        fileList.value = list.map(item => {
            item = { name: item.name, url: item.url, ossId: item.ossId };
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
        });
    } else {
        fileList.value = [];
        return [];
    }
}, { deep: true, immediate: true });

// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
    // console.log(file)
    // 校检文件类型
    if (props.fileType.length) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1].toLowerCase();;
        const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
            proxy?.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
            return false;
        }
    }
    // 校检文件大小
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    proxy?.$modal.loading("正在上传文件，请稍候...");
    number.value++;
    return true;
}

// 文件个数超出
const handleExceed = () => {
    proxy?.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
const handleUploadError = () => {
    proxy?.$modal.msgError("上传文件失败");
}

// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
    if (res.code === 200) {
        uploadList.value.push({ name: res.data.fileName, url: res.data.url, ossId: res.data.ossId });
        uploadedSuccessfully();
    } else {
        number.value--;
        proxy?.$modal.closeLoading();
        proxy?.$modal.msgError(res.msg);
        fileUploadRef.value?.handleRemove(file);
        uploadedSuccessfully();
    }
}
const handleRemove = (file: UploadFile) => {
    console.log(file)
    if (file.status === 'success') {
        let ossId = file.ossId;
        delOss(ossId);
        let index = fileList.value.findIndex(item => item.ossId === file.ossId)
        fileList.value.splice(index, 1);
        emit("update:modelValue", fileList.value);
    }

    // let index = fileList.value.findIndex(item => item.ossId === file.ossId)

    // fileList.value.splice(index, 1);
    // console.log(fileList.value)
    // return

    //   if (number.value > 0) {
    //     number.value--;
    //   }
    //   // console.log(number.value)
    //   emit("update:modelValue", fileList.value);
}
// 删除文件
// const handleDelete = (index: number) => {
//     let ossId = fileList.value[index].ossId;
//     delOss(ossId);
//     fileList.value.splice(index, 1);
//     emit("update:modelValue", listToString(fileList.value));
// }

// 上传结束处理
const uploadedSuccessfully = () => {
    if (number.value > 0 && uploadList.value.length === number.value) {
        fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
        uploadList.value = [];
        number.value = 0;
        emit("update:modelValue", fileList.value);
        proxy?.$modal.closeLoading();
    }
}

// 获取文件名称
const getFileName = (name: string) => {
    // 如果是url那么取最后的名字 如果不是直接返回
    if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
    } else {
        return name;
    }
}

// 对象转成指定字符串分隔
const listToString = (list: any[], separator?: string) => {
    let strs = "";
    separator = separator || ",";
    list.forEach(item => {
        if (item.ossId) {
            strs += item.ossId + separator;
        }
    })
    return strs != "" ? strs.substring(0, strs.length - 1) : "";
}
</script>

<style scoped lang="scss">
.upload-file {
    width: 100%;
}

:deep(.el-upload) {
    width: 100%;
    // padding: 48px 24px;
    // border: 1px solid var(--el-border-color);?
    // background: transparent;
}

.upload-file-uploader {
    margin-bottom: 5px;
}

// .upload-file-uploader .el-upload-list__item.is-success:focus:not(:hover) {
//     display: none !important;
// }


.upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
}

.upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
}

.ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
}

.upload-container {
    margin: 0 auto;
    display: flex;
    gap: 0 16px;
    align-items: center;
    width: 100%;

    border-radius: 4px;

    img {
        width: 48px;
        height: 48px;
    }

    .right {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;

        .tips {
            text-align: left;
            font-weight: normal;
            font-size: 12px;
            line-height: 20px;
            color: #999999;
        }

    }
}

:deep(.el-upload-list__item) {
    transition: none !important;
}
</style>
