<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <!-- <span class="card-title">学员数据</span> -->
          <el-form style="width:100%" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
            <div class="box-title flex">
              <div class="user-info flex">
                <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />
                <img :src="analyseData.avatar || avatar_default_man" class="avatar" />
                <div class="">{{ analyseData.name }}</div>
              </div>
              <div class="flex">
                <el-form-item prop="scriptTypes">
                  <el-select
                    collapse-tags
                    @change="handleQuery"
                    collapse-tags-tooltip
                    :max-collapse-tags="1"
                    style="width: 240px;"
                    multiple
                    v-model="queryParams.scriptTypes"
                    placeholder="请选择脚本类型"
                  >
                    <el-option v-for="item in scriptTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item prop="time" style="margin-bottom: 0px;">
                  <el-date-picker
                    :clearable="false"
                    :disabled-date="disabledDate"
                    v-model="queryParams.timeArray"
                    type="daterange"
                    range-separator="-"
                    value-format="YYYY-MM-DD"
                    :shortcuts="shortcuts"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="margin-right: 12px;width: 280px;"
                  />
                  <!-- <el-radio-group v-model="currentSelected" @change="initDateArray()">
                <el-radio-button v-for="item in timeArray" :key="item.text" :label="item.text" :value="item.text" />
              </el-radio-group> -->
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
      </template>
      <div class="box-container">
        <div class="box-content flex">
          <div class="box-item">
            <div class="item-title">最高分</div>
            <div class="item-count">
              <span class="num">{{ analyseData.maxScore || 0 }}</span
              ><span class="unit">分</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title">平均分</div>
            <div class="item-count">
              <span class="num">{{ analyseData.averageScore || 0 }}</span
              ><span class="unit">分</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title">总时长</div>
            <div class="item-count">
              <span class="num">{{ totalTime.hours > 0 ? totalTime.hours : '' }}</span>
              <span class="unit" v-if="totalTime.hours > 0">小时</span>
              <span class="num">{{ totalTime.minutes > 0 ? totalTime.minutes : '' }}</span>
              <span class="unit" v-if="totalTime.minutes > 0">分</span>
              <span class="num">{{ totalTime.seconds }}</span>
              <span class="unit">秒</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title">平均时长</div>
            <div class="item-count">
              <span class="num">{{ averageTime.hours > 0 ? averageTime.hours : '' }}</span>
              <span class="unit" v-if="averageTime.hours > 0">小时</span>
              <span class="num">{{ averageTime.minutes > 0 ? averageTime.minutes : '' }}</span>
              <span class="unit" v-if="averageTime.minutes > 0">分</span>
              <span class="num">{{ averageTime.seconds }}</span>
              <span class="unit">秒</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title">总次数</div>
            <div class="item-count">
              <span class="num">{{ analyseData.totalCount }}</span
              ><span class="unit">次</span>
            </div>
          </div>
        </div>
      </div>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="序号" align="center" type="index" width="150" />
        <el-table-column label="脚本" align="center" prop="scriptName" />

        <el-table-column label="类型" align="center" prop="scriptType">
          <template #default="scope">
            <span v-if="scope.row.scriptType === 1">技巧类</span>
            <span v-else-if="scope.row.scriptType === 2">答题类</span>
            <span v-else-if="scope.row.scriptType === 3">幻灯片演练</span>
          </template>
        </el-table-column>
        <el-table-column label="拜访对象" v-if="company === 'kangyuan' && scriptType !== 3">
          <template #default="scope">
            <div v-if="scope.row.scriptType === 3">无</div>
            <div v-else>
              <div class="visit-object">
                <div>{{ scope.row.emanName || '' }}</div>
                <Star v-if="scope.row.emanDifficult" v-model="scope.row.emanDifficult" readonly size="mini" />
              </div>
              <div class="visit-title">
                {{ occupationList.slice(0, 4).includes(scope.row.occupation) ? scope.row.department ? (
                  scope.row.department.includes('-') ?
                    scope.row.department.split('-')[1] : scope.row.department) : '' : scope.row.occupation }}
                <span v-if="scope.row.title" style="margin: 0 2px;">|</span>
                {{ scope.row.title ? scope.row.title.includes('-') ? scope.row.title.split('-')[1] : scope.row.title :
                '' }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="时长" align="center" prop="timeLong">
          <template #default="scope">
            {{ scope.row.timeLong || '-' }}
          </template>
        </el-table-column>

        <el-table-column label="得分" align="center" prop="score">
          <template #default="scope">
            {{ scope.row.status === 2 ? scope.row.score : statusMap.get(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center" prop="createTime" />

        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="goTo(scope.row.id)" text>查看报告</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="user" lang="ts">
import {  onBeforeRouteLeave } from "vue-router";
import dayjs from 'dayjs'
// import { onBeforeRouteUpdate } from "vue-router";
import { getUserDataDetailAnalyse, getUserChatRecord } from "@/api/dataBoard";
import { ChatReportVo, UserDataDetailAnalyseVo } from "@/api/dataBoard/types";
const router = useRouter();
import avatar_default_man from "@/assets/images/avatar_default_man.jpg";
import { occupationList } from "@/views/eManManage/components/data";
import useUserStore from "@/store/modules/user";
import { getScriptDetail } from "@/api/script";
import cache from '@/plugins/cache';
// 1-未生成，2-已生成，3-生成失败，4-生成中
const userStore = useUserStore();
const company = import.meta.env.VITE_APP_COMPANY;
const statusMap = new Map([
  [1, '无报告'],
  [2, '已生成'],
  [3, '生成错误'],
  [4, '生成中'],
])
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const currentDate = dayjs();
      return [currentDate, currentDate]
    },
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day');
      return [yesterday, yesterday]
    },
  },
  {
    text: '近3天',
    value: () => {
      const startDate = dayjs().subtract(2, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近7天',
    value: () => {
      const startDate = dayjs().subtract(6, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近30天',
    value: () => {
      const startDate = dayjs().subtract(30, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
]

// const timeArray = [{
//   text: '今天',
//   num: '0',
//   unit: 'day'
// }, {
//   text: '昨天',
//   num: '0',
//   unit: 'day'
// },
// {
//   text: '近3天',
//   num: '2',
//   unit: 'day'
// },
// {
//   text: '近7天',
//   num: '6',
//   unit: 'day'
// }, {
//   text: '近30天',
//   num: '30',
//   unit: 'day'
// }]

const route = useRoute();
const dataList = ref<ChatReportVo[]>([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startTime: '',
    userId: (route.query.userId || 0) as number,
    endTime: '',
    scriptId: '',
    timeArray: [] as string[],
    scriptTypes: [] as number[]
  },
});
// const currentSelected = ref(route.query.selectTime)
const { queryParams } = toRefs(data);

const scriptType = ref<number>();


const scriptTypeOptions = ref([
  {
    value: 1,
    label: '技巧练习数据'
  },
  {
    value: 2,
    label: '答题练习数据'
  },
  {
    value: 3,
    label: '幻灯片练习数据'
  }
])


const analyseData = ref<UserDataDetailAnalyseVo>({
  maxScore: 0,
  averageScore: 0,
  totalTime: 0,
  averageTime: 0,
  totalCount: 0,
  avatar: '',
  name: '',
})

const totalTime = ref({
  hours: 0,
  minutes: 0,
  seconds: 0,
})

const averageTime = ref({
  hours: 0,
  minutes: 0,
  seconds: 0,
})


const disabledDate = (val: any) => {
  return val > new Date();
};
//监听日期选择
watch(
  () => queryParams.value.timeArray,
  (val) => {
    if (val && val.length > 0) {
      queryParams.value.startTime = val[0];
      queryParams.value.endTime = val[1];
      getList()
    } else {
      queryParams.value.startTime = '';
      queryParams.value.endTime = '';
      getList()
    }
  }
);

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

//时间选择
const handleDate = () => {
  const cacheQuery = cache.session.getJSON('memberDetailDataQuery')
  console.log(cacheQuery)
  const startTime = cacheQuery ? cacheQuery.startTime : route.query.startTime as string
  const endTime = cacheQuery?cacheQuery.endTime:route.query.endTime as string
  const pageNum = cacheQuery?.pageNum||1
  // console.log(route.query.scriptTypes)
  const scriptTypes = cacheQuery?cacheQuery.scriptTypes:route.query.scriptTypes? JSON.parse(route.query.scriptTypes as string):[]
  if(cacheQuery){
    cache.session.remove('memberDetailDataQuery')
  }
  queryParams.value.scriptTypes = scriptTypes
  queryParams.value.scriptId = route.query.scriptId as string
  queryParams.value.timeArray = [startTime, endTime]
  queryParams.value.pageNum = pageNum
  if (route.query.scriptId) {
    _getScriptDetail(route.query.scriptId as string)
  }
}

//查询列表
const getList = async () => {
  loading.value = true;
  const res = await getUserChatRecord(queryParams.value);
  res.rows.forEach((item: any) => {
    if (item.timeLong) {
      const hours = Math.floor(Number(item.timeLong) / 3600)
      const min = Math.floor((Number(item.timeLong) % 3600) / 60)
      const seconds = Number(item.timeLong) % 60; // 计算剩余秒数
      item.timeLong = hours > 0 ? hours + '小时' + min + '分' + seconds + '秒' : min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
    } else {
      item.timeLong = '0秒'
    }

  })
  dataList.value = res.rows;
  total.value = res.total;
  loading.value = false;
  _getUserDataAnalyse()
}

const _getUserDataAnalyse = async () => {
  const res = await getUserDataDetailAnalyse({ scriptTypes: queryParams.value.scriptTypes, startTime: queryParams.value.startTime + ' 00:00:00', endTime: queryParams.value.endTime + ' 23:59:59', userId: queryParams.value.userId, scriptId: queryParams.value.scriptId })
  secondsFormat(res.data.totalTime, 'totalTime')
  secondsFormat(res.data.averageTime, 'averageTime')
  res.data.averageScore = Math.round(res.data.averageScore * 10) / 10
  analyseData.value = res.data
}

//时间格式化
function secondsFormat(sec: any, type: string) {
  if (type === 'totalTime') {
    totalTime.value = {
      hours: Number(Math.floor(sec / 3600)),
      minutes: Number(Math.floor((sec % 3600) / 60)),
      seconds: sec % 60
    }
  } else if (type === 'averageTime') {
    averageTime.value = {
      hours: Number(Math.floor(sec / 3600)),
      minutes: Number(Math.floor((sec % 3600) / 60)),
      seconds: sec % 60
    }
  }

}


//重置日期选择
const initDateArray = () => {
  // showFlag.value = queryParams.value.keyword === ''
  queryParams.value.timeArray = []
}

// const initCurrentSelected = () => {
//   currentSelected.value = ''
// }
const goTo = (id: number) => {
  cache.session.setJSON('memberDetailDataQuery', queryParams.value)
  router.push({
    name: 'Report-detail', query: {
      id: id
    }
  });
}

const _getScriptDetail = async (id: string) => {
  try {
    const res = await getScriptDetail({ id });
    scriptType.value = res.data.type;
  } catch (e) {
    console.log(e)
  }

}

onBeforeRouteLeave((to, from) => {
  // const query = cache.session.getJSON('pointQuery')
  // if (query) {
  //   cache.session.remove('pointQuery')
  // }
  console.log(to.path)

  if (to.path === '/dataBoard/report-detail') {
    cache.session.setJSON('memberDetailDataQuery', queryParams.value)
  } else {
    const query = cache.session.getJSON('memberDetailDataQuery')
    if (query) {
      cache.session.remove('memberDetailDataQuery')
    }
  }

})



onMounted(() => {
  handleDate()
  // getList()
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  align-items: center
}

.num {
  font-family: 'inter';
}

.box-wrap {
  height: 100%;
  overflow-y: auto;
}

.user-info {
  .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
    margin-left: 12px;
  }
}

.box-title {
  justify-content: space-between;
  width: 100%;
  margin-bottom: 18px;
}


.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-start;
  margin: 0 auto;
  padding: 30px 0 30px 0;
  width: 100%;




  //   .select-date {}

  .box-content {
    width: 100%;
    justify-content: space-between;
    gap: 0 24px;

    .box-item {
      background-color: rgb(244, 246, 255);
      flex: 1;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;

      .item-title {
        font-weight: bold;
        font-size: 14px;
      }

      .unit {
        font-size: 14px;
        font-weight: normal;
      }

      .item-count {
        width: 100%;
        text-align: center;
        font-size: 28px;
        margin: 24px 0;
        font-weight: bold;
        letter-spacing: 2px;

      }
    }
  }
}


:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 80px - 100px);
    overflow: scroll;
  }
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}

.visit {
  &-object {
    display: flex;
    align-items: center;
    gap: 10px;
    line-height: 1;
  }

  &-title {
    color: var(--el-text-color-disabled);
    font-size: 12px;
  }
}

.el-form-item--default {
  margin-bottom: 0px;
}
</style>
