<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">学员数据</span>
          <div class="search-container">
            <el-form style="width:100%" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
              <el-form-item prop="keyword">
                <el-input
                  v-model="queryParams.keyword"
                  @clear="handleQuery"
                  placeholder="搜索姓名"
                  clearable
                  style="width: 180px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item prop="scriptTypes">
                <el-select
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="1"
                  style="width: 180px;"
                  multiple
                  @change="handleQuery"
                  v-model="queryParams.scriptTypes"
                  placeholder="请选择脚本类型"
                >
                  <el-option v-for="item in scriptTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item prop="deptIds">
                <el-tree-select
                  popper-class="dept-popper"
                  v-model="queryParams.deptIds"
                  @change="handleQuery"
                  :data="deptOptions"
                  node-key="id"
                  collapse-tags
                  :default-expanded-keys="defaultExpandedKeys"
                  check-strictly
                  collapse-tags-tooltip
                  :max-collapse-tags="1"
                  multiple
                  show-checkbox
                  style="width: 250px"
                  :props="{ value: 'id', label: 'label', children: 'children', disabled: 'selected' }"
                  placeholder="请选择部门"
                  :default-expand-all="false"
                />
              </el-form-item>

              <el-form-item prop="time">
                <el-date-picker
                  :disabled-date="disabledDate"
                  v-model="queryParams.timeArray"
                  type="daterange"
                  range-separator="-"
                  :shortcuts="shortcuts"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="margin-right: 12px;width: 220px;"
                />
                <!-- <el-radio-group v-model="currentSelected" @change="initDateArray()">
                  <el-radio-button v-for="item in timeArray" :key="item.text" :label="item.text" :value="item.text" />
                </el-radio-group> -->
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="box-container">
        <div class="box-title flex">
          <span class="bold"> 整体数据 </span>

          <el-popover placement="bottom" :width="300" :visible="popoverVisible">
            <template #reference>
              <span>
                <el-tooltip content="导出" placement="top">
                  <img class="export" v-click-outside="outsideClick" @click="popoverVisible=!popoverVisible" src="@/assets/icons/png/export.png" />
                </el-tooltip>
              </span>
            </template>

            <div class="title" style="margin-bottom: 12px; color: #979797;">导出数据</div>
            <div class="btn-container flex">
              <div class="btn" @click="exportFile(1,false)">
                <div class="top">导出整体数据</div>
                <div class="desc">各部门的数据汇总计算后导出</div>
              </div>
              <div class="btn" @click="exportFile(1,true)">
                <div class="top">导出各部门的整体数据</div>
                <div class="desc">各部门的数据单独计算后导出</div>
              </div>
            </div>
          </el-popover>
        </div>
        <div class="box-content flex" v-if="showFlag">
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/5.png" class="icon" />练习人数</div>
            <div class="item-count">
              <span class="num">{{ analyseData.uv || 0 }}</span>
              <span class="unit">人</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/2.png" class="icon" />每日人均得分</div>
            <div class="item-count">
              <span class="num">{{ analyseData.averageScore || 0 }}</span
              ><span class="unit">分</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/3.png" class="icon" />每日人均练习时长</div>
            <div class="item-count">
              <span class="num">{{ durationTime.hours > 0 ? durationTime.hours : '' }}</span>
              <span class="unit" v-if="durationTime.hours > 0">小时</span>
              <span class="num">{{ durationTime.minutes > 0 ? durationTime.minutes : '' }}</span>
              <span class="unit" v-if="durationTime.minutes > 0">分</span>
              <span class="num">{{ durationTime.seconds }}</span>
              <span class="unit">秒</span>
            </div>
          </div>
          <div class="box-item">
            <div class="item-title"><img src="@/assets/icons/png/4.png" class="icon" />每日人均练习次数</div>
            <div class="item-count">
              <span class="num">{{ analyseData.averageCount }}</span
              ><span class="unit">次</span>
            </div>
          </div>
        </div>
      </div>
      <div class="box-container">
        <div class="box-title flex">
          <span class="bold"> 学员数据 </span>
          <el-tooltip content="导出" placement="top">
            <img class="export" src="@/assets/icons/png/export.png" @click="exportFile(2)" />
          </el-tooltip>
        </div>
        <el-table
          v-loading="loading"
          style="width: 100%;"
          :data="memberList"
          @sort-change="sortChange"
          :default-sort="{ prop: 'totalTime', order: 'descending' }"
        >
          <el-table-column label="序号" align="center" type="index" width="80" />
          <el-table-column label="姓名" align="center" prop="name" width="150" />
          <el-table-column label="部门" align="center" prop="deptName" min-width="300" />

          <!-- <el-table-column label="职务" align="center" prop="type" />
        <el-table-column label="角色" align="center" prop="type" /> -->
          <el-table-column label="总时长" align="center" prop="totalTime" width="150" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ scope.row.totalTime || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="总次数" align="center" prop="totalCount" width="100" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ scope.row.totalCount || '0' }}
            </template>
          </el-table-column>
          <el-table-column label="最高分" align="center" prop="maxScore" width="100" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ scope.row.maxScore || '0' }}
            </template>
          </el-table-column>
          <el-table-column label="平均分" align="center" prop="avgScore" width="100" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ scope.row.avgScore || '0' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="80">
            <template #default="scope">
              <el-button link type="primary" @click="goToMemberDetail(scope.row.id)" text>详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="user" lang="ts">
import dayjs from 'dayjs'
import { ClickOutside as vClickOutside } from "element-plus"
import { onBeforeRouteUpdate, onBeforeRouteLeave } from "vue-router";
import { TreeLong } from "@/api/department/types";
import { getUserChatStatics, getUserDataAnalyse } from "@/api/dataBoard";
import { UserChatResult, DataAnalyseVo } from "@/api/dataBoard/types";
import { useUserStore } from '@/store/modules/user';
import { download } from "@/utils/request";
import cache from '@/plugins/cache';
const popoverVisible = ref(false)
// import { orderBy } from 'element-plus/es/components/table/src/util';
// import { fa } from 'element-plus/es/locale';
// import { deepClone,treeToFlat } from "@/utils/index";
const userStore = useUserStore();
const router = useRouter();
const defaultExpandedKeys = ref<string[]>([])
// let pageLoading: any = null
// import { number } from 'vue-types';
const scriptTypeOptions=ref([
  {
    value: 1,
    label: '技巧练习数据'
  },
  {
    value:2,
    label: '答题练习数据'
  },
  {
    value: 3,
    label: '幻灯片练习数据'
  }
])
const outsideClick = () => {
  console.log(123)

  popoverVisible.value = false
}

// const timeArray = ['昨天', '近3天', '近7天', '近1个月']
// const timeArray = [{
//   text: '今天',
//   num: '0',
//   unit: 'day'
// }, {
//   text: '昨天',
//   num: '0',
//   unit: 'day'
// },
// {
//   text: '近3天',
//   num: '2',
//   unit: 'day'
// },
// {
//   text: '近7天',
//   num: '6',
//   unit: 'day'
// }, {
//   text: '近30天',
//   num: '29',
//   unit: 'day'
// }]
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const currentDate = dayjs();
      return [currentDate, currentDate]
    },
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day');
      return [yesterday, yesterday]
    },
  },
  {
    text: '近3天',
    value: () => {
      const startDate = dayjs().subtract(2, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近7天',
    value: () => {
      const startDate = dayjs().subtract(6, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近30天',
    value: () => {
      const startDate = dayjs().subtract(30, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
]

const memberList = ref<UserChatResult[]>([]);
const deptOptions = ref<TreeLong[]>([])

const loading = ref(false);
const total = ref(0);
// 'asc' : 'desc'

const orderMap = new Map([
  ['maxScore', 'max_score'],
  ['avgScore', 'avg_score'],
  ['totalTime', 'total_time'],
  ['totalCount', 'total_count'],
])


const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: '',
    startTime: '',
    endTime: '',
    deptIds: [],
    isAsc: 'desc',
    orderByColumn: 'total_time',
    timeArray: [] as string[],
    scriptTypes: [] as number[]
  },
});
// const currentSelected = ref('今天')
const { queryParams } = toRefs(data);
const analyseData = ref<DataAnalyseVo>({
  uv: 0,
  averageScore: 0,
  averageTime: 0,
  averageCount: 0,
  averageMinTime: 0,
})

const durationTime = ref({
  hours: 0,
  minutes: 0,
  seconds: 0,
})

const disabledDate = (val: any) => {
  return val > new Date();
};
const showFlag = ref(true)
//监听日期选择
watch(
  () => queryParams.value.timeArray,
  (val) => {
    queryParams.value.startTime = val && val.length > 0 ? val[0] : '';
    queryParams.value.endTime = val && val.length > 0 ? val[1] : '';
    getList()
  }
);

const goToMemberDetail = (id: number) => {
  cache.session.setJSON('memberDataQuery', queryParams.value)
  router.push({
    name: 'Member-detail', query: {
      userId: id,
      startTime: queryParams.value.startTime,
      endTime: queryParams.value.endTime,
      scriptTypes: JSON.stringify(queryParams.value.scriptTypes)
    }
  });
}


const sortChange = (column: any) => {
  console.log(column)
  queryParams.value.isAsc = column.order ? column.order === 'ascending' ? 'asc' : 'desc' : 'desc'
  queryParams.value.orderByColumn = orderMap.get(column.prop) || ''
  handleQuery()
}



const handleQuery = () => {
  queryParams.value.pageNum = 1;
  showFlag.value = queryParams.value.keyword === ''
  getList();
}
const handleDate = () => {
  if (cache.session.getJSON('memberDataQuery')) {
    queryParams.value = cache.session.getJSON('memberDataQuery')
    cache.session.remove('memberDataQuery')
  } else {
    const nowDate = dayjs().format('YYYY-MM-DD');
    queryParams.value.timeArray = [nowDate, nowDate]
  }

}

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await getUserChatStatics(queryParams.value);
  res.rows.forEach(item => {
    if (item.totalTime) {
      const hours = Math.floor(Number(item.totalTime) / 3600)
      const min = Math.floor((Number(item.totalTime) % 3600) / 60)
      const seconds = Number(item.totalTime) % 60; // 计算剩余秒数
      durationTime
      item.totalTime = hours > 0 ? hours + '小时' + min + '分' + seconds + '秒' : min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
    } else {
      item.totalTime = '0秒'
    }

  })
  memberList.value = res.rows;
  total.value = res.total;
  loading.value = false;
  _getUserDataAnalyse()
}

const _getUserDataAnalyse = async () => {
  try {
    const res = await getUserDataAnalyse({ scriptTypes: queryParams.value.scriptTypes, startTime: queryParams.value.startTime + ' 00:00:00', endTime: queryParams.value.endTime + ' 23:59:59', deptIds: queryParams.value.deptIds })
    secondsFormat(res.data.averageTime)
    res.data.averageCount = Math.round(res.data.averageCount * 10) / 10
    analyseData.value = res.data
    // pageLoading.close()
  } catch (error) {
    console.log(error)
    // pageLoading.close()
  }
}


function secondsFormat(sec: any) {
  durationTime.value = {
    hours: Number(Math.floor(sec / 3600)),
    minutes: Number(Math.floor((sec % 3600) / 60)),
    seconds: sec % 60
  }
}

const exportFile = (type: number,groupByDept?:boolean) => {
  const param = { ...queryParams.value }
  if (type === 1) {
    download('/biz/kanban/aggregateDataExport', { startTime: param.startTime + ' 00:00:00', endTime: param.endTime + ' 23:59:59', deptIds: param.deptIds,groupByDept:groupByDept, scriptTypes:param.scriptTypes}, `${param.startTime}至${param.endTime}学员数据的整体数据.xlsx`)
    popoverVisible.value = false
  } else if (type === 2) {
    download('/biz/kanban/userExport', {
      startTime: param.startTime + ' 00:00:00', endTime: param.endTime + ' 23:59:59', deptIds: param.deptIds, keyword: param.keyword, orderByColumn: queryParams.value.orderByColumn,
      isAsc: queryParams.value.isAsc,
      scriptTypes:param.scriptTypes
    }, `${param.startTime}至${param.endTime}学员数据.xlsx`)
  }
}

//重置日期选择
// const initDateArray = () => {
//   showFlag.value = queryParams.value.keyword === ''
//   queryParams.value.timeArray = []
// }
// 树形数据扁平化


const _getDeptTreeList = async () => {
  // pageLoading = ElLoading.service({
  //   lock: true,
  //   text: '正在加载',
  // })
  const res = await userStore.getDeptTreeSelectedListFn()
  if (res) {
    deptOptions.value = res
    defaultExpandedKeys.value = res.length > 0 ? [res[0].id] : []
    // const array1 = treeToFlat(deepClone(res))
    // const filterArray = array1.filter((item: any) => item.selected === false)
    // const deptIds = filterArray.length > 0 ? [filterArray[0].id] : []
    // queryParams.value.deptIds = deptIds
  }

}
// import { initWebSocket } from '@/utils/websocket';


// onMounted(() => {
//   nextTick(() => {
//     let protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
//     console.log(123)
//     initWebSocket(protocol + window.location.host + import.meta.env.VITE_APP_BASE_API + '/resource/websocket');
//   })
// })
onBeforeRouteUpdate(() => {

  queryParams.value.deptIds = []
  _getDeptTreeList()
})
onBeforeRouteLeave((to, from) => {
  // const query = cache.session.getJSON('pointQuery')
  // if (query) {
  //   cache.session.remove('pointQuery')
  // }
  if (to.path === '/dataBoard/member-detail') {
    cache.session.setJSON('memberDataQuery', queryParams.value)
  } else {
    const query = cache.session.getJSON('memberDataQuery')
    if (query) {
      cache.session.remove('memberDataQuery')
    }
  }

})

onMounted(() => {
  handleDate()
  if (!router.currentRoute.value.query.dynamic) {
    _getDeptTreeList()
  }
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.card-title {
  font-size: 24px;
  font-weight: bold;
}



.flex {
  display: flex;
  align-items: center
}
.btn-container {
  flex-direction: column;
  align-items: flex-start !important;
  gap:18px 0;
}
.btn{
  cursor: pointer;
  color: #333333;
  .top{
    font-weight: bold;
    font-size: 16px;
  }
  .desc{
    font-size: 14px;
    color: #999999 !important;
    margin-top: 4px;
  }
  &:hover{
    color: var(--el-color-primary);
  }
}
.num {
  font-family: 'inter';
}

.box-wrap {
  height: 100%;
  overflow-y: auto;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  padding: 0 0 0;
  width: 100%;
  margin-bottom: 16px;

  .box-title {
    justify-content: space-between;
    width: 100%;

    .export {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  .select-date {}

  .box-content {
    width: 100%;
    justify-content: space-between;
    gap: 0 24px;

    .box-item {
      background-color: #f4f6ff;
      flex: 1;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;

      .item-title {
        font-weight: bold;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 0 8px;
      }

      .icon {
        width: 24px;
        height: 24px;
      }

      .unit {
        font-size: 14px;
        font-weight: normal;
      }

      .item-count {
        width: 100%;
        text-align: center;
        font-size: 28px;
        margin: 24px 0;
        font-weight: bold;
        letter-spacing: 2px;

      }
    }
  }
}


:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 70px - 100px);
    overflow-y: auto;
  }
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}

.bold {
  font-weight: bold;
}
</style>
