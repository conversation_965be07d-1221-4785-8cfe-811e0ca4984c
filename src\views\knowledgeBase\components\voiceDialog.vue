<template>
  <div>
    <el-dialog
      class="dialog-container"
      fullscreen
      ref="formDialogRef"
      v-model="visibleFlag"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
      id="drop-area"
    >
      <div class="fullscreen-container" v-show="uploadFlag">
        <div class="upload-file-container"></div>
        <el-upload
          multiple
          :action="uploadFileUrl"
          :before-upload="handleBeforeUpload"
          v-model:file-list="fileList"
          drag
          name="file"
          :on-error="handleUploadError"
          :on-exceed="handleExceed"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
          :limit="limit"
          :on-remove="handleRemove"
          :headers="headers"
          class="upload-file-uploader"
          ref="fileUploadRef"
        >
          <div class="text-container">
            <div class="full-title">将音频拖动到此即可上传</div>
            <div class="full-tips">
              支持wav、pcm、ogg-opus、speex、silk、mp3、m4a、aac、amr的文件，单个文件大小不超过100MB，时长不超过10分钟，单次只允许上传一个文件。
            </div>
          </div>
        </el-upload>
      </div>

      <el-card>
        <template #header>
          <div class="card-title">
            {{ title }}
            <div style="font-size: 14px;color: #999999;margin-top: 12px;">如果您有针对该图片的音频讲解内容，可以上传在此进行标注</div>
          </div>
        </template>
        <div class="box-container">
          <div class="upload-inner" :style="{ zIndex: uploadFlag ? 4 : 2 }">
            <el-form ref="formParamsRef" label-width="100px" label-position="left">
              <div class="merge-method" @click.stop>
                <el-form-item prop="mergeMethod" label-width="150px" label-position="left">
                  <template #label>
                    <span class="label-text">音频文件</span>
                  </template>
                  <div class="upload-file">
                    <!-- 上传按钮 -->
                    <!-- <el-button type="primary">选取文件</el-button> -->
                    <el-upload
                      multiple
                      :action="uploadFileUrl"
                      :before-upload="handleBeforeUpload"
                      v-model:file-list="fileList"
                      name="file"
                      :on-error="handleUploadError"
                      :on-exceed="handleExceed"
                      :on-success="handleUploadSuccess"
                      :show-file-list="false"
                      :limit="limit"
                      :on-remove="handleRemove"
                      :headers="headers"
                      ref="fileUploadRef"
                      v-if="fileList.length === 0"
                    >
                      <div class="upload-container" v-if="fileList.length === 0">
                        <img src="@/assets/icons/png/upload.png" />
                        <div class="right">
                          <div style="color: #000000;">将文档拖到此处，或点击上传</div>
                          <div class="tips">
                            支持wav、pcm、ogg-opus、speex、silk、mp3、m4a、aac、amr的文件，单个文件大小不超过100MB，时长不超过10分钟，单次只允许上传一个文件。
                          </div>
                        </div>
                      </div>
                    </el-upload>
                    <div class="file-container flex" v-else>
                      <div class="left flex" style="  gap: 0 10px;">
                        <img src="@/assets/icons/svg/voice2.svg" />
                        {{ fileList[0]?.fileName }}
                      </div>
                      <div class="right flex" style="gap: 0 8px;">
                        <el-tooltip content="下载音频文件" placement="top">
                          <img
                            @click="downloadVioce(fileList[0].url)"
                            class="export"
                            style="width: 20px;cursor: pointer;"
                            src="@/assets/icons/png/export.png"
                          />
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                          <el-icon @click="removeVoice" style="cursor: pointer;" size="20">
                            <Close />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </div>

              <div class="merge-method" @click.stop>
                <el-form-item prop="mergeMethod" label-width="150px" label-position="left">
                  <template #label>
                    <span class="label-text flex">音频内容合并方式</span>
                    <el-tooltip content="音频转成文字后，与现有标注内容的合并方式。仅本次操作生效。" placement="top" popper-class="popper">
                      <el-icon class="label-tip" style="margin-left: 4px;">
                        <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;" />
                      </el-icon>
                    </el-tooltip>
                  </template>
                  <el-radio-group v-model="mergeMethod" style="display: flex;">
                    <el-radio :value="1" :label="1">
                      <span>添加在末尾</span>
                    </el-radio>
                    <el-radio :value="2" :label="2">
                      <span>替换原内容</span>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" :loading="importLoading" @click="handleSubmit" :disabled="fileList.length === 0">确 定</el-button>
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { globalHeaders } from "@/utils/request";
import { propTypes } from '@/utils/propTypes';
import { uploadVoice } from "@/api/knowledgeBase";
const currentUploadRequest = ref(null)
import { ElMessageBox } from 'element-plus'
import { start } from "nprogress";
const emits = defineEmits(['update:visible', 'success'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const fileList = ref<any[]>([])
const number = ref(0);
// const uploadList = ref<any[]>([]);
const importLoading = ref(false)

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/resource/oss/upload"); // 上传文件服务器地址
const headers = ref(globalHeaders());
const fileUploadRef = ref<ElUploadInstance>();
const currentFile = ref<any>(null)
const mergeMethod = ref(1)
const uploadFlag = ref(false) //ture 全屏
// let uploadLoadingInstance: any = null

// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '上传音频'
  },
  activeId: {
    type: String,
    default: ''
  },
  modelValue: [String, Object, Array],
  // 数量限制
  limit: propTypes.number.def(1),
  // 大小限制(MB)
  fileSize: propTypes.number.def(100),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def(['wav', 'pcm', 'ogg-opus', 'speex', 'silk', 'mp3', 'm4a', 'aac', 'amr']),
  // 是否显示提示
  isShowTip: propTypes.bool.def(true),

})





// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const downloadVioce = (url: string) => {
  window.open(url)
}

const removeVoice = () => {
  fileList.value = []
  currentFile.value = null
  uploadFlag.value = false

}

// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
  uploadFlag.value = false
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    console.log(isTypeOk)
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
      fileUploadRef.value?.handleRemove(file);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }

  proxy?.$modal.loading("正在上传文件，请稍候...");
  currentUploadRequest.value = file.request
  currentFile.value = file
  number.value++;
  return true;
}

// 文件个数超出
const handleExceed = () => {
  proxy?.$modal.msgError(`上传文件数量不能超过${props.limit}个!`);
}




// 上传失败
const handleUploadError = (file: any) => {
  if (number.value > 0) {
    number.value--;
  }
  fileUploadRef.value?.handleRemove(file);
  fileList.value = []
  // emit("update:modelValue", fileList.value);
  proxy?.$modal.msgError("上传文件失败");
}
const handleRemove = () => {
  if (number.value > 0) {
    number.value--;
  }
}
// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  // uploadLoadingInstance?.close()
  // console.log(res)
  importLoading.value = false
  proxy?.$modal.closeLoading();
  if (res.code === 200) {
    uploadedSuccessfully(res.data);
  } else {
    number.value--;
    proxy?.$modal.closeLoading();
    proxy?.$modal.msgError(res.msg);
    fileUploadRef.value?.handleRemove(file);
    currentFile.value = null
    fileList.value = []
  }
}


// 上传结束处理
const uploadedSuccessfully = (data: any) => {
  if (number.value > 0) {
    number.value = 0;
    data.currentFile = currentFile.value
    fileList.value = [data]
    currentFile.value = null
  }
}



const closeDialog = () => {
  if (fileList.value.length > 0) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要取消吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        fileList.value = []
        currentFile.value = null
        visibleFlag.value = false
      })
      .catch(() => {
      })
  } else {
    visibleFlag.value = false
  }

}

const handleSubmit = async () => {
  importLoading.value = true
  await uploadVoice({ mergeMethod: mergeMethod.value, ossId: fileList.value[0].ossId, kbItemId: props.activeId }).then((res) => {
    proxy.$modal.closeLoading();
    proxy.$modal.msgSuccess('保存成功');
    visibleFlag.value = false
    fileList.value = []
    uploadFlag.value = false
    emits('success')
  }).finally(() => {
    importLoading.value = false;
  });


  // const array = fileIdList.value.map(item => item.id)
  // emits('sendFileIdList', array)
  // fileIdList.value=[]
}
onMounted(() => {
  // console.log(draggingCounter.value)
  var last = null;
  let elem = document.getElementById('drop-area')
  document.addEventListener('dragenter', function (e) {
    last = e.target; // 记录最后进入的元素
    // console.log('dragenter', e.target);
    // elem.classList.add('content');
    uploadFlag.value = true
  });
  document.addEventListener('dragleave', function (e) {
    // 如果此时退的元素是最后进入的元素，说明是真正退出了`drop-area`元素
    if (last === e.target) {
      // console.log('dragleave', e.target);
      // elem.classList.remove('content');
      uploadFlag.value = false

      e.stopPropagation();
      e.preventDefault();//事件冒泡和浏览器默认事件还是要阻止的不然不触发dropEvent事件
    }
  });

})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 20px;
  // font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 250px;
}

.fullscreen-container {
  width: 100%;
  height: 100%;
  position: fixed !important;
  z-index: 9999;
  left: 0;
  top: 0;

  :deep(.el-upload) {
    width: 100%;
    height: 100%;
  }
}

// 背景
.upload-file-container {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  filter: blur(5px);
  backdrop-filter: blur(5px);
  position: absolute !important;
}

.upload-file-uploader {
  margin-bottom: 5px;

  position: absolute;
  z-index: 10000;
  width: 100%;
  height: 100%;

}

.text-container {
  width: 95%;
  margin: 0 auto;
  border-radius: 40px;
  height: 100%;
  border: 1px dashed #c1ced6;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .full-tips,
  .full-title {
    color: #ffffff;
  }

  .full-title {
    font-size: 24px;
    font-weight: bold;
  }

  .full-tips {
    width: 500px;
    text-align: center;
    margin-top: 24px;
    font-size: 20px;
  }
}


.flex {
  display: flex;
  align-items: center;
}

.upload-file {
  width: 100%;
}

.upload-inner {
  position: absolute;
  padding: 16px;
  width: 100%;
  // z-index: 4;
}

.merge-method {}

.rotating-image {
  animation: rotate 2s linear infinite;
}



.upload-container {
  margin: 0 auto;
  display: flex;
  align-items: flex-start;
  gap: 0 16px;
  width: 100%;
  border-radius: 4px;
  border: 1px dashed #c1ced6;
  box-sizing: border-box;
  padding: 16px;

  img {
    width: 48px;
    height: 48px;
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;

    .tips {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
      color: #999999;
      line-height: 28px;
    }
  }
}

.file-container {
  padding: 12px;
  width: 100%;
  background-color: var(--el-color-primary-light-9);
  box-sizing: border-box;

  .left {
    width: 90%;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}

:deep(.is-dragover) {
  // border: 1px dashed #4f66ff;
  // background-color: rgba($color: #a0abed, $alpha: 0.1) !important;
}

:deep(.el-upload) {
  width: 100%;
  // height: 250px;
}

:deep(.el-upload-dragger) {
  height: 100%;
  border: none;
  background: none;
}

.dialog-container {
  background-color: transparent !important;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
