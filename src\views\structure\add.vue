<template>
  <div>
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container" v-loading="loading">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="手机号" prop="phoneNumber">
                <el-input v-model="formParams.phoneNumber" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="部门" prop="deptId">
                <el-tree-select
                  style="width: 100%;"
                  v-model="formParams.deptId"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  filterable
                  placeholder="请选择部门"
                  check-strictly
                  clearable
                  popper-class="dept-popper"
                  :default-expand-all="true"
                >
                  <template #empty> <span>部门不存在</span></template>
                </el-tree-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="formParams.gender">
                  <el-radio :value="0" :label="0">男</el-radio>
                  <el-radio :value="1" :label="1">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { reactive, ref } from 'vue'
import { saveOrUpdateUser } from '@/api/user'
import { getDeptTreeList } from "@/api/department/index"
import { TreeLong } from "@/api/department/types";
import { ElMessageBox } from 'element-plus'
import { departmentTreeUserDisable } from '@/utils';
import { useUserStore } from '@/store/modules/user';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success', 'freezeUser'])
const formParamsRef = ref()
const btnLoading = ref(false)
const loading = ref(false)
const deptOptions = ref<TreeLong[]>([])
const userStore = useUserStore();
const initForm = {
  gender: null,
  name: '',
  phoneNumber: '',
  deptId: ''
}
const formParams = ref(initForm)
import type { FormRules } from 'element-plus'
interface RuleForm {
  name: string
  phoneNumber: string,
  deptId: string
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
  ],
  deptId: [
    { required: true, message: '请选择部门', trigger: 'change' },
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern:
        /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
      message: '请填写正确的手机号',
      trigger: 'blur'
    },
  ],
})

watch(() => props.visible, val => {
  if (val) {
    _getDeptTreeList()
  }
})
const _getDeptTreeList = async () => {
  loading.value = true
  const res = await getDeptTreeList()
  if (res.data) {

    if(userStore.cpFlag) {
      deptOptions.value = departmentTreeUserDisable(res.data)
    } else {
      deptOptions.value = res.data
      formParams.value.deptId = res.data[0].id
    }

  }
  loading.value = false
}

// props
const props = defineProps({

  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增'
  },

})




// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})




const back = () => {
  let bool = false
  Object.keys(formParams.value).forEach((key) => {
    if (key !== 'deptId') {
      if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
        JSON.stringify(formParams.value[key]) !== 'null' &&
        JSON.stringify(formParams.value[key]) !== '[]'
      ) {
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}

const closeDialog = () => {
  formParamsRef.value.resetFields()
  formParams.value = initForm
  visibleAdd.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
      };
      await saveOrUpdateUser(params).then((res) => {
        if (res.data) {
          ElMessageBox.confirm(
            '该成员已被冻结，是否要前往解冻',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(async () => {
              closeDialog()
              emits('freezeUser', res.data)
            })
            .catch(() => {
            })
        } else {
          proxy.$modal.msgSuccess('保存成功');
          closeDialog()
          emits('success')
        }

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}
</style>
