<template>
  <el-dialog v-model="visibleDialog" width="750px" append-to-body>
    <template #title>
      <div>AI陪练页面设置</div>
      <div class="sub-title">设置AI陪练页面的展现形式</div>
    </template>
    <div class="dialog-content">
      <div class="select-box">
        <div class="select-item">
          <div class="select-item-name">产品列表</div>
          <div class="select-item-preview" :class="{ active: type === 'PRODUCT'}" @click="changeType('PRODUCT')">
            <img class="select-item-img" src="@/assets/images/weapp_index_prictice.jpg" alt="产品列表" />
          </div>
        </div>

        <div class="select-item">
          <div class="select-item-name">脚本列表</div>
          <div class="select-item-preview" :class="{active: type === 'SCRIPT'}" @click="changeType('SCRIPT')">
            <img class="select-item-img" src="@/assets/images/weapp_script.jpg" alt="脚本列表" />
            <div class="select-item-default">默认</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  value: {
    type: String,
    default: ''
  }
})
const emits = defineEmits(['update:visible', 'onSuccess'])

const type = ref()

watch(() => props.visible, (val) => {
  if(val) {
    type.value = props.value
  }
})

// 弹窗组件显示隐藏
const visibleDialog = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const changeType = (val:string) => {
  console.log('chageType', val)
  type.value = val
}

const closeDialog = () => {
  visibleDialog.value = false
}
const handleSubmit = () => {
  closeDialog()
  emits('onSuccess', 'aiPractisePage', type)
}
</script>

<style scoped lang="scss">
@import "./style";
</style>
