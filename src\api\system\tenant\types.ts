export interface TenantVO extends BaseEntity {
  id: number | string;
  tenantId: number | string;
  cpFlag: boolean;
  username: string;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
  licenseNumber: string;
  address: string;
  domain: string;
  intro: string;
  enterpriseFlag: boolean;
  remark: string;
  packageId: string | number;
  expireTime: string;
  accountCount: number;
  status: string;
}

export interface TenantQuery extends PageQuery {
  tenantId: string | number;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
}

export interface TenantForm {
  id: number | string | undefined;
  tenantId: number | string | undefined;
  username: string;
  password: string;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
  licenseNumber: string;
  domain: string;
  address: string;
  intro: string;
  remark: string;
  packageId: string | number;
  expireTime: string;
  accountCount: number;
  appAccountCount: number | null;
  status: string;
  emanNum: number | null;
  whiteAccountCount: number | null;
}

export interface WeappSettingParam {
  indexPage: string;
  aiPractisePage: string;
  chatPage: string;
}
