<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />关卡{{ currentPageData.orderNum }}详情</span
          >
        </div>
      </template>
      <div class="box-container">
        <div class="flex">
          <div style="width: 70px;">关卡{{ currentPageData.orderNum }}</div>
          <div style="width: 600px;">{{ currentPageData.conditionText }}</div>
          <div style="width: 200px;">
            已通关<span style="margin-left: 12px;">{{ currentPageData.completionCount }}人</span>
          </div>
          <div style="flex: 1;display: flex;justify-content: flex-end;">
            <el-tooltip content="导出" placement="top">
              <img class="export" src="@/assets/icons/png/export.png" @click="handleExport" />
            </el-tooltip>
          </div>
        </div>
        <el-table v-loading="loading" :data="dataList" @row-click="gotoLevelUser">
          <el-table-column label="序号" align="center" type="index" width="100" />

          <el-table-column label="姓名" align="left" prop="name" width="200" />
          <el-table-column label="部门" align="left" prop="deptName" min-width="200" />
          <el-table-column label="挑战次数" align="left" prop="num" width="200" />
          <el-table-column label="状态" align="left" prop="status" width="200">
            <template #default="scope">
              <span v-if="scope.row.status === 1">已通关</span>
              <span v-else-if="scope.row.status === 0">待通关</span>
            </template>
          </el-table-column>
          <el-table-column label="通关时间" align="left" prop="unlockTime" width="200">
            <template #default="scope">
              {{ scope.row.unlockTime? dayjs(scope.row.unlockTime).format("YYYY年MM月DD日 HH:mm:ss"):'-'}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="100">
            <template #default="scope">
              <el-button link type="primary" text @click="gotoLevelUser(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { eventLevelStageDetail,eventLevelStageDetailUser } from "@/api/eventLevel";
import { EventLevelStageUserVo,EventLevelStageDetailVo } from '@/api/eventLevel/types';
import { download } from "@/utils/request";
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const route = useRoute();
const router = useRouter();
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
});
const currentPageData = ref<EventLevelStageDetailVo>({} as EventLevelStageDetailVo)

const loading = ref(false);
const total = ref(0);
const { queryParams } = toRefs(data);

const dataList = ref<EventLevelStageUserVo[]>([])


const gotoLevelUser = (row: any) => {
  router.push({
    name: 'Level-user', query: {
      userId: row.id,
      id: route.query.id,
      userName: row.name,
    }
  });
}

const _getDetail = async () => {
    const res = await eventLevelStageDetail(route.query.id as string);
    console.log(res.data)
    let text=''
    res.data.unlockConditions.forEach((el: any) => {
       text+= el.scriptName += (`,获得${el.time}次${el.score}分及以上`)
    });
    res.data.conditionText=text
    currentPageData.value = res.data
    console.log(currentPageData.value)
    proxy?.$modal.closeLoading();
}

const getList = async () => {
  const res = await eventLevelStageDetailUser({...queryParams.value,id:route.query.id as string})
  dataList.value = res.rows;
  total.value = res.total;
  loading.value = false;
  proxy?.$modal.closeLoading();
}

const handleExport = () => {
  download(`/biz/eventLevelStage/detail/${route.query.id}/user-export`, {}, `关卡${currentPageData.value.orderNum}学员通关数据${dayjs().format('YYYY年MM月DD日')}.xlsx`)
}
onUnmounted(() => {
    // cache.session.remove('pointQuery')
})
onMounted(() => {
    proxy?.$modal.loading("正在加载");
    _getDetail()
    getList()
})
</script>
<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 0 12px;
    font-size: 24px;
    font-weight: bold;
}

.flex {
    width: 100%;
    padding:  0 0 0 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 18px 0;
    font-size: 14px;
}

.export {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;
    align-items: flex-end;
    margin: 0 auto;
    // padding: 0 30px 30px 30px;
    width: 100%;

}

.box-title {
    justify-content: space-between;
    width: 100%;
    margin-bottom: 18px;
}
</style>
