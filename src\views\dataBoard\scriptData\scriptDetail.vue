<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />{{
              scriptName }}</span
          >
          <div class="search-container">
            <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
              <div class="box-title flex">
                <el-form-item prop="deptIds">
                  <el-tree-select
                    v-model="queryParams.deptIds"
                    :data="deptOptions"
                    :props="{ value: 'id', label: 'label', children: 'children' ,disabled: 'selected'}"
                    node-key="id"
                    check-strictly
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="2"
                    multiple
                    show-checkbox
                    style="width: 250px"
                    placeholder="请选择部门"
                    popper-class="dept-popper"
                    :default-expand-all="true"
                  />
                </el-form-item>
                <el-form-item prop="time">
                  <el-date-picker
                    :disabled-date="disabledDate"
                    v-model="queryParams.timeArray"
                    type="daterange"
                    :clearable="false"
                    range-separator="-"
                    :shortcuts="shortcuts"
                    value-format="YYYY-MM-DD"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="margin-right: 12px;width: 280px;"
                  />
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </template>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="脚本概况" name="脚本概况">
          <ScriptTab :dept-ids="queryParams.deptIds" :type="type" :time-array="queryParams.timeArray" :script-id="scriptId"> </ScriptTab>
        </el-tab-pane>
        <el-tab-pane label="学员详情" name="学员详情">
          <MemberTab :dept-ids="queryParams.deptIds" :time-array="queryParams.timeArray" :script-id="scriptId"></MemberTab>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup name="scriptData" lang="ts">
// import type { TabsPaneContext } from 'element-plus'
import ScriptTab from "./components/scriptTab.vue";
import MemberTab from "./components/memberTab.vue";
import { TreeLong } from "@/api/department/types";
import { useUserStore } from '@/store/modules/user';
import { deepClone,treeToFlat } from "@/utils/index";

import dayjs from 'dayjs'
import cache from "@/plugins/cache";
const route = useRoute();
const router = useRouter();
const activeName = ref('脚本概况')
const userStore = useUserStore();
const scriptName = ref('')
const scriptId = ref('')
const type=ref(0)
const deptOptions = ref<TreeLong[]>([])
const data = reactive({
  queryParams: {
    deptIds: [] as string[],
    timeArray: [] as string[]
  },
});
const { queryParams } = toRefs(data);
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const currentDate = dayjs();
      return [currentDate, currentDate]
    },
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = dayjs().subtract(1, 'day');
      return [yesterday, yesterday]
    },
  },
  {
    text: '近3天',
    value: () => {
      const startDate = dayjs().subtract(2, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近7天',
    value: () => {
      const startDate = dayjs().subtract(6, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
  {
    text: '近30天',
    value: () => {
      const startDate = dayjs().subtract(30, 'day');
      const endDate = dayjs();
      return [startDate, endDate]
    },
  },
]
const disabledDate = (val: any) => {
  return val > new Date();
};
/**初始化 */
const handleDate = () => {
const loading =  ElLoading.service({
    lock: true,
    text: 'Loading',
  })
  scriptName.value = route.query.scriptName as string
  scriptId.value = route.query.scriptId as string
  type.value = route.query.type as unknown as number
  if (cache.session.getJSON('memberScriptDataQuery')) {
   const query = cache.session.getJSON('memberScriptDataQuery')
    // cache.session.remove('memberScriptDataQuery')
    // console.log(query)
    queryParams.value.timeArray =[query.startTime, query.endTime]
    queryParams.value.deptIds = query.deptIds
    activeName.value = '学员详情'
  } else {
    queryParams.value.timeArray = [route.query.startTime as string, route.query.endTime as string];
    queryParams.value.deptIds = route.query.deptIds as string[]
  }
  loading.close()

}
const _getDeptTreeList = async () => {
  const res = await userStore.getDeptTreeSelectedListFn()
  if (res) {
    deptOptions.value = res
    // const array1 = treeToFlat(deepClone(res))
    // const filterArray = array1.filter((item: any) => item.selected === false)
    // const deptIds = filterArray.length > 0 ? [filterArray[0].id] : []
    // queryParams.value.deptIds = deptIds
  }
  handleDate()
}

onMounted(() => {
  _getDeptTreeList()
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.card-title {
  font-size: 24px;
  display: flex;
  align-items: center;
  font-weight: bold;
  gap: 0 12px;
}

.flex {
  display: flex;
  align-items: center
}

:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 84px - 32px - 50px);
    overflow: auto;
  }
}

.bold {
  font-weight: bold;
}
:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}
</style>
