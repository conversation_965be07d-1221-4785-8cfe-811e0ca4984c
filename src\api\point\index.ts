import request from '@/utils/request';
import { PointQuery, PointRecordVo, PointRecordQuery, UserPointVo } from './types';
import { AxiosPromise } from 'axios';

// 查询脚本列表
export function getPointList(query: PointQuery): AxiosPromise<UserPointVo[]> {
  return request({
    url: '/biz/point/userPointList',
    method: 'get',
    params: query
  });
}
// 查询脚本列表
export function getPointRecordList(query: PointRecordQuery): AxiosPromise<PointRecordVo[]> {
  return request({
    url: '/biz/point/recordList',
    method: 'get',
    params: query
  });
}
// 清除积分
export function clearPoints(data: { date: string; clearAll: boolean }): AxiosPromise<any> {
  return request({
    url: '/biz/point/clearPoints',
    method: 'post',
    data
  });
}
