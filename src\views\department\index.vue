<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">部门管理</span>
          <el-button type="primary" icon="Plus" @click="handleAdd()">新增</el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="deptList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        ref="deptTableRef"
        lazy
        :load="load"
        :default-expand-all="isExpandAll"
      >
        <el-table-column prop="deptName" label="部门名称"></el-table-column>
        <el-table-column prop="userNum" label="部门人数" width="200"></el-table-column>

        <!-- <el-table-column prop="orderNum" align="center" label="序号" width="200"></el-table-column> -->
        <!-- <el-table-column prop="status" align="center" label="状态" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column> -->
        <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span v-formatTime="scope.row.createTime"></span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="200">
          <template #default="scope">
            <!-- <el-button link type="primary" @click="" text>新增</el-button> -->
            <el-button link type="primary" @click="handleEdit(scope)" text :disabled="userStore.cpFlag && scope.row.externalId">编辑</el-button>
            <el-button
              link
              type="primary"
              @click="handleDelete(scope.row)"
              :disabled="userStore.cpFlag&& scope.row.externalId"
              text
              v-if="scope.row.parentId !== 0"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <Add :title="comAddTitle" v-model:visible="showAdd" @success="initGetList"> </Add>
    <Detail :id="comAddId" :isEdit="isEdit" :title="comEditTitle" v-model:visible="showEdit" @success="initGetList" ref="detail"> </Detail>
  </div>
</template>

<script setup name="Dept" lang="ts">
import { getDeptList, delDept } from "@/api/department/index"
import { DeptVo } from "@/api/department/types";
const { proxy } = getCurrentInstance() as ComponentInternalInstance
import Add from "./add.vue";
import Detail from "./detail.vue";
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
const deptList = ref<DeptVo[]>([])
const loading = ref(true)
const showAdd = ref(false)
const showEdit = ref(false)
const isEdit = ref(false)
const comAddId = ref() // 事件id
const comAddTitle = ref('新建') // 弹窗组件标题
const comEditTitle = ref('编辑') // 弹窗组件标题
const isExpandAll = ref(false)
const detail = ref();
const deptTableRef = ref<ElTableInstance>();

// const initFormData: DeptForm = {
//   deptId: undefined,
//   parentId: undefined,
//   deptName: undefined,
//   orderNum: 0,
//   leader: undefined,
//   phone: undefined,
//   email: undefined,
//   status: "0"
// }
const data = reactive({
  //   form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  //   rules: {
  //     parentId: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
  //     deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
  //     orderNum: [{ required: true, message: "显示排序不能为空", trigger: "blur" }],
  //     email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
  //     phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
  //   },
})


// /**详情 */
// const handleDetail = (scope: any) => {
//   showEdit.value = true
//   comAddId.value = scope.row.id
//   isEdit.value = false
//   comEditTitle.value = '基本信息'
// }

const load = async (
  row: DeptVo,
  treeNode: unknown,
  resolve: (data: DeptVo[]) => void
) => {
  //获取子部门
  const res = await getDeptList({ parentId: row.id });
  resolve(res.data);
}

/**编辑 */
const handleEdit = (scope: any) => {
  showEdit.value = true
  detail.value.initFormParams(scope.row);
  comAddId.value = scope.row.id
  isEdit.value = true
  comEditTitle.value = '编辑部门'
}


const handleAdd = async () => {
  showAdd.value = true
  comAddTitle.value = '添加部门'
}
const initGetList = async () => {
  deptList.value=[]
  getList()
}
/** 查询菜单列表 */
const getList = async () => {
  loading.value = true;
  const res = await getDeptList({ parentId: '0' });
  const firstData = proxy?.handleTree<DeptVo>(res.data, "id")
  console.log(firstData)
  if (firstData) {
    if (firstData.length > 0) {
      // 展开第一行
      const { data } = await getDeptList({ parentId: firstData[0].id });
      deptTableRef.value!.store.states.lazyTreeNodeMap.value[firstData[0].id] = data;
      deptTableRef.value!.store.states.treeData.value[firstData[0].id] = {
        data,
        display: true,
        expanded: true,
        lazy: true,
        level: 0,
        loaded: true,
        loading: false,
      };
    }
    deptTableRef.value?.toggleRowExpansion(firstData[0].id, true);
    deptList.value = firstData
  }
  loading.value = false
}




/** 删除按钮操作 */
const handleDelete = async (row: DeptVo) => {
  await proxy?.$modal.confirm('是否删除部门' + row.deptName + '？');
  await delDept({ id: row.id });
  await getList();
  proxy?.$modal.msgSuccess("删除成功");
}

onMounted(() => {
  getList();
});
</script>
