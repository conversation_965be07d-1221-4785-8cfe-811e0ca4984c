<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">读音标注</span>
          <el-row :gutter="10">
            <el-col :span="1.5">
              <el-button type="primary" icon="Plus" @click="handleAdd()">新增</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
      <el-table v-loading="loading" :data="polyphoneList" @row-click="handleEdit">
        <!-- <el-table-column label="编号" align="center" type="index" width="150" /> -->
        <!-- <el-table-column type="selection" width="55" align="left" /> -->
        <el-table-column label="词语" align="left" prop="content" />
        <el-table-column label="多音字" align="left" prop="charText" />
        <el-table-column label="读音" align="left" prop="charTone" />

        <el-table-column width="200" label="操作" align="left" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="gap-12">
              <el-button link type="primary" @click.stop="handleEdit(scope.row)" text>编辑</el-button>
              <span @click.stop>
                <el-popconfirm
                  width="220"
                  placement="top"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  @confirm="handleDelete(scope.row)"
                  title="确定删除吗？"
                >
                  <template #reference>
                    <el-button link type="primary" text>删除</el-button>
                  </template>
                </el-popconfirm>
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination
        v-show="total > 0"
        v-if="queryParams.status !== 4"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      /> -->
      <el-dialog
        ref="formDialogRef"
        :title="dialogTitle"
        v-model="visibleFlag"
        width="800px"
        append-to-body
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form ref="formParamsRef" :model="form" :rules="rules" v-loading="dialogLoading" label-position="left" label-width="100px">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="词语" prop="content">
                <el-input v-model="form.content" @blur="getToneMapFn(form.content)" placeholder="请输入需要标准读音的词语" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="多音字" prop="charText">
                <span class="tips" v-if="!blurFlag">请先输入词语</span>
                <template v-else-if="charStrList.length > 0">
                  <div class="flex gap-12">
                    <div
                      class="char"
                      @click="setChecked(index)"
                      :class="item.checked ? 'checked' : ''"
                      v-for="(item, index) in charStrList"
                      :key="index"
                      :value="item.charStr"
                      @input="blurFlag = false"
                    >
                      {{ item.charStr }}
                    </div>
                  </div>
                </template>
                <span v-else-if="blurFlag && charStrList.length === 0">该词语中未发现多音字</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="toneList.length === 0">
            <el-col :span="24">
              <el-form-item label="读音" prop="charTone">
                <span class="tips" v-if="toneList.length === 0">请先选择多音字</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-else>
            <el-col :span="24">
              <el-form-item :label="`“${item.charStr}”的读音`" v-for="(item, index) in toneList" :key="index" prop="charTone">
                <div class="gap-12">
                  <div
                    class="char"
                    @click="setToneChecked(index, it.key)"
                    :class="it.key === item.currentTone ? 'checked' : ''"
                    v-for="(it, i) in item.toneArray"
                    :key="i"
                  >
                    {{ it.value }}
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getPolyphoneRecord, getToneMap, addPolyphoneRecord, removePolyphoneRecord } from '@/api/polyphone';
import { PolyphoneRecordVo } from '@/api/polyphone/types';
// import { deepClone } from '@/utils';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import type { FormRules } from 'element-plus'
// import { number } from 'vue-types';
// import { it } from 'element-plus/es/locale';
const polyphoneList = ref<PolyphoneRecordVo[]>([]);
const loading = ref(true);
const btnLoading = ref(false);
const formParamsRef = ref()
const visibleFlag = ref(false)
const currentRowId = ref('')
const charStrList = ref<any>([])
const blurFlag = ref(false)
const dialogLoading = ref(false)
// const total = ref(0);
const emits = defineEmits(['change-type']);
const dialogTitle = ref('新增记录')

const rowToneList = ref<any>([])

const checkChar = (rule: any, value: any, callback: any) => {
  const regex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
  if (!regex.test(value)) {
    callback(new Error('不能输入标点符号'))
  } else {
    callback()
  }
}

const form = ref({
  content: ''
})
const rules = reactive<FormRules<{ content: string }>>({
  content: [
    { required: true, message: '请输入词语', trigger: ["blur"] },
    {
      min: 1,
      max: 16,
      message: '最多16个字符',
      trigger: ["blur"]
    }, {
      validator: checkChar,
      trigger: ["blur"]
    }
  ],
})
// 获取读音
const getToneMapFn = async (content: string) => {
  if (!content) return
  blurFlag.value = true
  dialogLoading.value = true
  const res = await getToneMap(content)
  dialogLoading.value = false
  res.data.forEach((element: any) => {
    if (rowToneList.value.findIndex((it: any) => it.charStr === element.charStr) !== -1) {
      element.checked = true
    }
  })
  charStrList.value = res.data
  if (currentRowId.value) {
    nextTick(() => {
      setEditCurrentTone()
    })
  }
}

/**编辑 */
const handleEdit = (row: any) => {
  // emits('change-type', { flagType: 'detail', isEdit: true, id: row.id })
  currentRowId.value = row.id
  form.value.content = row.content
  visibleFlag.value = true
  rowToneList.value = JSON.parse(row.toneSequence)
  getToneMapFn(row.content)
  dialogTitle.value = '编辑' + row.content
}


// 赋值当前选中的多音字
const setEditCurrentTone = () => {
  const rowArray = [...rowToneList.value]
  console.log(rowToneList.value)
  charStrList.value.forEach((element: any) => {
    const currentToneObj = rowArray.find((it: any) => it.charStr === element.charStr)
    const currentIndex = rowArray.findIndex((it: any) => it.charStr === element.charStr)
    if (element?.charStr && currentToneObj?.charStr) {
      rowArray.splice(currentIndex, 1)
      if (currentToneObj.charStr === element.charStr) {
        element.checked = true
        console.log(currentToneObj.tone)
        element.currentTone = currentToneObj.tone
      }
    } else {
      element.checked = false
      element.currentTone = ''
    }

  })
  console.log(charStrList.value)
}
// 读音数组
const toneList = computed(() => {
  const array = charStrList.value.filter((item: any) => item.checked)
  array.forEach((item: any) => {
    item.toneArray = []
    item.currentTone = item.currentTone?item.currentTone: ''
    Object.keys(item.tone).forEach((key: any) => {
      item.toneArray.push({ key: item.tone[key], value: key })
    })
  })
  return array
})


/**删除 */
const handleDelete = async (row: any) => {
  await removePolyphoneRecord(row.id).then(() => {
    proxy.$modal.msgSuccess('删除成功')
    getList()
  })
}
const closeDialog = () => {
  if (form.value.content) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        initFn()
      })
  } else {
    initFn()
  }
}
const initFn = () => {
  currentRowId.value = ''
  formParamsRef.value.resetFields()
  form.value.content = ''
  charStrList.value = []
  rowToneList.value = []
  blurFlag.value = false
  visibleFlag.value = false
}

const setChecked = (index: number) => {
  console.log(charStrList.value[index])
  charStrList.value[index].checked = !charStrList.value[index].checked
}

const setToneChecked = (index: number, tone: string) => {
  toneList.value[index].currentTone = tone
}


const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      if (charStrList.value.length === 0) {
        ElMessage.error('无多音字保存')
        return
      }
      if (charStrList.value.length > 0 && toneList.value.length === 0) {
        ElMessage.error('请选择多音字')
        return
      }
      if (toneList.value.filter(item => item.currentTone === '').length > 0) {
        ElMessage.error('请选择对应拼音')
        return
      }
      const toneSequence = toneList.value.map((item: any) => {
        return {
          charStr: item.charStr,
          tone: item?.currentTone || '',
          toneWithNumber: item.toneArray.find((it: any) => it.key === item.currentTone).value,
          index: item.index
        }
      })
      btnLoading.value = true
      await addPolyphoneRecord({ id: currentRowId.value, content: form.value.content, toneSequence: JSON.stringify(toneSequence) }).then(() => {
        proxy.$modal.msgSuccess(currentRowId.value ? '修改成功' : '新增成功');
        initFn()
        getList()
      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await getPolyphoneRecord();
  res.rows.forEach(element => {
    element.charText = element.toneSequence ? JSON.parse(element.toneSequence).map((item: any) => item.charStr).join("，") : ""
    element.charTone = element.toneSequence ? JSON.parse(element.toneSequence).map((item: any) => item.toneWithNumber).join("，") : ""
  });
  polyphoneList.value = res.rows;
  loading.value = false;
}

const handleAdd = () => {
  visibleFlag.value = true
  dialogTitle.value = '新增记录'
  currentRowId.value = ''
  nextTick(() => {
    formParamsRef.value.resetFields()
    form.value.content = ''
  })
}


onMounted(() => {
  getList();
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}

:global(.script-type-dialog) {
  background: none;
  box-shadow: none;
}

.script-type {
  &-head {
    color: #fff;
    text-align: center;
    font-size: 24px;
  }

  &-content {
    cursor: pointer;
  }

  &-container {
    width: 80%;
    margin: 0 auto;
  }

  &-box {
    width: 280px;
    height: 310px;
    border-radius: 9px;
    text-align: center;
    background-color: #fff;
    overflow: hidden;
  }

  &-icon {
    width: 93px;
    height: 93px;
    display: block;
    margin: 40px auto 17px;
  }

  &-title {
    font-size: 20px;
    font-weight: 500;
    width: 187px;
  }

  &-tip {
    font-size: 15px;
    margin-top: 20px;
    margin-left: 46px;
    margin-right: 46px;
  }

  &-close {
    display: block;
    width: 32px;
    height: 32px;
    margin: 68px auto 0;
    cursor: pointer;
  }
}

:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 90px - 100px);
  }
}

:deep(.el-table__row) {
  cursor: pointer;
}

.gap-12 {
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  gap: 0 12px;
}

.flex {
  display: flex;
  align-items: center;
}

.char {
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  cursor: pointer;
}

.checked {
  background-color: #4f66ff;
  border: 1px solid #4f66ff;
  color: #fff;
}

.dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  margin-right: 8px;
}

.tips {
  color: #cccccc;
}
</style>
