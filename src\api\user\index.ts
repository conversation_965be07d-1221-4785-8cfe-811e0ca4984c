import request from '@/utils/request';
import { userListQuery, userResult, userFormVo, UserImportResultVo } from './types.ts';
import { AxiosPromise } from 'axios';

// 查询用户列表
export function getUserList(query: userListQuery): AxiosPromise<userResult[]> {
  return request({
    url: '/biz/user/list',
    method: 'get',
    params: query
  });
}
export function saveOrUpdateUser(data: userFormVo): AxiosPromise<string> {
  return request({
    url: '/biz/user/saveOrUpdate',
    method: 'post',
    data: data
  });
}

export function getUserDetail(query: { id: string }): AxiosPromise<userResult> {
  return request({
    url: '/biz/user/detail',
    method: 'get',
    params: query
  });
}
export function freezeUser(array: (number | string)[]): AxiosPromise<string> {
  return request({
    url: '/biz/user/freeze',
    data: array,
    method: 'post'
  });
}
/**人数检查限制 */
export function checkAppUserLimit(): AxiosPromise<any> {
  return request({
    url: '/biz/user/checkAppUserLimit',
    method: 'get'
  });
}
/**批量修改部门 */
export function batchUpdateDeptId(data: { deptId: string; userIdList: string[] }): AxiosPromise<any> {
  return request({
    url: '/biz/user/batchUpdateDeptId',
    method: 'post',
    data
  });
}
/**用户导入数据 */
export function userImportData(data: { file: any; skipDept: boolean }): AxiosPromise<UserImportResultVo> {
  const formData = new FormData();
  formData.append('file', data.file);
  return request({
    url: '/biz/user/importData',
    method: 'post',
    params: { skipDept: data.skipDept },
    data: formData
  });
}

/**用户导出错误数据 */
export function exportErrorData(query: { errorListCacheId: string }): AxiosPromise<any> {
  return request({
    url: '/biz/user/exportErrorData',
    method: 'post',
    params: query
  });
}
/**删除用户 */
export function deleteUser(array: (number | string)[]): AxiosPromise<string> {
  return request({
    url: '/biz/user/delete',
    data: array,
    method: 'post'
  });
}

/**发送提醒激活短息 */
export function sendSms(id: string | number): AxiosPromise<any> {
  return request({
    url: '/biz/sms/send/' + id,
    method: 'get'
  });
}
/**批量发送提醒激活短息 */
export function sendBatchSms(array: (number | string)[]): AxiosPromise<any> {
  return request({
    url: '/biz/sms/batch',
    data: array,
    method: 'post'
  });
}
/**批量修改数据权限 */
export function batchUpdateDataScope(data: {
  userIdList: string[];
  dataScope: string;
  kanbanFlag: boolean;
  shareReportPermission: number;
}): AxiosPromise<any> {
  return request({
    url: '/biz/user/batchUpdateDataScope',
    method: 'post',
    data
  });
}
/**删除用户 */
export function removeWhiteFlag(id: string): AxiosPromise<string> {
  return request({
    url: '/biz/user/remove-white-list/' + id,
    method: 'delete'
  });
}

/**同步人员 */
export function syncUser(): AxiosPromise<any> {
  return request({
    url: '/biz/user/sync',
    method: 'post'
  });
}
/**是否正在同步 */
export function getIsSyncing(): AxiosPromise<boolean> {
  return request({
    url: '/biz/user/isSyncing',
    method: 'get'
  });
}
