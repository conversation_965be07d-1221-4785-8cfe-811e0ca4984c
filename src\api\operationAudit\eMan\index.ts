import request from '@/utils/request';
import { eManQuery, eManListResult, eManCertificationVo } from './types';
import { AxiosPromise } from 'axios';
import { AuditFormVo } from '../professionalCertification/types';

// 查询租户列表
export function eManList(query: eManQuery): AxiosPromise<eManListResult[]> {
  return request({
    url: '/biz/eman/list',
    method: 'get',
    params: query
  });
}
export function geteManAuditDetail(query: { id: string }): AxiosPromise<eManCertificationVo> {
  return request({
    url: '/biz/eman/auditDetail',
    method: 'get',
    params: query
  });
}

export function audit(data: AuditFormVo): AxiosPromise<any> {
  return request({
    url: '/biz/eman/audit',
    method: 'post',
    data
  });
}
