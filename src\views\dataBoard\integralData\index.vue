<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">积分数据</span>
          <div class="search-container flex">
            <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
              <el-form-item prop="userName">
                <el-input
                  v-model="queryParams.userName"
                  @clear="resetQuery"
                  placeholder="搜索姓名"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item prop="pointTypeList">
                <el-select
                  v-model="queryParams.pointTypeList"
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="1"
                  multiple
                  placeholder="所有积分"
                  clearable
                  style="width: 240px"
                >
                  <el-option v-for="item in pointTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item prop="deptIds">
                <el-tree-select
                  v-model="queryParams.deptIds"
                  @change="handleQuery"
                  clearable
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children', disabled: 'selected' }"
                  node-key="id"
                  check-strictly
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="2"
                  multiple
                  show-checkbox
                  style="width: 250px"
                  :default-expanded-keys="defaultExpandedKeys"
                  placeholder="请选择部门"
                  popper-class="dept-popper"
                  :default-expand-all="false"
                />
              </el-form-item>
              <el-form-item prop="time">
                <el-date-picker
                  :disabled-date="disabledDate"
                  v-model="queryParams.timeArray"
                  type="daterange"
                  :clearable="false"
                  range-separator="-"
                  :shortcuts="shortcuts"
                  popper-class="date-picker"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 250px;"
                />
                <!-- <el-radio-group v-model="currentSelected" @change="initDateArray()">
                  <el-radio-button v-for="item in timeArray" :key="item.text" :label="item.text" :value="item.text" />
                </el-radio-group> -->
              </el-form-item>
            </el-form>
            <el-tooltip content="导出" placement="top">
              <img class="export" src="@/assets/icons/png/export.png" @click="handleExport" />
            </el-tooltip>
          </div>
        </div>
      </template>
      <div class="box-container">
        <el-table v-loading="loading" @sort-change="sortChange" :default-sort="{ prop: 'points', order: 'descending' }" :data="integralList">
          <el-table-column label="序号" align="left" type="index" width="100" />
          <el-table-column label="姓名" align="center" prop="name" width="180" />

          <el-table-column label="部门" align="left" prop="deptName" />
          <el-table-column
            label="账号开通时间"
            align="center"
            prop="createTime"
            width="200"
            sortable="custom"
            :sort-orders="['ascending', 'descending']"
          />
          <el-table-column
            label="新增积分"
            align="center"
            prop="filterPoints"
            width="100"
            sortable="custom"
            :sort-orders="['ascending', 'descending']"
          />
          <el-table-column
            label="总积分"
            width="100"
            align="center"
            prop="points"
            sortable="custom"
            :sort-orders="['ascending', 'descending']"
          ></el-table-column>
          />
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button link type="primary" text @click="goToMemberDetail(scope.row.id)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width: 100%;justify-content: space-between;">
          <el-tooltip content="积分清除" placement="top">
            <img
              src="@/assets/icons/svg/clear.svg"
              v-hasPermi="['integral:clear']"
              style="width: 20px; height: 20px; cursor: pointer;float: left;margin-top: 30px;"
              @click="clearShow = true"
            />
          </el-tooltip>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
      <ClearData v-model:visible="clearShow" @success="getList"></ClearData>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onBeforeRouteLeave } from "vue-router";
import { TreeLong } from "@/api/department/types";
import { useUserStore } from '@/store/modules/user';
import { download } from "@/utils/request";
import { getPointList } from "@/api/point";
import { UserPointVo } from "@/api/point/types";
import cache from '@/plugins/cache';
// import { deepClone, treeToFlat } from "@/utils/index";
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import ClearData from "../components/clearData.vue";
dayjs.locale('zh-cn')
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const router = useRouter();
const defaultExpandedKeys = ref<string[]>([])
const deptOptions = ref<TreeLong[]>([])
const queryFormRef = ref<ElFormInstance>();
const integralList = ref<UserPointVo[]>([])
const userStore = useUserStore();
const clearShow = ref(false)
const isCurrentWeek = ref(true)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    isAsc: 'desc',
    deptIds: [],
    startTime: '',
    endTime: '',
    pointTypeList: [],
    orderByColumn: 'points',
    timeArray: [] as string[]
  },
});
const shortcuts = [
  {
    text: '本周',
    value: () => {
      // 获取本周开始日期
      const startOfWeek = dayjs().startOf('week');
      // 获取本周结束日期
      const endOfWeek = dayjs();
      return [startOfWeek, endOfWeek]
    },
  },
  {
    text: '上周',
    value: () => {
      // 获取今天
      const today = dayjs();
      // 获取上周的开始日期
      const startOfLastWeek = today.subtract(7, 'day').startOf('week');
      // 获取上周的结束日期
      const endOfLastWeek = today.subtract(7, 'day').endOf('week');
      return [startOfLastWeek, endOfLastWeek]
    },
  },
  {
    text: '本月',
    value: () => {
      // 获取本月开始日期
      const startOfMonth = dayjs().startOf('month');
      // 获取本月结束日期
      const endOfMonth = dayjs();
      return [startOfMonth, endOfMonth]
    },
  },
  {
    text: '上月',
    value: () => {
      // 获取今天
      // 获取上周的开始日期
      const startOfMonth = dayjs().subtract(1, 'month').startOf('month')
      // 获取上周的结束日期
      const endOfMonth = dayjs().subtract(1, 'month').endOf('month')
      return [startOfMonth, endOfMonth]
    },
  },
]
const pointTypeOptions = ref<any>([{
  value: '签到',
  label: '签到积分'
}, {
  value: '技巧',
  label: '技巧练习积分'
}, {
  value: '答题',
  label: '答题练习积分'
}, {
  value: '幻灯片',
  label: '幻灯片练习积分'
}])
const loading = ref(false);
const total = ref(0);
const { queryParams } = toRefs(data);
const orderMap = new Map([
  ['points', 'points'],
  ['filterPoints', 'filter_points'],
  ['createTime', 'create_time'],

])

const sortChange = (column: any) => {
  queryParams.value.isAsc = column.order
  queryParams.value.orderByColumn = orderMap.get(column.prop) || ''
  handleQuery()
}

const handleExport = () => {
  let name = ''
  const param = {
    deptIds: queryParams.value.deptIds,
    isAsc: queryParams.value.isAsc,
    startTime: queryParams.value.startTime,
    endTime: queryParams.value.endTime,
    userName: queryParams.value.userName,
    orderByColumn: queryParams.value.orderByColumn,
    pointTypeList: queryParams.value.pointTypeList,
  }
  if (param.pointTypeList.length > 0){
    name = param.pointTypeList.join('、')
  }

    download('/biz/point/userPointListExport', param, `${queryParams.value.startTime}至${queryParams.value.endTime}${name}积分数据.xlsx`)
  }

  const goToMemberDetail = (id: number) => {

    router.push({
      name: 'Integral-detail', query: {
        userId: id,
      }
    });
  }
  //监听日期选择
  watch(
    () => queryParams.value.timeArray,
    (val) => {
      if (val && val.length > 0) {
        const startOfWeek = dayjs().startOf('week').format('YYYY-MM-DD');
        const endOfWeek = dayjs().format('YYYY-MM-DD');
        isCurrentWeek.value = (startOfWeek === val[0] && endOfWeek === val[1])
        queryParams.value.startTime = val[0];
        queryParams.value.endTime = val[1];
        getList()
      } else {
        queryParams.value.startTime = '';
        queryParams.value.endTime = '';
        getList()
      }
    }
  );

  watch(
    () => queryParams.value.pointTypeList,
    (val) => {
      if (val && val.length > 0) {
        queryParams.value.pageNum = 1
        getList()
      } else {
        queryParams.value.pointTypeList = undefined
        getList()
      }
    }
  )


  const disabledDate = (val: any) => {
    return val > new Date();
  };
  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }



  /** 搜索 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }
  const getList = async () => {
    const res = await getPointList(queryParams.value)
    integralList.value = res.rows;
    total.value = res.total;
    loading.value = false;
    proxy?.$modal.closeLoading();
  }

  const _getDeptTreeList = async () => {
    const res = await userStore.getDeptTreeSelectedListFn()
    if (res.length > 0) {
      deptOptions.value = res
      defaultExpandedKeys.value = res.length > 0 ? [res[0].id] : []
      // const array1 = treeToFlat(deepClone(res))
      // const filterArray = array1.filter((item: any) => item.selected === false)
      // const deptIds = filterArray.length > 0 ? [filterArray[0].id] : []
      // queryParams.value.deptIds = deptIds
    }

    const query = cache.session.getJSON('pointQuery')
    if (query) {
      queryParams.value = query
      cache.session.remove('pointQuery')
    }
    handleDate()
  }

  const handleDate = () => {
    // 默认本周
    const startOfWeek = dayjs().startOf('week').format('YYYY-MM-DD');
    const endOfWeek = dayjs().format('YYYY-MM-DD');;
    queryParams.value.timeArray = [startOfWeek, endOfWeek]
  }



  // watch(() => router, newRoute => {
  //   console.log(newRoute, '234');
  //   console.log(newRoute.options.history.state)
  //   console.log(newRoute.options.history.state.back, '上一页面路由');
  // }, { immediate: true, deep: true })

  onBeforeRouteLeave((to, from) => {
    if (to.path === '/dataBoard/integral-detail') {
      cache.session.setJSON('pointQuery', queryParams.value)
    } else {
      const query = cache.session.getJSON('pointQuery')
      if (query) {
        cache.session.remove('pointQuery')
      }
    }
  })
  onUnmounted(() => {
    // cache.session.remove('pointQuery')
  })
  onMounted(() => {
    proxy?.$modal.loading("正在加载");
    _getDeptTreeList()
  })
</script>
<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  align-items: center
}

.export {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-end;
  margin: 0 auto;
  // padding: 0 30px 30px 30px;
  width: 100%;

}

.box-title {
  justify-content: flex-end;

  // width: 100%;
  :deep(.el-form-item--default) {
    margin-bottom: 0px;
  }

  :deep(.el-form-item) {
    margin-right: 16px;
  }
}
</style>
