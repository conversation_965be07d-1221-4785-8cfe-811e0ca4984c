<template>
  <div class="p-2">
    <el-table v-loading="loading" ref="tableRef" :data="memberList" :default-sort="defaultSort" @sort-change="sortChange">
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="姓名" align="center" prop="name" width="180" />
      <el-table-column label="部门" align="center" prop="deptName" />

      <!-- <el-table-column label="职务" align="center" prop="type" />
          <el-table-column label="角色" align="center" prop="type" /> -->
      <el-table-column label="总时长" align="center" prop="totalTime" width="150" sortable="custom" :sort-orders="['ascending', 'descending']">
        <template #default="scope">
          {{ scope.row.totalTime || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="总次数" align="center" prop="totalCount" width="100" sortable="custom" :sort-orders="['ascending', 'descending']">
        <template #default="scope">
          {{ scope.row.totalCount || '0' }}
        </template>
      </el-table-column>
      <el-table-column label="最高分" align="center" prop="maxScore" width="100" sortable="custom" :sort-orders="['ascending', 'descending']">
        <template #default="scope">
          {{ scope.row.maxScore || '0' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button link type="primary" @click="goToMemberDetail(scope.row.id)" text>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="user" lang="ts">
import { getUserChatStatics, } from "@/api/dataBoard";
import { UserChatResult, } from "@/api/dataBoard/types";
import { onBeforeRouteLeave } from "vue-router";
import cache from '@/plugins/cache';
const tableRef = ref()
const router = useRouter();
const props = defineProps({
  deptIds: {
    type: Array,
    default: () => []
  },
  scriptId: {
    type: String,
    default: ''
  },
  timeArray: {
    type: Array,
    default: () => []
  },
})

const orderMap = new Map([
  ['maxScore', 'max_score'],
  ['totalTime', 'total_time'],
  ['totalCount', 'total_count'],
])

const reverseMap = new Map();
orderMap.forEach((value, key) => {
  if (!reverseMap.has(value)) {
    reverseMap.set(value, []);
  }
  reverseMap.get(value).push(key);
});

const defaultSort = ref(
  { prop: 'totalTime', order: 'descending' }
)
const memberList = ref<UserChatResult[]>([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startTime: '',
    endTime: '',
    deptIds: [],
    scriptId: '',
    orderByColumn: 'total_time',
    isAsc: 'desc',
    timeArray: [] as string[]
  },
});
// const currentSelected = ref('今天')
const { queryParams } = toRefs(data);

const durationTime = ref({
  hours: 0,
  minutes: 0,
  seconds: 0,
})


//监听
watch(
  () => props,
  (val: any) => {
    console.log(val)
    if (val.timeArray && val.timeArray.length > 0) {
      queryParams.value.startTime = val.timeArray[0];
      queryParams.value.endTime = val.timeArray[1];
    } else {
      queryParams.value.startTime = '';
      queryParams.value.endTime = '';
    }
    if (val.deptIds) queryParams.value.deptIds = val.deptIds
    if (val.scriptId) queryParams.value.scriptId = val.scriptId
    queryParams.value.pageNum = 1
    getList()

  }, {
  deep: true
}
);


const sortChange = (column: any) => {
  console.log(column)
  queryParams.value.isAsc = column.order ? column.order === 'ascending' ? 'asc' : 'desc' : 'desc'
  queryParams.value.orderByColumn = orderMap.get(column.prop) || ''
  getList()
}


const goToMemberDetail = (id: number) => {
  cache.session.setJSON('memberScriptDataQuery', queryParams.value)
  router.push({
    name: 'Member-detail', query: {
      userId: id,
      startTime: queryParams.value.startTime,
      endTime: queryParams.value.endTime,
      scriptId: queryParams.value.scriptId,
    }
  });
}


/** 查询列表 */
const getList =  () => {
  nextTick( async() => {
    if (cache.session.getJSON('memberScriptDataQuery')) {
      queryParams.value = cache.session.getJSON('memberScriptDataQuery')
      // defaultSort.value = { prop: queryParams.value.orderByColumn, order: queryParams.value.isAsc }
      tableRef.value.clearSort()
      tableRef.value.sort(reverseMap.get(queryParams.value.orderByColumn)[0], queryParams.value.isAsc === 'asc' ? 'ascending' : 'descending')
      cache.session.remove('memberScriptDataQuery')
      defaultSort.value = { prop: reverseMap.get(queryParams.value.orderByColumn)[0], order: queryParams.value.isAsc === 'asc' ? 'ascending' : 'descending' }
    }

    // console.log(defaultSort.value)
    // console.log(queryParams.value)

    // console.log(cache.session.getJSON('memberScriptDataQuery'))
    loading.value = true;
    const res = await getUserChatStatics(queryParams.value);
    res.rows.forEach(item => {
      if (item.totalTime) {
        const hours = Math.floor(Number(item.totalTime) / 3600)
        const min = Math.floor((Number(item.totalTime) % 3600) / 60)
        const seconds = Number(item.totalTime) % 60; // 计算剩余秒数
        durationTime
        item.totalTime = hours > 0 ? hours + '小时' + min + '分' + seconds + '秒' : min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
      } else {
        item.totalTime = '0秒'
      }

    })
    memberList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  })
}


onBeforeRouteLeave((to, from) => {
  // const query = cache.session.getJSON('pointQuery')
  // if (query) {
  //   cache.session.remove('pointQuery')
  // }
  console.log(to.path)
  if (to.path === '/dataBoard/member-detail') {
    cache.session.setJSON('memberScriptDataQuery', queryParams.value)
  } else {
    const query = cache.session.getJSON('memberDataQuery')
    if (query) {
      cache.session.remove('memberScriptDataQuery')
      cache.session.remove('memberDataQuery')
    }
  }

})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.card-title {
  font-size: 24px;
  font-weight: bold;
}



.flex {
  display: flex;
  align-items: center
}

.num {
  font-family: 'inter';
}

.box-wrap {
  height: 100%;
  overflow-y: auto;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-start;
  margin: 0 auto;
  padding: 0 30px 30px 30px;
  width: 100%;

  .box-title {
    justify-content: space-between;
    width: 100%;
  }

  .select-date {}

  .box-content {
    width: 100%;
    justify-content: space-between;
    gap: 0 24px;

    .box-item {
      background-color: #f4f6ff;
      flex: 1;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;

      .item-title {
        font-weight: bold;
        font-size: 14px;
      }

      .unit {
        font-size: 14px;
        font-weight: normal;
      }

      .item-count {
        width: 100%;
        text-align: center;
        font-size: 28px;
        margin: 24px 0;
        font-weight: bold;
        letter-spacing: 2px;

      }
    }
  }
}


:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 70px - 100px);
  }
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}
</style>
