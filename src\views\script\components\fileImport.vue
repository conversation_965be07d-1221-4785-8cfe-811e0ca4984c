<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      :title="title"
      v-model="visibleFlag"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="box-container">
        <div class="box">
          <div class="box-title">1.下载导入模板</div>
          <div class="box-tips">根据提示信息完善表格内容</div>
          <el-button type="primary" @click="open">下载模板表格</el-button>
        </div>
        <div class="box">
          <div class="box-title">2.上传完善后的模板</div>
          <div class="box-tips">更新模板中的信息后上传</div>
          <el-upload
            multiple
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="handleUploadChange"
            v-model:file-list="fileList"
            :drag="qaArray.length === 0 ? true : false"
            :on-error="handleUploadError"
            :on-success="handleUploadSuccess"
            :show-file-list="false"
            :headers="headers"
            class="upload-file-uploader"
            :auto-upload="false"
            ref="fileUploadRef"
          >
            <!-- 上传按钮 -->
            <!-- <el-button type="primary">选取文件</el-button> -->
            <div class="upload-container" v-if="fileList.length === 0">
              <img src="@/assets/icons/png/upload.png" />
              <div class="right">
                <div>将文档拖到此处，或点击上传</div>
              </div>
            </div>
            <div class="upload-container" v-else>
              <img src="@/assets/icons/png/excel.png" />
              <div class="right">
                <div>{{ fileList[0].name }}</div>
                <el-button type="primary">重新选择</el-button>
              </div>
            </div>
          </el-upload>
          <!-- <FileUpload :isShowTip="false" ></FileUpload> -->
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="importLoading" :disabled="fileList.length === 0">导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { genFileId, ElMessageBox } from 'element-plus'
import type { UploadRawFile } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import { ScriptQABo } from '@/api/script/types'

const emits = defineEmits(['sendQA', 'update:visible']);
import { globalHeaders } from "@/utils/request";
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + "/biz/script/batch-import-qa"); // 上传文件服务器地址
const headers = ref(globalHeaders());
// const fileFlag = ref<boolean>(true);
const fileType = ref<string[]>(['xls', 'xlsx'])
// const fileName = ref<string>('')
const qaArray = ref<ScriptQABo[]>([])
const fileUploadRef = ref()
const fileList = ref([])
const importLoading = ref(false)
// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'AI生成'
  },
  scriptId: {
    type: String,
    default: ''
  },

})


// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const open = () => {
  window.open(import.meta.env.VITE_APP_OSS_PREFIX+'/excel_template/%E7%AD%94%E9%A2%98%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx')
}

const closeDialog = () => {
  if (fileList.value.length > 0) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要取消吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        importLoading.value=false
        fileUploadRef.value!.clearFiles()
        visibleFlag.value = false
      })
      .catch(() => {
      })
  } else {
    importLoading.value=false
    visibleFlag.value = false
  }

}

// 上传结束处理
const uploadedSuccessfully = () => {
  // fileFlag.value = false
  proxy?.$modal.msgSuccess("导入成功");
  emits('sendQA', qaArray.value)
  qaArray.value = []
  importLoading.value=false
  fileList.value=[]
  proxy?.$modal.closeLoading();
  closeDialog()

}



// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
  // 校检文件类型
  if (fileType.value.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join("/")}格式文件!`);
      return false;
    }
  }
  // 校检文件大小
  // if (props.fileSize) {
  //     const isLt = file.size / 1024 / 1024 < props.fileSize;
  //     if (!isLt) {
  //         proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
  //         return false;
  //     }
  // }
  // fileName.value = file.name
  proxy?.$modal.loading("正在上传文件，请稍候...");
  // number.value++;
  return true;
}

const handleUploadChange=(file: any)=>{
  if (fileType.value.length) {
    const fileName =  file.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join("/")}格式文件!`);
      fileUploadRef.value?.handleRemove(file);
      return false;
    }
    importLoading.value=false
  }
}
const handleExceed = (files: any) => {
  console.log(files)
  fileUploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  fileUploadRef.value!.handleStart(file)
}

// 上传失败
const handleUploadError = () => {
  proxy?.$modal.msgError("上传文件失败");
}

// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  if (res.code === 200) {
    qaArray.value = res.data
    uploadedSuccessfully();
  } else {
    // number.value--;
    qaArray.value = []
    proxy?.$modal.closeLoading();
    proxy?.$modal.msgError(res.msg);
    fileUploadRef.value?.handleRemove(file);
    importLoading.value=false
  }
}

const handleSubmit = () => {
  importLoading.value = true
  fileUploadRef.value!.submit()
}
</script>

<style scoped lang="scss">
.box-container {
  display: flex;
  flex-direction: column;
  gap: 8px 0;
}

.box {
  padding: 16px 32px;
  background: rgba(247, 249, 255, 0.8);

  &-title {
    font-weight: bold;
    font-size: 16px;
  }

  &-tips {
    font-size: 12px;
    color: #999999;
    margin: 12px 0;
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
}

.upload-container {
  margin: 0 auto;
  display: flex;
  gap: 0 16px;
  width: 100%;

  border-radius: 4px;

  img {
    width: 48px;
    height: 48px;
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    gap: 8px 0;

    .tips {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
      color: #999999;
    }

  }
}
</style>
