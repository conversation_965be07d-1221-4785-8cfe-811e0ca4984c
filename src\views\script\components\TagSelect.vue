<template>
  <div class="tag-select">
    <div
      class="tag"
      :class="(multi ? (selectedItem && selectedItem.includes(item)) : selectedItem === item) ? 'selected' : 'default'"
      v-for="item in tags"
      :key="item"
      @click="selectItem(item)"
    >
      {{ item }}
    </div>
    <div class="add-flag" v-show="addFlag">
      <div v-for="(item, index) in newTags" :key="index">
        <el-input
          v-if="item.inputVisible"
          :ref="`InputRef${index}`"
          v-model="updateInputValue"
          style="width: 200px;"
          @blur="handleTagInputConfirm(index)"
        />
        <div
          v-else
          class="tag"
          style="padding-right: 24px;"
          :class="(multi ? (selectedItem && selectedItem.includes(item.value)) : selectedItem === item.value) ? 'selected' : 'default'"
          @click="selectItem(item.value)"
          @dblclick="handleDoubleClick(index)"
        >
          <span> {{ item.value }} </span
          ><el-icon
            class="close"
            @click.stop="removeTag(index)"
            :color="(multi ? (selectedItem && selectedItem.includes(item.value)) : selectedItem === item.value) ? '#ffffff' : '#999999'"
          >
            <Close />
          </el-icon>
        </div>
      </div>
    </div>
    <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" style="width: 200px;" @blur="handleInputConfirm" />
    <el-button :disabled="addDisabled" style="padding:4px 30px;font-size: 16px;" v-else @click="showInput" v-show="addFlag">+</el-button>

    <!-- <div class="tag default">+</div> -->
  </div>
</template>
<script lang="ts" setup name="TagSelect">
import { computed } from 'vue'
import { useFormItem } from 'element-plus'
// import { fa } from 'element-plus/es/locale'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const addDisabled = ref(false)
const inputValue = ref('')
const updateInputValue = ref('')
const inputVisible = ref(false)
const InputRef = ref(null)
const props = withDefaults(defineProps<{minWordLength:number, maxWordLength:number,modelValue: string[] | string, tags: string[], newTags?: { value: string, inputVisible: boolean }[], multi?: boolean, addFlag?: boolean }>(), {
  tags: () => [],
  newTags: () => [],
  multi: true,
  addFlag: true,
  maxWordLength:20,
  minWordLength:2,
})
const newTags = ref<{ value: string, inputVisible: boolean }[]>(props.newTags || [])

console.log(props.maxWordLength)

const emits = defineEmits(['update:modelValue'])

const { formItem } = useFormItem()

const selectedItem = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    formItem?.validate('blur')
  }
})

watch(() => props.tags, (val) => {
  if (props.modelValue.length > 0 && props.addFlag) {
    (props.modelValue as string[]).forEach((item: string) => {
      if (!props.tags.includes(item)) {
        newTags.value.push({ inputVisible: false, value: item })
      }
    })
  }
})





const selectItem = (item: string) => {
  if (props.multi) {
    if (selectedItem.value.includes(item)) {
      selectedItem.value = selectedItem.value.filter((i: string) => i !== item)
    } else {
      selectedItem.value = [...selectedItem.value, item]
    }
  } else {
    if (selectedItem.value && selectedItem.value === item) {
      selectedItem.value = ''
    } else {
      selectedItem.value = item
    }

  }

}


const handleDoubleClick = (index: number) => {
  addDisabled.value = true
  let val = null
  if ((typeof selectedItem.value) === 'string') {
    val = selectedItem.value.includes(newTags.value[index].value)
  } else {
    val = (selectedItem.value as string[]).some((item) => item === newTags.value[index].value); //寻找是否有选中的
  }
  if (val) {
    ElMessage.error('选中不可编辑')
    return
  }
  newTags.value[index].inputVisible = true
  updateInputValue.value = newTags.value[index].value
  nextTick(() => {
    proxy.$refs[`InputRef${index}`][0].focus()
  })
}

const removeTag = (index: number) => {
  if (selectedItem.value.length > 0) {
    let val = null
    if ((typeof selectedItem.value) === 'string') {
      val = selectedItem.value.includes(newTags.value[index].value)
    } else {
      val = (selectedItem.value as string[]).some((item) => item === newTags.value[index].value); //寻找是否有选中的
    }
    if (!val) {
      newTags.value.splice(index, 1)
    } else {
      ElMessage.error('已选中不可删除')
    }
  } else {
    newTags.value.splice(index, 1)
  }
}


const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}
const handleTagInputConfirm = (index: number) => {
  console.log(1111)
  console.log(2222)
  if (updateInputValue.value !== '') {
    // const isValid = /^[\u4e00-\u9fa5]*$/.test(updateInputValue.value);
    // if (!isValid) {
    //   ElMessage.error('请仅输入文字');
    //   return
    // }

    if (updateInputValue.value.length < props.minWordLength) {
      ElMessage.error(`最少填写${props.minWordLength}个字符`);
      return
    } if (updateInputValue.value.length > props.maxWordLength) {
      console.log(props.maxWordLength)
      ElMessage.error(`最多填写${props.maxWordLength}个字符`);
      return
    }
    if (newTags.value[index].value !== updateInputValue.value) {
      const flag = newTags.value.some((item) => item.value === updateInputValue.value)
      const flag1 = props.tags.some((item) => item === updateInputValue.value)
      if (flag || flag1) {
        ElMessage.error('该词条已存在');
        return
      }
    }
    if (updateInputValue.value !== newTags.value[index].value) {
      let val = selectedItem.value.indexOf(newTags.value[index].value);
      if (val !== -1) {
        //修改了后，把之前选中的去了
        if (typeof selectedItem.value === 'string') {
          selectedItem.value = ''
        } else {
          (selectedItem.value as string[]).splice(val, 1)
        }
      }
    }
    newTags.value[index].value = updateInputValue.value
    newTags.value[index].inputVisible = false
    updateInputValue.value = ''
    addDisabled.value = false
  }
  else {
    ElMessage.error('关键词不能为空');
  }
}

const handleInputConfirm = () => {
  if (inputValue.value !== '') {
    // const isValid = /^[\u4e00-\u9fa5]*$/.test(inputValue.value);
    // if (!isValid) {
    //   ElMessage.error('请仅输入文字');
    //   return
    // }

    if (inputValue.value.length < props.minWordLength) {
      ElMessage.error(`最少填写${props.minWordLength}个字符`);
      return
    } if (inputValue.value.length > props.maxWordLength) {
      ElMessage.error(`最多填写${props.maxWordLength}个字符`);
      return
    }
    //判断是否重复
    const flag = newTags.value.some((item) => item.value === inputValue.value)
    const flag1 = props.tags.some((item) => item === inputValue.value)
    if (flag || flag1) {
      ElMessage.error('该词条已存在');
      return
    }
    if (inputValue.value) {
      newTags.value.push({ value: inputValue.value, inputVisible: false })
    }
    inputVisible.value = false
    inputValue.value = ''
  } else {
    inputVisible.value = false
  }
}

watch(() => props.newTags, (val) => {
  newTags.value = [...val]
})
</script>
<style lang="scss" scoped>
.el-button+.el-button {
  margin-left: 0;
}

.tag-select {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}


.add-flag {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  cursor: pointer;
  padding: 0px 16px;
  border-radius: 4px;
  position: relative;
  min-width: 20px;
  height: 30px;
  display: flex;
  align-items: center;
}

.close {
  cursor: pointer;
  position: absolute;
  right: 4px;
  top: 9px;
}

.selected {
  background-color: #4F66FF;
  color: #ffffff;
}

.default {
  color: --el-button-text-color;
  border: 1px solid #dcdfe6;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #ffffff;
}
</style>
