<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />{{ currentPageData.name }}</span
          >
        </div>
      </template>
      <div class="box-container">
        <el-table v-loading="loading" :data="currentPageData.levelStages" @row-click="gotoActivityItem">
          <el-table-column label="关卡" align="left" prop="orderNum" width="200">
            <template #default="scope"> 关卡{{ scope.row.orderNum }} </template>
          </el-table-column>

          <el-table-column label="任务" align="left" prop="detail">
            <template #default="scope">
              <div class="" v-for="(item,index) in scope.row.detail" :key="index">{{ item.conditionText }}</div>
            </template>
          </el-table-column>

          <el-table-column label="已通关人数" align="left" prop="completionCount" width="100" />
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button link type="primary" text @click="gotoActivityItem(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getLevelDetail } from "@/api/eventLevel";
import { LevelFormBo } from '@/api/script/types';
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const route = useRoute();
const router = useRouter();
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
});
const currentPageData = ref<LevelFormBo>({} as LevelFormBo)

const loading = ref(false);
const total = ref(0);
const { queryParams } = toRefs(data);



const gotoActivityItem = (row: any) => {
  router.push({
    name: 'Activity-item', query: {
      id: row.id,
    }
  });
}

const _getDetail = async () => {
    const res = await getLevelDetail(route.query.id as string);
    res.data.levelStages.forEach((el: any, index: any) => {
        el.popId = new Date().getTime() + index
        el.detail.forEach((element: any) => {
            let text = element.scriptName
            element.condition.forEach((item: any) => {
                element.conditionText = text += (`,获得${item.time}次${item.score}分及以上`)
            });
        });
    });
    currentPageData.value = {
        name: res.data.name,
        levelStages: res.data.levelStages,
        status: res.data.status,
        deptIds: res.data.deptIds.split(","),
        deptNames: res.data.deptNames.split(","),
        startDate: res.data.startDate,
        endDate: res.data.endDate,
        timeArray: [res.data.startDate, res.data.endDate],
    }
    console.log(currentPageData.value)
    proxy?.$modal.closeLoading();
}



onUnmounted(() => {
    // cache.session.remove('pointQuery')
})
onMounted(() => {
    proxy?.$modal.loading("正在加载");
    _getDetail()
})
</script>
<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 0 12px;
    font-size: 24px;
    font-weight: bold;
}

.flex {
    display: flex;
    align-items: center
}

.export {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;
    align-items: flex-end;
    margin: 0 auto;
    // padding: 0 30px 30px 30px;
    width: 100%;

}

.box-title {
    justify-content: space-between;
    width: 100%;
    margin-bottom: 18px;
}
</style>
