<template>
  <el-card>
    <template #header>
      <div class="card-header">
        <span class="card-title flex">
          <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
          新增幻灯片</span
        >
        <div class="right-button">
          <el-button plain type="info" @click="back()">取消</el-button>
          <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
        </div>
      </div>
    </template>
    <div class="box-container" ref="scrollDiv">
      <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="130px" label-position="left">
        <el-row :gutter="24">
          <el-col :span="16">
            <el-form-item label="幻灯片名称" prop="name">
              <el-input v-model="formParams.name" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="16">
            <el-form-item label="产品" prop="product" v-if="formParams.type">
              <el-select clearable v-model="formParams.product" placeholder="请选择或者输入" filterable allow-create>
                <el-option v-for="item in productList" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="16">
            <el-form-item prop="scoringStandardId" v-if="formParams.type">
              <template #label>
                <span class="label-text">整体评分标准</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div class="tip-content">评分标准是系统判断学员演练水平的主要依据，系统将根据此评分标准生成结果报告。</div>
                  </template>
                  <el-icon class="label-tip">
                    <img src="@/assets/icons/svg/help1.svg" class="help" />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="formParams.scoringStandardId" placeholder="请选择">
                <el-option v-for="item in scoringStandardArray" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="16">
            <el-form-item prop="pptScoringType" v-if="formParams.type">
              <template #label>
                <span class="label-text">单页评分方式</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div class="tip-content flex" style="flex-direction: column;">
                      <span> 将会根据每页设定的演练要求，在学员进行幻灯片演练时进行打分。可选择三种： </span>
                      <span> 1.演练要求+语义识别：AI根据学员此页讲解的内容，判断是否符合设定的演练要求，进行自动打分。 </span>
                      <span> 2.关键词+语义识别：需设置每页幻灯片需要讲解的关键词，AI根据学员讲解内容，判断是否提及这些关键词，并进行自动打分。 </span>
                      <span>
                        3.关键词+关键词匹配：需设置每页幻灯片需要讲解到的关键词，系统进行匹配学员此页是否提及演练要求中的关键词，根据比例计算得分。
                      </span>
                    </div>
                  </template>
                  <el-icon class="label-tip">
                    <img src="@/assets/icons/svg/help1.svg" class="help" />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-radio-group v-model="formParams.pptScoringType">
                <el-radio label="1">演练要求+语义识别</el-radio>
                <el-radio label="2">关键词+语义识别</el-radio>
                <el-radio label="3">关键词+关键词匹配</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="align-items: flex-start;">
          <el-col :span="9">
            <el-form-item prop="timeLimit">
              <template #label>
                <span class="label-text">时间限制</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div class="tip-content">
                      整个幻灯片演练的时间限制，系统会根据设定的时限在学员练习时进行倒计时，超时将自动结束练习，最多填写30分钟。
                    </div>
                  </template>
                  <el-icon class="label-tip">
                    <img src="@/assets/icons/svg/help1.svg" class="help" />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-input v-model="formParams.timeLimit" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <div class="tips" style="margin-bottom: 22px;color: #333333; margin-left: 10px;">分钟</div>
        </el-row>
        <el-divider />
        <div class="ppt-list">
          <el-row :gutter="24" class="ppt-list-header">
            <el-col :span="2">
              <div class="ppt-list-head">页码</div>
            </el-col>
            <el-col :span="6">
              <div class="ppt-list-head">页面预览</div>
            </el-col>
            <el-col :span="10">
              <div class="ppt-list-head" v-if="formParams.pptScoringType === '1'">
                <span class="label-text">演练要求</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div class="tip-content">
                      此页幻灯片的演练要求是什么，可以填写讲解要点、过渡语、注意事项、重点等等，学员在练习过程中可查看演练要求进行话术调整，如果不填写默认无要求。
                    </div>
                  </template>
                  <el-icon class="label-tip">
                    <img src="@/assets/icons/svg/help1.svg" class="help" />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="ppt-list-head" v-if="['2', '3'].includes(formParams.pptScoringType)">
                <span class="label-text">关键词</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div class="tip-content">此页幻灯片需要讲解到的关键词是什么，可填写关键词及其同义词，如果不填写默认无关键词。</div>
                  </template>
                  <el-icon class="label-tip">
                    <img src="@/assets/icons/svg/help1.svg" class="help" />
                  </el-icon>
                </el-tooltip>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="ppt-list-head">操作</div>
            </el-col>
          </el-row>
          <el-row class="mt20" :gutter="24" v-for="(item, index) in formParams.pptList" :key="item.id">
            <el-col :span="2">
              <div class="ppt-list-page">
                <div class="ppt-list-page-num">P{{ index + 1 }}</div>
                <div class="ppt-list-page-name">{{ item.name }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="ppt-list-preview">
                <el-form-item :prop="'pptList.' + index + '.imageUrl'" :rules="setRules.image">
                  <el-image :src="item.imageUrl" class="ppt-list-image" fit="contain" hide-on-click-modal :preview-src-list="[item.imageUrl]" />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="10">
              <div class="ppt-list-description" v-if="formParams.pptScoringType === '1'">
                <el-form-item :prop="'pptList.' + index + '.requirement'" :rules="setRules.requirement">
                  <div class="backdrop-text">
                    <el-input
                      v-model="item.requirement"
                      :disabled="item.aiCreateFlag"
                      type="textarea"
                      :placeholder="item.aiCreateFlag ? 'AI生成中……' : '请输入演练关键点'"
                      :autosize="false"
                      resize="none"
                    />
                    <span class="ai" @click="item.aiCreateFlag ? stopLabel(item.imageId) : confirmLabel(item.imageId)">{{
                        item.aiCreateFlag ? '停止生成' : 'AI生成' }}</span>
                  </div>
                </el-form-item>
              </div>
              <div class="keyword-list" v-else>
                <div>
                  <div class="word-box" style="gap: 0;" v-if="item.keywords">
                    <span v-for="(it, i) in JSON.parse(item.keywords) || []" :key="i">
                      {{ it.name }}
                      <template v-if="it.synonyms && it.synonyms.length > 0">/</template>
                      <template v-if="it.synonyms"> {{ it.synonyms.join('/') }}</template>
                      <template v-if="it.remark && formParams.pptScoringType === '2'">( {{ '备注：' + it.remark }})</template>
                      <template v-if="i !== (JSON.parse(item.keywords) || []).length - 1">、</template>
                    </span>
                  </div>
                </div>
                <div>
                  <div class="info-btn" @click="openKeywordDialog(item.imageId)">编辑关键词</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="ppt-list-actions">
                <el-tooltip placement="top" trigger="hover" content="替换">
                  <div class="ppt-list-icon " :class="item.aiCreateFlag ? 'replace-disabled' : 'replace'" @click="replacePPT(index)" />
                </el-tooltip>

                <el-tooltip placement="top" trigger="hover" content="上移">
                  <div class="ppt-list-icon" :class="index === 0 || item.aiCreateFlag ? 'up-disabled' : 'up'" @click="moveUp(index)" />
                </el-tooltip>

                <el-tooltip placement="top" trigger="hover" content="下移">
                  <div
                    class="ppt-list-icon"
                    :class="(index === formParams.pptList.length - 1) || item.aiCreateFlag ? 'down-disabled' : 'down'"
                    @click="moveDown(index)"
                  />
                </el-tooltip>
                <span v-if="(index === 0 && formParams.pptList.length === 1) || item.aiCreateFlag">
                  <el-tooltip placement="top" trigger="hover" content="删除">
                    <div class="ppt-list-icon" :class="item.aiCreateFlag ? 'delete-disabled' : 'delete'" @click="removePPT(index)" />
                  </el-tooltip>
                </span>
                <span v-else>
                  <el-popconfirm
                    width="220"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="removePPT(index)"
                    title="确定删除吗?"
                  >
                    <template #reference>
                      <div>
                        <el-tooltip placement="top" trigger="hover" content="删除">
                          <div class="ppt-list-icon" :class="item.aiCreateFlag ? 'delete-disabled' : 'delete'" />
                        </el-tooltip>
                      </div>
                    </template>
                  </el-popconfirm>
                </span>
              </div>
            </el-col>
          </el-row>
          <div class="button">
            <el-button type="primary" @click="addPPT" icon="plus">添加幻灯片图片</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <PPTUpload
      v-model:visible="showAddPPT"
      :title="uploadTitle"
      :limit="pptUploadLimit"
      :file-size="10"
      :file-type="['jpg', 'jpeg', 'png', 'JPG', 'JPEG', 'PNG']"
      @success="onAddSuccess"
    />
    <KeywordDialog
      :pptScoringType="formParams.pptScoringType"
      v-model:visible="showKeywordDialog"
      @success="getKeywords"
      :currentKeywords="currentKeywords"
    >
    </KeywordDialog>
  </el-card>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ChatReportScoringStandardVo, PPTBo, scriptFormVo } from "@/api/script/types";
import { ElMessageBox, FormRules } from "element-plus";
import { getAllProduct, getChatReportScoringStandard, saveOrUpdateScript } from "@/api/script";
import { deepClone, moveToError } from "@/utils";
import PPTUpload from './components/PPTUpload'
import { globalHeaders } from "@/utils/request";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import KeywordDialog from './components/keywordsDialog.vue'
const showKeywordDialog = ref(false)
const scrollDiv = ref()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
const btnLoading = ref(false)
const scoringStandardArray = ref<ChatReportScoringStandardVo[]>([])
const productList = ref<string[]>([])

// const showBatchAiLabel = ref(false)
// const currentMergeMethod = ref(1)
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const headers = ref(globalHeaders());
const ctrl = new AbortController(); // 用于中断请求
const currentImageId = ref()
// const labelLoading = ref(false)
// const currentImageId = ref()
const currentKeywords = ref<string>('')


const props = defineProps({
  extra: {
    type: Object,
  }
})

const formParams = ref<scriptFormVo>({
  type: 3,
  name: '',
  timeLimit: null,
  scoringStandardId: null,
  product: '',
  pptScoringType: '1',
  pptList: []
})
interface RuleForm {
  name: string
  timeLimit: string
  type: number
  scoringStandardId: string | number
  product: string
  pptScoringType: string
}

const showAddPPT = ref(false)
const pptUploadLimit = ref(100)
const uploadTitle = ref('')
const replaceIndex = ref<number>();
const addType = ref('') // add replace

const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 1000) {
    callback(new Error('最多1000个字符'))
  } else {
    callback()
  }
}

const checkTimeLimit = (rule: any, value: any, callback: any) => {
  if (value > 30) {
    callback(new Error('最多30分钟'))
  } else {
    callback()
  }
}

const checkNameLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('最多64个字符'))
  } else {
    callback()
  }
}
const checkProductLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('最多64个字符'))
  } else {
    callback()
  }
}

const setRules = reactive({
  image: [
    { required: true, message: '请上传图片', trigger: 'blur' },
  ],
  requirement: [
    {
      validator: checkFontLengthLimit,
      trigger: ["change"]
    }
  ]
})

const rules = reactive<FormRules<RuleForm>>({
  pptScoringType: [
    { required: true, message: '请选择单页评分标准', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入幻灯片名称', trigger: 'blur' },
    {
      validator: checkNameLimit,
      trigger: ["blur"]
    }
  ],
  timeLimit: [
    { required: true, message: '请输入整体演练时限', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkTimeLimit,
      trigger: 'blur'
    }
  ],
  scoringStandardId: [
    { required: true, message: '请选择评分标准', trigger: ["blur", "change"] },
  ],
  product: [
    { required: true, message: '请选择产品', trigger: ["blur", "change"] },
    {
      validator: checkProductLimit,
      trigger: ["blur", "change"]
    }
  ]
})



// const closeLabelDialog = () => {
//   showBatchAiLabel.value = false
//   currentMergeMethod.value = 1
// }

// const handleBatchAiLabelOrVoice = (imageId) => {
//   showBatchAiLabel.value = false //隐藏弹框
//   createAiLabel(imageId)
// }

const openKeywordDialog = (imageId: string) => {
  showKeywordDialog.value = true
  currentImageId.value = imageId
  currentKeywords.value = formParams.value.pptList.find((item: any) => item.imageId === imageId).keywords
}

const getKeywords = (keywords: any) => {
  const index = formParams.value.pptList.findIndex((item: any) => item.imageId === currentImageId.value)
  formParams.value.pptList[index].keywords = keywords.length > 0 ? JSON.stringify(keywords) : null
}

const confirmLabel = (imageId: any) => {
  // console.log(imageId)
  const index = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  if (formParams.value.pptList[index].requirement !== '') {
    ElMessageBox.confirm(
      '已存在的内容将会被覆盖。',
      '确定要进行AI生成吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        // currentImageId.value = imageId
        // showBatchAiLabel.value = true
        createAiLabel(imageId)
      })
      .catch(() => {
      })

  } else {
    createAiLabel(imageId)
  }
}
// 终止标注
const stopLabel = (imageId: any) => {
  const index = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  formParams.value.pptList[index].aiCreateFlag = false
  ctrl.abort();
}

// Ai生成标注描述
const createAiLabel = async (imageId: string) => {
  const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  formParams.value.pptList[ctIndex].requirement = ''
  let createResult = formParams.value.pptList[ctIndex].requirement

  const requestOptions = {
    method: "POST", // 请求方法，SSE 通常是 GET 请求。如果涉及到双向通信，需要改为 POST。
    headers: {
      "Content-Type": "text/event-stream", // 设置内容类型为 SSE，即
      ...headers.value
    },
    signal: ctrl.signal,
    // 可以添加其他需要的配置：
    // 如果为 POST 请求，需要携带 body 参数以传递请求体；
    // 如果希望用户切换到另一个页面后仍能保持 SSE 连接，可以配置 openWhenHidden 属性为 true；
    // 如果需要使用 AbortController 来实现检测到问题终止连接的，可以配置 signal 属性等。
  };
  fetchEventSource(baseUrl + '/biz/script/pptAiLabel?imageId=' + imageId, {
    ...requestOptions,
    onopen(response) {
      console.log("Connection opened!", response);
      formParams.value.pptList[ctIndex].aiCreateFlag = true
    },
    onmessage(event) {
      const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
      if (event.data !== '') {
        const data = JSON.parse(event.data)
        // console.log(data.content)
        if (formParams.value.pptList[ctIndex].aiCreateFlag) {
          createResult += data.content
          setResult(imageId, createResult)
        }
      }
    },
    onerror(error) {
      // console.log(error)
      const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
      formParams.value.pptList[ctIndex].aiCreateFlag = false
      ctrl.abort();
      throw error
    },
    onclose() {
      const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
      formParams.value.pptList[ctIndex].aiCreateFlag = false
      console.log("Connection closed!");
    },
  });
}
const setResult = (imageId: string, result: string) => {
  const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  formParams.value.pptList[ctIndex].requirement = result
}



const removePPT = (index: number) => {
  if (formParams.value.pptList[index].aiCreateFlag) {
    return
  }
  if (index === 0 && formParams.value.pptList.length === 1) {
    proxy.$modal.msgError('最后一页幻灯片无法删除')
  } else {
    formParams.value.pptList.splice(index, 1);
    proxy.$modal.msgSuccess('删除成功')
  }
}

const moveUp = (index: number) => {

  if (index === 0 || formParams.value.pptList[index].aiCreateFlag) {
    return;
  }
  const temp = formParams.value.pptList[index];
  formParams.value.pptList.splice(index, 1);
  formParams.value.pptList.splice(index - 1, 0, temp);
}

const moveDown = (index: number) => {

  if ((index === formParams.value.pptList.length - 1) || formParams.value.pptList[index].aiCreateFlag) {
    return;
  }
  const temp = formParams.value.pptList[index];
  formParams.value.pptList.splice(index, 1);
  formParams.value.pptList.splice(index + 1, 0, temp);
}


const addPPT = () => {
  // tood: 最多添加100个
  if (formParams.value.pptList.length >= 100) {
    proxy.$modal.msgError('最多添加100页幻灯片');
    return;
  }

  pptUploadLimit.value = 100;
  addType.value = 'add';
  uploadTitle.value = '上传幻灯片图片'
  showAddPPT.value = true
}

const onAddSuccess = (fileLiet: any) => {
  console.log('onAddSuccess', fileLiet)
  if (pptUploadLimit.value === 1) {
    formParams.value.pptList[replaceIndex.value].imageId = fileLiet[0].ossId;
    formParams.value.pptList[replaceIndex.value].imageUrl = fileLiet[0].url;
  } else {
    // formParams.value.pptList的数量最多是100个，将fileLiet添加到formParams.value.pptList中时，需要判断两者加起来是否超过100个，如果超过则截取前面100个，如果没超过就直接添加
    if (formParams.value.pptList.length + fileLiet.length > 100) {
      fileLiet = fileLiet.slice(0, 100 - formParams.value.pptList.length);
    }
    formParams.value.pptList.push(...fileLiet.map((item) => {
      return {
        imageUrl: item.url,
        imageId: item.ossId,
        id: item.uid,
        requirement: '',
        name: item.name,
        keywords: '',
        aiCreateFlag: false
      }
    }))

    // 滚动到底部
    nextTick(() => {
      let scrollElem = scrollDiv.value;
      scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
    });
  }
}

const replacePPT = (index: number) => {
  if (formParams.value.pptList[index].aiCreateFlag) {
    return
  }
  pptUploadLimit.value = 1;
  replaceIndex.value = index;
  addType.value = 'replace'
  uploadTitle.value = '替换幻灯片图片'
  showAddPPT.value = true
}

const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (
      key !== 'type' &&
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]'
    ) {
      if (key === 'pptList') {
        formParams.value[key].forEach(element => {
          console.log()
          if (JSON.stringify(element.imageUrl) !== JSON.stringify('') && JSON.stringify(element.imageUrl) !== JSON.stringify('')) {
            bool = true;
            return;
          }
        });
      } else {
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '' })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '' })
  }
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {

      if (!formParams.value.pptList.length) {
        proxy.$modal.msgError('至少需要添加一页幻灯片');
        return;
      }
      const aiHandleIndex = formParams.value.pptList.findIndex((item: any) => item.aiCreateFlag)
      if (aiHandleIndex !== -1) {
        proxy.$modal.msgError('AI生成中，请等待完成后再进行保存')
        return
      }
      const params = deepClone(formParams.value);
      params.pptList = params.pptList.map((item: PPTBo, index: number) => {
        if (typeof item.id === "number") {
          return {
            orderNum: index,
            imageId: item.imageId,
            imageUrl: item.imageUrl,
            requirement: item.requirement,
            keywords: item.keywords
          }
        } else {
          return {
            ...item,
            orderNum: index
          };
        }
      })
      btnLoading.value = true;
      await saveOrUpdateScript(params).then((res) => {
        emits('change-type', { flagType: 'list', id: '', listType: 1 })
        proxy.$modal.msgSuccess('保存成功');

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
        moveToError();
      console.log('error submit!!');
      return false;
    }
  })
}

const getProductList = async () => {
  const res = await getAllProduct(2);
  productList.value = res.data
}
const _getChatReportScoringStandard = async () => {
  const res = await getChatReportScoringStandard({ type: formParams.value.type, pageNum: 1, pageSize: 99 });
  scoringStandardArray.value = res.rows
}
const changeType = () => {
  getProductList()
  _getChatReportScoringStandard()
}
changeType()
onMounted(() => {
  if (props.extra) {
    formParams.value.pptList = props.extra.fileList.map((item: any) => {
      return {
        name: item.name,
        imageUrl: item.url,
        imageId: item.ossId,
        id: '',
        requirement: '',
        aiCreateFlag: false
      }
    })
    formParams.value.product = props.extra.product
  }
})
</script>

<style lang="scss" scoped>
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;
  }
}

.box-container {
  // display: flex;
  // flex-direction: column;
  // gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  box-sizing: border-box;
  height: calc(100vh - 84px - 32px - 100px);
  overflow-y: auto;
  width: 100%;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-radio) {
  height: auto;
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.tips {
  color: #999999;
  margin-top: 4px;
  font-size: 14px;
}

.ppt-list {
  &-header {
    margin-bottom: 16px;
  }

  &-head {
    color: #999999;
    font-size: 14px;
    line-height: 22px;
  }

  &-page {
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;

    &-num {
      font-size: 16px;
      line-height: 22px;
      font-weight: bold;
    }

    &-name {
      color: #999999;
      font-size: 12px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
    }
  }

  &-preview {
    width: 202px;
    height: 115px;
    border-radius: 4px;
    border: 1px solid #D9D9D9;

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }

  &-image {
    width: 200px;
    height: 113px;
  }

  &-description {
    height: 115px;

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }

    :deep(.el-textarea__inner) {
      height: 115px !important;
    }
  }

  &-actions {
    width: 210px;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 25px;

    img {
      cursor: pointer;
    }
  }

  &-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;

    &.replace {
      background-image: url("@/assets/images/icon_replace.svg");
    }

    &.replace-disabled {
      background-image: url("@/assets/images/icon_replace_disabled.svg");
    }

    &.up {
      background-image: url("@/assets/images/icon_up.svg");
    }

    &.up-disabled {
      background-image: url("@/assets/images/icon_up_disabled.svg");
    }

    &.down {
      background-image: url("@/assets/images/icon_down.svg");
    }

    &.down-disabled {
      background-image: url("@/assets/images/icon_down_disabled.svg");
    }

    &.delete {
      background-image: url("@/assets/images/icon_delete.svg");
    }

    &.delete-disabled {
      background-image: url("@/assets/images/icon_delete_disabled.svg");
    }
  }
}

.label-tip {
  align-self: center;
  color: #999999;
  vertical-align: middle;
}

.tip-content {
  max-width: 230px;
  // 两端对齐
  text-align: justify;
}

.help {
  width: 16px;
  margin-left: 8px;
}

.backdrop-text {
  position: relative;
  width: 100%;

  :deep(.el-textarea__inner) {
    padding-bottom: 40px;
    height: 250px;
  }
}

.ai {
  background-color: rgb(246, 247, 255);
  color: #4F66FF;
  // padding: 5px;
  position: absolute;
  border-radius: 4px;
  bottom: 10px;
  left: 10px;
  padding: 4px 8px;
  line-height: 20px;
  cursor: pointer;
}

.keyword-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 16px;
}

.info-btn {
  background-color: rgb(246, 247, 255);
  color: #4F66FF;
  // padding: 5px;
  border-radius: 4px;
  padding: 4px 8px;
  line-height: 20px;
  cursor: pointer;
  width: 100px;
  text-align: center;
  font-size: 14px;
}

.word-box {
  word-break: break-all;
}
</style>
