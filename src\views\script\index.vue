<template>
  <component
    :is="componentList.get(currentCom)"
    :id="currentId"
    :is-edit="isEdit"
    @reload="isReload?getList:''"
    v-model:flag="currentType"
    :listType="listType"
    :extra="extra"
    @change-type="handleChangeType"
  />
</template>

<script setup>
import List from './list.vue'
import AddQuestion from './addQuestion.vue'
import AddSkill from './addSkill.vue'
import Detail from './detail.vue'
import DetailSkill from './detailSkill.vue'
import AddLevelGroup from './addLevelGroup.vue'
import EditLevelGroup from './editLevelGroup.vue'
import AddPPT from "./addPPT.vue";
import DetailPPT from "./detailPPT.vue";

/** 当前显示组件切换判断 */
//  组件列表
// const componentList = {
//   List,
//   Add
// }

const componentList = new Map([
  ['list',List],
  ['add-question', AddQuestion],
  ['add-skill', AddSkill],
  ['detail', Detail],
  ['detail-skill', DetailSkill],
  ['add-level-group', AddLevelGroup],
  ['edit-level-group',EditLevelGroup],
  ['add-ppt',AddPPT],
  ['detail-ppt',DetailPPT]
])


const currentCom = ref('list') // 当前组件
const currentId = ref() // 当前行数据id
const currentType = ref()
const isReload = ref()
const listType = ref(null)
const extra = ref(null)

const isEdit=ref(false)
// 监听组件切换事件-改变当前显示
const handleChangeType = payload => {
  currentId.value = payload.id
  currentType.value = payload.flagType
  currentCom.value= payload?.flagType
  isReload.value=payload?.isReload
  isEdit.value=payload?.isEdit
  listType.value=payload?.listType
  extra.value=payload?.extra
}
</script>

<style lang="scss" scoped></style>
