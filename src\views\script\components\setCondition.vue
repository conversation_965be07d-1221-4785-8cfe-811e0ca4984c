<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <template #title>
        {{ title }}
        <div style="font-size: 14px;color: #999999;margin-top: 12px;">当任务脚本满足要求时即可解锁本关卡的脚本</div>
      </template>
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" label-width="120px" v-loading="loading">
          <div class="qa" v-for="(item, index) in formParams.conditionArray" :key="index">
            <el-row :gutter="24">
              <el-col :span="20">
                <el-form-item label="任务脚本" :prop="'conditionArray.' + index + '.scriptId'" :rules="setRules.scriptId">
                  <el-select v-model="item.scriptId" placeholder="请选择">
                    <template #label="{ label }">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(Number(label.slice(0, 1))) }"
                        ></div>
                        <span>{{ label.slice(1, label.length) }}</span>
                      </div>
                    </template>
                    <el-option v-for="it in scriptOptions" :key="it.id" :label="it.status + it.name" :value="it.id">
                      <div class="flex">
                        <div
                          class="dot"
                          style="width: 11px; height: 11px;border-radius: 50%;"
                          :style="{ backgroundColor: colorMap.get(it.status) }"
                        ></div>
                        {{ it.name }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="20">
                <div class="flex">
                  <el-form-item label="任务要求" :prop="'conditionArray.' + index + '.time'" :rules="setRules.time">
                    获得 <el-input v-model="item.time" style="width:80px;margin: 0 16px;" placeholder="请输入" /> 次
                  </el-form-item>
                  <el-form-item style="margin-left: -120px;" :prop="'conditionArray.' + index + '.score'" :rules="setRules.score"
                    ><el-input v-model="item.score" style="width:80px;margin-right: 16px;" placeholder="请输入" /> 分及以上
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" v-loading="btnLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { LevelStageUnlockConditionBo, ScriptDetailVo } from "@/api/script/types";
import { getLevelScriptList, scriptUnlockPermissionCheck} from "@/api/script";
import { ElMessageBox } from 'element-plus'
const emits = defineEmits(['update:visible', 'success'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const scriptOptions = ref<ScriptDetailVo[]>([])
const formParamsRef = ref()
import { deepClone } from "@/utils/index"
const btnLoading = ref(false)
const loading = ref(false)
const colorMap = new Map([
    [2, '#5EEE3A'],
    [1, '#D9D9D9'],
])
const initForm = {
    conditionArray: [{
        time: null,
        score: null,
        scriptId: '',
        scriptName: ''
    }] as unknown as LevelStageUnlockConditionBo[]
}

const oldFromInfo = ref({
    conditionArray: [{
        time: null,
        score: null,
        scriptId: '',
        scriptName: ''
    }] as unknown as LevelStageUnlockConditionBo[]
})



const formParams = ref(initForm)
// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新增'
    },
    currentCondition: {
        type: Array,
        default: () => []
    },
    scriptId: {
        type: String,
        default: ''
    },
})


watch(() => props.visible, val => {
    if (val) {
        _getLevelScriptList()

    }
})



const checkmNum = (rule: any, value: any, callback: any) => {
    if (Number(value) > 100) {
        callback(new Error('最多100分'))
    } else {
        callback()
    }
}

const setRules = reactive({
    scriptId: [
        {
            required: true,
            message: '请选择条件脚本',
            trigger: 'change'
        },
    ],
    time: [
        {
            required: true,
            message: '请输入次数',
            trigger: ['blur']
        },
        {
            pattern: /^[1-9]\d*$/,
            message: "请输入大于0的整数",
            trigger: 'blur'
        },
    ],
    score: [
        { required: true, message: '请输入分数', trigger: 'blur' },
        {
            pattern: /^[1-9]\d*$/,
            message: "请输入大于0的整数",
            trigger: 'blur'
        },
        {
            validator: checkmNum,
            trigger: 'blur'
        },
    ],
})


const _getLevelScriptList = async () => {
    loading.value = true
    const res = await getLevelScriptList();
    loading.value = false
    scriptOptions.value = res.data;
    if (props.currentCondition.length > 0) {
        console.log(props.currentCondition)
        oldFromInfo.value.conditionArray = deepClone(props.currentCondition as unknown as LevelStageUnlockConditionBo[])
        formParams.value.conditionArray = deepClone(props.currentCondition as unknown as LevelStageUnlockConditionBo[])
    }
}

// 弹窗组件显示隐藏
const visibleAdd = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})




const back = () => {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
        if (typeof formParams.value[key] !== 'object') {
            if (
                JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
            ) {
                bool = true;
                return;
            }
        } else {
            if (
                JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
            ) {
                bool = true;
                return;
            }
        }
    });
    if (bool) {
        ElMessageBox.confirm(
            '未保存的内容将丢失',
            '确定要返回吗？',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async () => {
                closeDialog()
            })
            .catch(() => {
            })
    } else {
        closeDialog()
    }
}






const closeDialog = () => {
    loading.value = true
    formParamsRef.value.resetFields()
    formParams.value = initForm
    loading.value = false
    visibleAdd.value = false
}


// //检查权限方法
// const _scriptUnlockPermissionCheck=async(levelStage:LevelStageBo)=>{
//   formLoading.value=true
//   const unlockConditionScriptIds=levelStage.condition?.map(item=>item.scriptId)||[]

//   formLoading.value=false
//   if(!res.data){

//   }
// }

const success=(array:any)=>{
    emits('success', array)
    closeDialog()
}


const handleSubmit = () => {
    proxy.$refs['formParamsRef'].validate(async (valid: any) => {
        if (valid) {
            const array = deepClone(formParams.value.conditionArray)
            array.forEach((element: any) => {
                element.scriptName = scriptOptions.value.find(item => item.id === element.scriptId)!.name
            });

            success(array)

        } else {
            console.log('error submit!!');
            return false;
        }
    });
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;

    .box-title {
        color: #272C47;
        font-size: 20px;
        margin: 18px 0;
        font-weight: bold;

    }
}

.tips {
    color: #999999;
    margin-left: 16px;
}

:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}


.flex {
    display: flex;
    gap: 0 10px;
    align-items: center;
}
</style>
