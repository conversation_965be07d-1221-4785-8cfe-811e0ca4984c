// export interface DeptQuery extends PageQuery {
//   keyword: string;
//   status: number | string | null;
// }

/**
 * DeptVo，部门Vo
 */
export interface DeptVo {
  hasChildren: boolean;
  isLeaf?: boolean;
  /**
   * 祖级列表
   */
  ancestors?: string;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 删除标志（0代表存在 2代表删除）
   */
  delFlag?: string;
  /**
   * 部门名称
   */
  deptName: string;
  /**
   * 部门ID
   */
  id: string;
  /**
   * 显示顺序
   */
  orderNum?: number;
  /**
   * 父部门ID
   */
  parentId: string;
  /**
   * 人数
   */
  userNum?: number;

  externalId?: number;
}

/**
 * TreeLong
 */
export interface TreeLong {
  config?: TreeNodeConfig;
  empty?: boolean;
  id: string;
  name?: Name;
  label?: string;
  parentId?: number;
  weight?: number;
  children?: TreeLong[];
  selected?: boolean;
  externalId?: number;
  disabled?: boolean;
}

/**
 * TreeNodeConfig
 */
export interface TreeNodeConfig {
  childrenKey?: string;
  deep?: number;
  idKey?: string;
  nameKey?: string;
  parentIdKey?: string;
  weightKey?: string;
}
export interface Name {
  empty?: boolean;
}
/**
 * DeptFormVo
 */
export interface DeptFormVo {
  /**
   * 部门名称
   */
  deptName: string;
  /**
   * 父部门ID
   */
  parentId: string | number;
  id?: string;
}
