<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            创建知识库</span
          >
          <div class="right-button">
            <el-button plain type="info" @click="back()">取消</el-button>
            <el-button type="primary" @click="chooseLabelType">保存</el-button>
          </div>
        </div>
      </template>
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="100px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="name" label="名称">
                <el-input v-model="formParams.name" placeholder="请输入名称" />
                <div class="tips">
                  <!-- <img src="@/assets/icons/png/info.png" class="help" /> -->
                  知识库名称仅支持中文、英文、数字、下划线（_）、中划线（-）、英文点（.）（1～50字符）
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="desc" label="描述">
                <el-input
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  :autosize="{ minRows: 2 }"
                  v-model="formParams.description"
                  placeholder="知识库描述文案"
                />
                <!-- <div class="tips">请描述这个知识库的内容和用途</div> -->
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="ossIds" label="上传文件">
                <fileUpload
                  v-model="formParams.ossIds"
                  :fileType="['jpg', 'jpeg', 'png']"
                  title="将图片拖到此处，或点击上传"
                  fileTypeText="支持JPG，JPGE，PNG，单个文件大小不超过10MB。"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <el-dialog v-model="showLabelType" width="800" align-center :show-close="false" title="请选择标注方式">
      <!-- <template #title>
        <div class="choose-type-head"></div>
      </template> -->
      <div class="choose-type-container">
        <div class="choose-type-box" @click="setLabelType(1)" :class="labelType === 1 ? 'checked' : ''">
          <div class="left">
            <div class="choose-type-title">智能标注</div>
            <div class="choose-type-tip" type="info">AI深度理解图片，自动提供详细的内容描述。</div>
          </div>
          <el-icon color="#4f66ff" size="30" v-if="labelType === 1">
            <CircleCheckFilled />
          </el-icon>
        </div>
        <div class="choose-type-box" @click="setLabelType(2)" :class="labelType === 2 ? 'checked' : ''">
          <div class="left">
            <div class="choose-type-title">人工标注</div>
            <div class="choose-type-tip" type="info">不执行处理，在图片导入完成后，手动添加图片描述。</div>
          </div>

          <el-icon color="#4f66ff" size="30" v-if="labelType === 2">
            <CircleCheckFilled />
          </el-icon>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeChooseTypeDialog">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :disabled="labelType === 0" :loading="btnLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import type { FormRules } from 'element-plus'
import { createKnowledgeBase } from '@/api/knowledgeBase';
import { KnowledgeBaseBo } from '@/api/knowledgeBase/types';
const emits = defineEmits(['change-type'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const btnLoading = ref(false)
const showLabelType = ref(false)
const labelType = ref<number>(0)

const formParams = ref<KnowledgeBaseBo>({
  name: '',
  description: '',
  ossIds: '',
  type: 0
})
const rules = reactive<FormRules<KnowledgeBaseBo>>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]+$/,
      message: "仅支持中文、英文、数字、下划线（_）、中划线（-）、英文点（.）",
      trigger: 'blur'
    },
    {
      min: 1,
      max: 50,
      message: '最多50字符',
      trigger: ["blur"]
    },
  ],
  ossIds: [
    { required: true, message: '图片不能为空', trigger: ['blur', 'change'] },
  ],
})

watch(() => formParams.value.ossIds, () => {
  proxy.$refs['formParamsRef'].validateField('ossIds')
})


/**返回校验 */
const back = () => {
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]' &&
      JSON.stringify(formParams.value[key]) !== '0'
    ) {
      bool = true;
      return;
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', })
  }
}

const noOpen = () => {
  ElMessage.info('敬请期待');
}
const setLabelType = (type: number) => {
  labelType.value = type
  // ElMessage.info('敬请期待');

}

const chooseLabelType = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      showLabelType.value = true
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
const closeChooseTypeDialog = () => {
  showLabelType.value = false
}

/**提交 */
const handleSubmit = async () => {
  const params: any = {
    ...formParams.value,
    type: 2,
    labelType: labelType.value
  };
  params.ossIds = params.ossIds.map((item: any) => item.ossId)
  console.log(params)
  await createKnowledgeBase(params).then((res) => {
    emits('change-type', { flagType: 'item', id: res.data, rowName: params.name, initFlag: labelType.value === 2 })
    proxy.$modal.msgSuccess('保存成功');
  }).finally(() => {
    btnLoading.value = false;
  });
}
</script>
<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.choose-type-container {
  display: flex;
  flex-direction: column;
  gap: 24px 0;
}

.choose-type-box {
  padding: 24px 36px;
  border: 1px solid #c1ced6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .left {}
}

.choose-type-title {
  font-size: 16px;
  font-weight: bold;
}

.choose-type-tip {
  margin-top: 18px;
  color: #919090;
}

.checked {
  border: 1px solid #4f66ff !important;
}

.right-button {
  // margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;
  }
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-start;
  margin: 0 auto;
  // height: 100%;
  // overflow-y: auto;
  width: 100%;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

:deep(.el-card) {
  .el-card__body {
    height: calc(100vh - 84px - 32px - 100px);
    overflow-y: auto;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-form) {
  width: 100%;
}

.tips {
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
  margin-top: 10px;
}
</style>
