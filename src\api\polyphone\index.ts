import request from '@/utils/request';
import { AxiosPromise } from 'axios';

// 查询脚本列表
export function getPolyphoneRecord(): AxiosPromise<any[]> {
  return request({
    url: '/biz/polyphoneRecord/list',
    method: 'get'
  });
}

// 查询多音字
export function getToneMap(content: string): AxiosPromise<any> {
  return request({
    url: '/biz/polyphoneRecord/getToneMap',
    method: 'get',
    params: {
      content: content
    }
  });
}
export function addPolyphoneRecord(data: any): AxiosPromise<any> {
  return request({
    url: '/biz/polyphoneRecord/saveOrUpdate',
    method: 'post',
    data: data
  });
}
export function removePolyphoneRecord(id: any): AxiosPromise<any> {
  return request({
    url: '/biz/polyphoneRecord/remove?id=' + id,
    method: 'post'
  });
}
