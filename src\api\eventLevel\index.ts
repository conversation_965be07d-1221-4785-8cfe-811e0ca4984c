import request from '@/utils/request';
import { ScriptDetailVo, LevelVo, LevelFormBo, EventLevelStageUserVo, EventLevelStageDetailVo, UserChatListVo } from './types';
import { AxiosPromise } from 'axios';

/**获取闯关列表 */
export function getLevelList(query: PageQuery): AxiosPromise<LevelVo[]> {
  return request({
    url: '/biz/eventLevel/list',
    method: 'get',
    params: query
  });
}
/**关卡脚本列表 */
export function getLevelScriptList(): AxiosPromise<ScriptDetailVo[]> {
  return request({
    url: '/biz/script/levelScriptList',
    method: 'get'
  });
}

/**添加关卡 */
export function addLevelScriptGroup(data: LevelFormBo): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/add',
    method: 'post',
    data
  });
}

/**关卡组脚本重复检查 */
export function scriptLevelDuplicateCheck(query: { scriptId: string; levelId: string | null }): AxiosPromise<boolean> {
  return request({
    url: '/biz/level/scriptLevelDuplicateCheck',
    method: 'get',
    params: query
  });
}
/**上线关卡 */
export function onlineLevelScriptGroup(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/online',
    method: 'post',
    params: query
  });
}
/**下线关卡 */
export function offlineLevelScriptGroup(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/offline',
    method: 'post',
    params: query
  });
}

/**获取闯关未上线脚本 */
export function getOfflineScript(query: { levelId: string }): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/getOfflineScript/' + query.levelId,
    method: 'get'
  });
}

/**删除 */
export function deleteLevel(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/delete?id=' + id,
    method: 'post'
  });
}
/**脚本解锁权限检查 */
export function scriptUnlockPermissionCheck(query: { scriptId: string; unlockConditionScriptIds: string[] | string }): AxiosPromise<any> {
  query.unlockConditionScriptIds = (query.unlockConditionScriptIds as string[]).join(',');
  return request({
    url: '/biz/level/scriptUnlockPermissionCheck',
    method: 'get',
    params: query
  });
}
/**详情 */
export function getLevelDetail(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/detail/' + id,
    method: 'get'
  });
}
/**修改关卡 */
export function updateLevelScriptGroup(data: LevelFormBo): AxiosPromise<any> {
  return request({
    url: '/biz/eventLevel/update',
    method: 'post',
    data
  });
}
/*判断脚本是否在闯关中 */
export function checkScriptInLevel(query: { scriptId: string }): AxiosPromise<any> {
  return request({
    url: '/biz/level/checkScriptInLevel',
    method: 'get',
    params: query
  });
}

/*详情 */
export function eventLevelStageDetail(id: string): AxiosPromise<EventLevelStageDetailVo> {
  return request({
    url: '/biz/eventLevelStage/detail/' + id,
    method: 'get'
  });
}
/*详情用户列表 */
export function eventLevelStageDetailUser(query: { id: string; pageNum: number; pageSize: number }): AxiosPromise<EventLevelStageUserVo[]> {
  return request({
    url: `/biz/eventLevelStage/detail/${query.id}/user`,
    method: 'get',
    params: {
      pageNum: query.pageNum,
      pageSize: query.pageSize
    }
  });
}
/*闯关用户聊天列表 */
export function eventLevelStageUserChatDetail(
  id: string,
  userId: string,
  params: { pageNum: number; pageSize: number }
): AxiosPromise<UserChatListVo[]> {
  return request({
    url: `/biz/eventLevelStage/detail/${id}/user-chat/${userId}`,
    method: 'get',
    params
  });
}
