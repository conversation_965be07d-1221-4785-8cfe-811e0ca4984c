<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex" v-if="editFlag">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            编辑E人</span
          >
          <span class="card-title flex" v-else>
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            E人详情</span
          >
          <div class="right-button" v-if="editFlag">
            <el-button plain type="info" @click="props.isEdit ? back() : checkSave()"> 取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
          <div class="right-button" v-else>
            <el-button type="primary" plain @click="emits('change-type', { flagType: 'list', id: '', listType: listType })">返回</el-button>
            <el-button type="primary" @click="editFlag = true">编辑</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" v-if="editFlag" ref="scrollDiv">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="avatar">
                <template #label>
                  <span class="label-text">头像</span>
                </template>

                <div class="avatar-img" v-if="formParams.avatar" @click="initAvatar">
                  <el-image :src="formParams.avatar" class="avatar" />
                  <img class="tips-icon" src="@/assets/icons/svg/3d.svg" v-show="formParams.zipFileUrl !== ''&&formParams.show3dFlag" />
                </div>
                <div class="avatar-empty" v-else @click="initAvatar"><el-image :src="avatar" class="empty" /></div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="name">
                <template #label>
                  <span class="label-text">昵称</span>
                </template>
                <el-input v-model="formParams.name" placeholder="请输入">
                  <template #append>
                    <img src="@/assets/icons/png/refresh.png" @click="_getPersonName" style="cursor: pointer;" class="icon" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="occupation">
                <template #label>
                  <span class="label-text">职业</span>
                </template>
                <el-select clearable v-model="formParams.occupation" placeholder="请选择" @change="occupationChange">
                  <el-option v-for="item in totalOccupationList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24" v-if="formParams.occupation && departmentReactive.show">
            <el-col :span="16">
              <el-form-item :label="departmentReactive.label" prop="department">
                <el-cascader
                  v-model="formParams.department"
                  :options="departmentList"
                  :props="{
                  value: 'name',
                  label: 'name',
                  children: 'subName',
                }"
                  clearable
                  filterable
                  @change="departmentChange"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24" v-show="titleFlag">
            <el-col :span="16">
              <el-form-item prop="title">
                <template #label>
                  <span class="label-text">{{ titleFlag === 'title' ? '职称' : (titleFlag == 'level' ? '级别' : '')
                  }}</span>
                </template>
                <el-select v-if="titleFlag === 'title'" clearable filterable v-model="formParams.title" placeholder="请选择">
                  <el-option v-for="item in titleList" :key="item" :label="item.split('-')[1]" :value="item" />
                </el-select>
                <el-select v-if="titleFlag === 'level'" clearable filterable v-model="formParams.title" placeholder="请选择">
                  <el-option v-for="item in levelList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="性格" prop="personality">
                <el-select
                  clearable
                  default-first-option
                  allow-create
                  :reserve-keyword="false"
                  v-model="formParams.personality"
                  filterable
                  multiple
                  placeholder="请选择或者输入"
                >
                  <el-option v-for="item in personNatureList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="声音" prop="toneName">
                <el-input @click="showVoice = true" v-model="formParams.toneName" class="input" readonly placeholder="请选择">
                  <template v-slot:suffix>
                    <el-icon @click="showVoice = true">
                      <ArrowDown style="cursor:pointer;" />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="skill">
                <template #label>
                  <span class="label-text">擅长方向</span>
                </template>
                <el-input
                  v-model="formParams.skill"
                  type="textarea"
                  :autosize="{ minRows: 5 }"
                  placeholder="如：睑腺炎、睑板腺囊肿、泪囊炎、近视、倒睫、泪道阻塞、结膜炎、干眼、眨眼、飞蚊症、结膜肿物、眼脸肿物"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24" v-if="formParams.type === 2">
            <el-col :span="16">
              <el-form-item prop="kbIds">
                <template #label>
                  <span class="label-text">知识库</span>
                </template>
                <choose-knowledge v-model="formParams.kbIds" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="formParams.type === 2">
            <el-col :span="16">
              <el-form-item prop="responseType">
                <template #label>
                  <span class="label-text flex">无答案回复</span>
                  <el-tooltip content="当知识库没有找到答案时E人的回复话术" placement="top" popper-class="popper">
                    <el-icon class="label-tip" style=" margin-left: 4px;">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px; " />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="formParams.responseType" style="display: flex;">
                  <el-radio :value="0" :label="0">
                    <div class="flex gap4">
                      <span>默认</span>
                      <el-tooltip
                        content="E人将参考回复以下内容“知识库中没有找到这个问题的答案，建议换个问题或者表述方式后重试。”"
                        placement="top"
                        popper-class="popper"
                      >
                        <el-icon class="label-tip"> <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;" /> </el-icon>
                      </el-tooltip>
                    </div>
                  </el-radio>
                  <el-radio :value="1" :label="1">
                    <div class="flex gap4">
                      <span>AI回复</span>
                      <el-tooltip content="E人回复的内容将由AI自己的数据生成。" placement="top" popper-class="popper">
                        <el-icon class="label-tip">
                          <img src="@/assets/icons/svg/help1.svg" style="width: 16px;" />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="responseType">
                <template #label>
                  <span class="label-text flex">开场白</span>
                  <el-tooltip
                    content="开启后，E人将在对话开始后主动进行开场，如果设置了多条开场白，将随机一条进行开场。"
                    placement="top"
                    popper-class="popper"
                  >
                    <el-icon class="label-tip" style="margin-left: 4px;">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="formParams.introductionType" style="display: flex;">
                  <el-radio :value="0" :label="0">
                    <div class="flex gap4">
                      <span>关闭</span>
                    </div>
                  </el-radio>
                  <el-radio :value="1" :label="0">
                    <div class="flex gap4">
                      <span>立刻开场</span>
                      <el-tooltip content="用户开始对话后，E人立即说出开场白。" placement="top" popper-class="popper">
                        <el-icon class="label-tip">
                          <img src="@/assets/icons/svg/help1.svg" style=" width: 16px; " />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </el-radio>
                  <el-radio :value="2" :label="1">
                    <div class="flex gap4">
                      <span>延迟开场</span>
                      <el-tooltip content="用户开始对话后，如果用户若干秒内未开始进行对话，E人将说出开场白" placement="top" popper-class="popper">
                        <el-icon class="label-tip">
                          <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;" />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="formParams.introductionType === 2" style="margin-bottom: 24px;">
            <el-col :span="24">
              <div class="introduction-container">
                <div class="flex" style="gap: 0 16px;width: 100%;">
                  <el-form-item prop="introductionDelay" class="introduction-dellay">
                    <div class="flex">
                      延迟
                      <el-select v-model="formParams.introductionDelay" style="width: 60px;">
                        <el-option v-for="item in 6" :key="item + 4" :label="item + 4" :value="item + 4" />
                      </el-select>
                      秒
                    </div>
                    <div class="tips" v-if="formParams.introductionDelay">
                      如果用户{{ formParams.introductionDelay }}s内未进行对话，E人将说出开场白；否则不会出现开场白。
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="introduction-content-list" v-if="formParams.introductionType !== 0">
            <el-row :gutter="24" class="flex" v-for="(item, index) in formParams.introductionContentList" :key="index">
              <el-col :span="20" style="margin-bottom: 18px;">
                <el-form-item :prop="'introductionContentList.' + index + '.content'" class="content" :rules="setRules.content">
                  <el-input type="textarea" placeholder="请输入" resize="none" v-model="item.content" :autosize="{ minRows: 1, maxRows: 2 }" />
                  <div class="font-limit">{{ item.content.length + '/100' }}</div>
                </el-form-item>
              </el-col>
              <div class="flex">
                <div
                  class="intro-icon"
                  color="#4F66FF"
                  @click="addIntroductionContent"
                  v-if="index === (formParams.introductionContentList.length - 1)"
                >
                  <el-icon>
                    <Plus />
                  </el-icon>
                </div>
                <div
                  class="intro-icon"
                  @click="removeIntroductionContent(index)"
                  v-if="!(index === 0 && formParams.introductionContentList.length === 1)"
                >
                  <el-icon color="#4F66FF">
                    <Minus />
                  </el-icon>
                </div>
              </div>
            </el-row>
            <div class="button" id="bottom">
              <el-button type="primary" @click="showIntroductionDialog = true">快速导入</el-button>
            </div>
          </div>
          <el-row :gutter="24" v-if="company === 'kangyuan' && oldFromInfo.type === 1">
            <el-col :span="16">
              <el-form-item prop="emanDifficult">
                <template #label>
                  <span class="label-text flex">拜访难度</span>
                  <el-tooltip content="可以标记E人对话的难易程度，设置后将在小程序展示E人的星级标记等级。" placement="top" popper-class="popper">
                    <el-icon class="label-tip" style="margin-left: 4px;">
                      <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <Star v-model="formParams.emanDifficult" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="box-container" v-else>
        <div class="script-msg">
          <div class="left">头像</div>
          <div class="right">
            <div class="avatar-img">
              <el-image :src="oldFromInfo.avatar" class="avatar" fit="cover" lazy />
              <img class="tips-icon" src="@/assets/icons/svg/3d.svg" v-show="formParams.zipFileUrl !== ''&&formParams.show3dFlag" />
            </div>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">昵称</div>
          <div class="right">
            <span>{{ oldFromInfo.name }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">职业</div>
          <div class="right">
            <span>{{ oldFromInfo.occupation }}</span>
          </div>
        </div>
        <div class="script-msg" v-if="departmentReactive.show">
          <div class="left">{{ departmentReactive.label }}</div>
          <div class="right">
            <span>{{ oldFromInfo.department }}</span>
          </div>
        </div>
        <div class="script-msg" v-if="oldFromInfo.title">
          <div class="left">
            {{ titleFlag === 'title' ? '职称' : (titleFlag == 'level' ? '级别' : '')
            }}
          </div>
          <div class="right">
            <span>{{ oldFromInfo.title }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">性格</div>
          <div class="right">
            <span>{{ oldFromInfo.personality }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">声音</div>
          <div class="right">
            <span>{{ oldFromInfo.tone ? JSON.parse(oldFromInfo.tone).name : '' }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">擅长方向</div>
          <div class="right">
            <span>{{ oldFromInfo.skill }}</span>
          </div>
        </div>
        <div class="script-msg" v-if="oldFromInfo.type === 2">
          <div class="left">知识库</div>
          <div class="right">
            <div class="chosen-list" v-if="oldFromInfo.kbList?.length > 0">
              <div class="chosen-list-item" v-for="item in oldFromInfo.kbList" :key="item.kbId">
                <img src="@/assets/icons/png/file.png" v-if="item.type === 1" />
                <img src="@/assets/icons/png/fileimg.png" v-if="item.type === 2" />
                <span class="chosen-list-name">{{ item.kbName }}</span>
              </div>
            </div>
            <div v-else>无</div>
          </div>
        </div>

        <div class="script-msg" v-if="oldFromInfo.type === 2">
          <div class="left">无答案回复</div>
          <div class="right">{{ oldFromInfo.responseType === 0 ? '默认' : ' AI回复' }}</div>
        </div>
        <div class="script-msg">
          <div class="left">开场白</div>
          <div class="right">
            <div class="flex">
              {{ introMap.get(oldFromInfo.introductionType) }}
              <span class="tips" v-if="oldFromInfo.introductionType === 2">
                如果用户{{ oldFromInfo.introductionDelay }}s内未进行对话，E人将说出开场白；否则不会出现开场白。
              </span>
            </div>
            <div v-if="oldFromInfo.introductionType!==0">
              <el-row style="margin-top: 10px;" :gutter="24" v-for="(item, index) in oldFromInfo.introductionContentList" :key="index">
                <el-col :span="24">
                  <div class="contentText">{{ item.content }}</div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="script-msg" v-if="company === 'kangyuan' && oldFromInfo.type === 1">
          <div class="left">拜访难度</div>
          <div class="right">
            <Star readonly v-model="oldFromInfo.emanDifficult" />
          </div>
        </div>
        <div class="script-msg">
          <div class="left">类型</div>
          <div class="right">
            <span>{{ oldFromInfo.type === 2 ? '企业智脑' : '情景演练' }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">创建时间</div>
          <div class="right">
            <span>{{ oldFromInfo.createTime }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">创建者</div>
          <div class="right">
            <span>{{ oldFromInfo.creator }}</span>
          </div>
        </div>
      </div>
      <avatar-dialog
        title="选择头像"
        v-model:visible="showAvatar"
        v-model:gender="formParams.gender"
        @avatarSuccess="setAvatar"
        :tone="formParams.tone"
        :currentAvatar="currentAvatar"
      ></avatar-dialog>
      <voice-dialog
        title="选择声音"
        v-model:visible="showVoice"
        :avatar="formParams.avatar"
        v-model:gender="formParams.gender"
        @toneSuccess="setVoice"
      ></voice-dialog>
      <el-dialog title="快速导入" v-model="showIntroductionDialog" width="700" align-center :show-close="false">
        <template #title>
          快速导入
          <div class="title-tips">多个开场白之间请用换行隔开</div>
        </template>
        <el-input
          v-model="introductionContentString"
          type="textarea"
          :autosize="{ minRows: 10, maxRows: 10 }"
          placeholder="请输入"
          style="width: 100%;height: calc( 100% - 20px)"
        ></el-input>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="checkCloseDialog">关 闭</el-button>
            <el-button type="primary" @click="parseIntroductionContent">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="AddEMan" lang="ts">
import { reactive, ref } from 'vue'
import { getDepartmentList } from '@/api/script'
import { getEmanDetail, updateEman } from "@/api/eman";
import { EmanFormVo } from "@/api/eman/types";
import { ElMessageBox, ElLoading } from 'element-plus'
import avatar from '@/assets/icons/png/avatar1.png'
import avatarDialog, { avatarObjVo } from './components/avatarDialog.vue'
import voiceDialog from './components/voiceDialog.vue'
import type { FormRules } from 'element-plus'
import { getPersonNature, getPersonName, getProfessionalList } from "@/api/eman/common";
import { occupationList, occupationIntelligenceList, occupationOtherList, shopLevelList, convertArrayToObject, levelSellerList } from './components/data';
import { deepClone } from "@/utils/index";
import ChooseKnowledge from './components/chooseKnowledge.vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import { ArrowDown } from '@element-plus/icons-vue'
import Star from "@/components/Star/index.vue";
import useUserStore from "@/store/modules/user";
const emits = defineEmits(['change-type'])
const company = import.meta.env.VITE_APP_COMPANY;
const btnLoading = ref(false)
const userStore = useUserStore();
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  listType: {
    type: Number
  }
})
const editFlag = ref(props.isEdit)
const titleFlag = ref('') // level or title
const scrollDiv = ref()

const personNatureList = ref<any[]>(); //性格
const departmentList = ref<any[]>(); //科室
const titleList = ref<any[]>(); //职称
const titleAllList = ref<any[]>(); //总职称
const levelList = ref<string[]>([]); // 级别
  const currentAvatar=ref<avatarObjVo>({} as avatarObjVo)

const formParams = ref<EmanFormVo>({
  id: '',
  name: '',
  avatar: '',
  gender: 0,
  background: '',
  personality: [],
  tone: null,
  toneName: '',
  skill: '',
  occupation: '',
  zipFileUrl: '',
  videoUrl: '',
  department: [],
  title: [],
  type: null,
  responseType: 0,
  kbIds: null,
  introductionType: 0,
  introductionDelay: 5,
  introductionContentList: [{ content: '' }],
  introductionList: [],
  emanDifficult: undefined,
  show3dFlag:false
})
const oldFromInfo = ref<EmanFormVo>({} as EmanFormVo)
const showAvatar = ref(false)
const showVoice = ref(false)
const introductionContentString = ref('')
const showIntroductionDialog = ref(false)
const introMap = new Map([
  [0, '关闭'],
  [1, '立刻开场'],
  [2, '延迟开场']
])

const totalOccupationList = ref<string[]>([])

const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 200) {
    callback(new Error(`字数太多啦${value.length}/200`))
  } else {
    callback()
  }
}




// 定义自定义规则函数
const checkArrayLength = (rule: any, value: any, callback: any) => {
  if (value.length < 1) {
    callback(new Error('至少选择1个标签'));
  } else if (value.length > 3) {
    callback(new Error('最多选择3个标签'));
  } else {
    callback();
  }
};
// 定义自定义规则函数
const checkDelayNumber = (rule: any, value: any, callback: any) => {
  if (value < 5 || value > 10) {
    callback(new Error('延迟时间必须在5-10之间'))
  } else {
    callback()
  }
}

const checkDepartment = (rule: any, value: any, callback: any) => {
  switch (formParams.value.occupation) {
    case totalOccupationList.value[0]:
    case totalOccupationList.value[1]:
    case totalOccupationList.value[2]:
      if (!value || value.length === 0) {
        callback(new Error('请选择科室'))
      } else {
        callback();
      }
      break;
    default:
      callback();
  }
}

const checkTitle = (rule: any, value: any, callback: any) => {
  console.log(formParams.value.occupation)
  switch (formParams.value.occupation) {
    case totalOccupationList.value[0]:
    case totalOccupationList.value[1]:
    case totalOccupationList.value[2]:
    case totalOccupationList.value[3]:
      if (!value || value.length === 0) {
        callback(new Error('请选择职称'))
      } else {
        callback();
      }
      break;
    case totalOccupationList.value[4]:
    case totalOccupationList.value[6]:
      if (!value) {
        callback(new Error('请选择级别'))
      } else {
        callback();
      }
      break;
    default:
      callback();
  }
}
const setRules = reactive({
  content: [
    {
      required: true,
      message: '请输入开场白',
      trigger: ['blur']
    },
    {
      min: 1,
      max: 100,
      message: '字数太多啦',
      trigger: ['blur']
    },
  ],
})


const rules = reactive<FormRules<EmanFormVo>>({
  avatar: [
    { required: true, message: '请选择头像', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 12, message: '支持2-12个字，不要使用数字或特殊符号', trigger: 'blur' },
    {
      pattern: /^[A-Za-z\u4e00-\u9fa5]+$/,
      message: "支持2-12个字，不要使用数字或特殊符号",
      trigger: 'blur'
    },
  ],
  skill: [
    { required: true, message: '请输入擅长方向', trigger: 'blur' },
    {
      validator: checkFontLengthLimit,
      trigger: 'blur'
    },
  ],
  occupation: [
    { required: true, message: '请选择职业', trigger: ["blur", "change"] },
  ],
  title: [
    { required: true, validator: checkTitle, trigger: ["blur", "change"] },
  ],
  toneName: [
    { required: true, message: '请选择声音', trigger: ["blur", "change"] },
  ],
  introductionDelay: [
    { required: true, message: '请输入延迟时间', trigger: ["blur"] },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于5小于10的整数",
      trigger: ['blur']
    },
    {
      validator: checkDelayNumber,
      trigger: 'blur'
    },

  ],
  personality: [
    { required: true, message: '请选择或者输入性格', trigger: ["change"] },
    {
      validator: checkArrayLength,
      trigger: ["change"]
    },
  ],
  department: [{ required: true, validator: checkDepartment, trigger: ["change"] }],
})

const departmentReactive = reactive({
  label: '',
  show: false,
  toggleSelect: 0, // 1医疗卫生专业人士 2药店职员 3药企高管
  lastDepartment: convertArrayToObject(occupationList),
  lastLevel: '',
  lastExecutiveLevel: ''
})




/**头像赋值 */
const setAvatar = (val: { avatar: string, background: string; zipFileUrl: string; videoUrl: string; show3dFlag: boolean; }, resetFlag: boolean) => {
  formParams.value.avatar = val.avatar
  formParams.value.background = val.background
  formParams.value.zipFileUrl = val.zipFileUrl || ''
  formParams.value.videoUrl = val.videoUrl || ''
  formParams.value.show3dFlag = val.show3dFlag || false
  //重置语音
  if (resetFlag) {
    formParams.value.toneName = ''
    formParams.value.tone = null
  }
  proxy.$refs['formParamsRef']?.clearValidate('avatar')
}
/**声音赋值 */
const setVoice = (val: any, resetFlag: boolean) => {
  formParams.value.toneName = val.name
  formParams.value.tone = val
  if (resetFlag) {
    formParams.value.avatar = ''
    formParams.value.background = ''
    formParams.value.zipFileUrl = ''
    formParams.value.videoUrl = ''
  }
}


const initAvatar = () => {
  showAvatar.value = true
  currentAvatar.value = {
    avatar: formParams.value.avatar,
    background: formParams.value.background,
    zipFileUrl: formParams.value.zipFileUrl,
    videoUrl: formParams.value.videoUrl,
    show3dFlag: formParams.value.show3dFlag as boolean
  }
}


/**提交 */
const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
      };
      params.introductionList = formParams.value.introductionContentList.map((item: any) => {
        return item.content
      })
      params.type = 1
      if (typeof formParams.value.title !== 'string') {
        params.title = (formParams.value.title as string[]).join('-');
      }
      params.personality = (formParams.value.personality as string[]).join(',');
      params.tone = JSON.stringify(formParams.value.tone)
      if (formParams.value.department) {
        params.department = (formParams.value.department as string[]).length > 0 ? (formParams.value.department as string[]).join('-') : null
      }
      params.kbIds = formParams.value.kbIds?.map((item: any) => item.id)
      await updateEman(params).then(() => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
        proxy.$modal.msgSuccess('保存成功');
      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!', valid);
      return false;
    }
  });
}

const addIntroductionContent = () => {
  if (formParams.value.introductionContentList[formParams.value.introductionContentList.length - 1].content === '') {
    ElMessage.error('请输入开场白内容');
    return
  }
  formParams.value.introductionContentList.push({ content: '' })
  // 滚动到底部
  nextTick(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
  });
}

const removeIntroductionContent = (index: any) => {
  formParams.value.introductionContentList.splice(index, 1)
  formParams.value.introductionContentList.forEach((element, index) => {
    proxy.$refs['formParamsRef'].validateField('introductionContentList.' + index + '.content')
  });
}
const parseIntroductionContent = () => {
  const array = introductionContentString.value.split('\n')
  const newArray = array.map((item: any) => {
    return { content: item }
  })
  const allArray = [...formParams.value.introductionContentList, ...newArray]
  // 去除空数组
  const arr = allArray.filter(function (item) {
    return item.content !== "";
  });
  formParams.value.introductionContentList = [...arr]
  introductionContentString.value = ''
  showIntroductionDialog.value = false
  nextTick(() => {
    // const element = document.querySelector('#bottom');
    // element?.scrollIntoView({ behavior: "smooth" });
    setTimeout(() => {
      let scrollElem = scrollDiv.value;
      scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
      formParams.value.introductionContentList.forEach((element, index) => {
        proxy.$refs['formParamsRef'].validateField('introductionContentList.' + index + '.content')
      });
    }, 100);
  });
}

const checkCloseDialog = () => {
  if (introductionContentString.value !== '') {

    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        introductionContentString.value = ''
        showIntroductionDialog.value = false
      })
      .catch(() => {
      })
  } else {
    showIntroductionDialog.value = false

  }
}

const handleDepartment = (data: any) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].subName && data[i].subName.length > 0) {
      data[i].subName = data[i].subName.map((item: string) => ({ name: item }))
    }
  }
}
// const handleProfessionalTitle = (data: any) => {
//   for (let i = 0; i < data.length; i++) {
//     if (data[i].subTitle && data[i].subTitle.length > 0) {
//       data[i].subTitle = data[i].subTitle.map((item: string) => ({ title: item }))
//     }
//   }
// }

const _getPersonName = async () => {
  const res: any = await getPersonName();
  formParams.value.name = res.msg
  proxy.$refs['formParamsRef'].clearValidate('name')
}

/**部门等 */
const requestDepartmentList = async () => {
  const res: any = await getDepartmentList();
  handleDepartment(res.data)
  departmentList.value = res.data
}
//性格
const _getPersonNature = async () => {
  const res: any = await getPersonNature();
  personNatureList.value = res.data
}
//职称
const _getProfessionalList = async () => {
  const res: any = await getProfessionalList();
  titleAllList.value = res.data
}

// 选择类型
const getData = () => {
  requestDepartmentList()
  _getPersonNature()
  _getProfessionalList()
}


const departmentChange = (val: string) => {
  departmentReactive.lastDepartment[formParams.value.occupation] = val
}

const occupationChange = (val: string) => {
  let label = '';
  switch (val) {
    case totalOccupationList.value[0]:
    case totalOccupationList.value[1]:
    case totalOccupationList.value[2]:
    case totalOccupationList.value[3]:
      levelList.value = []
      label = '科室'
      departmentReactive.toggleSelect = 1
      departmentReactive.show = true
      titleFlag.value = 'title'
      formParams.value.title = ''
      if (departmentReactive.lastDepartment[val]) {
        formParams.value.department = departmentReactive.lastDepartment[val]
      } else {
        formParams.value.department = []
      }
      filterTitleList(val)
      proxy.$refs['formParamsRef']?.clearValidate('title')
      proxy.$refs['formParamsRef']?.clearValidate('department')
      break;
    case occupationList[4]:
      departmentReactive.show = false
      titleFlag.value = 'level'
      formParams.value.title = ''
      proxy.$refs['formParamsRef']?.clearValidate('title')
      break;
    case totalOccupationList.value[6]:
      departmentReactive.show = false
      titleFlag.value = 'level'
      formParams.value.title = ''
      levelList.value = levelSellerList
      proxy.$refs['formParamsRef'].clearValidate('title')
      break;
    default:
      departmentReactive.show = false
      titleFlag.value = ''
      formParams.value.title = ''
      levelList.value = []
      proxy.$refs['formParamsRef'].clearValidate('title')
      proxy.$refs['formParamsRef'].clearValidate('department')
  }
  departmentReactive.label = label
}



/* const initOccupationChange = (val: string) => {
  let label = '';
  switch (val) {
    case totalOccupationList.value[0]:
    case totalOccupationList.value[1]:
    case totalOccupationList.value[2]:
    case totalOccupationList.value[3]:
      label = '科室'
      departmentReactive.toggleSelect = 1
      departmentReactive.show = true
      if (departmentReactive.lastDepartment[val]) {
        formParams.value.department = departmentReactive.lastDepartment[val]
      } else {
        formParams.value.department = []
      }
      filterTitleList(val)
      proxy.$refs['formParamsRef']?.clearValidate('title')
      proxy.$refs['formParamsRef']?.clearValidate('department')
      break;
    case totalOccupationList.value[4]:
      console.log(123)
      departmentReactive.show = false
      proxy.$refs['formParamsRef']?.clearValidate('title')
      break;
  }
  departmentReactive.label = label
} */
const filterTitleList = (val: string) => {
  titleList.value = titleAllList.value?.find(item => item.title === val).subTitle
}

/**详情 */
const _getEManDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
  })

  const res = await getEmanDetail(props.id);
  loading.close()
  res.data.toneName = JSON.parse(res.data.tone).name
  res.data.introductionList = res.data.introductionList || []
  res.data.introductionContentList = res.data.introductionList.map((item: any) => {
    return { content: item }
  })
  res.data.videoUrl = res.data.videoUrl || ''
  formParams.value = {
    id: res.data.id,
    name: res.data.name,
    avatar: res.data.avatar,
    zipFileUrl: res.data.zipFileUrl || '',
    videoUrl: res.data.videoUrl,
    gender: res.data.gender,
    responseType: res.data.responseType,
    background: res.data.background,
    personality: res.data.personality.split(','),
    tone: JSON.parse(res.data.tone),
    type: res.data.type,
    toneName: JSON.parse(res.data.tone).name,
    skill: res.data.skill,
    occupation: res.data.occupation,
    title: '',
    introductionType: res.data.introductionType,
    introductionDelay: res.data.introductionDelay || 5,
    introductionList: res.data.introductionList || [],
    introductionContentList: res.data.introductionContentList.length === 0 ? [{ content: '' }] : res.data.introductionContentList || [],
    department: [],
    kbIds: res.data.kbList.map((item: any) => ({ id: item.kbId, name: item.kbName, type: item.type })),
    emanDifficult: res.data.emanDifficult,
    show3dFlag: res.data.show3dFlag
  }

  if (res.data.department) {
    formParams.value.department = res.data.department.split('-')
    departmentReactive.lastDepartment[formParams.value.occupation] = formParams.value.department
  }
  if (res.data.type === 2) {
    totalOccupationList.value = occupationList.concat(occupationIntelligenceList);
  } else {
    totalOccupationList.value = occupationList.concat(occupationOtherList);
  }
  let label = '';
  switch (formParams.value.occupation) {
    case totalOccupationList.value[0]:
    case totalOccupationList.value[1]:
    case totalOccupationList.value[2]:
    case totalOccupationList.value[3]:
      formParams.value.title = res.data.title
      titleFlag.value = 'title'
      label = '科室'
      departmentReactive.toggleSelect = 1
      departmentReactive.show = true
      if (departmentReactive.lastDepartment[formParams.value.occupation]) {
        formParams.value.department = departmentReactive.lastDepartment[formParams.value.occupation]
      } else {
        formParams.value.department = []
      }
      filterTitleList(formParams.value.occupation)
      proxy.$refs['formParamsRef']?.clearValidate('title')
      proxy.$refs['formParamsRef']?.clearValidate('department')
      break;
    case totalOccupationList.value[4]:
      formParams.value.title = res.data.title
      titleFlag.value = 'level'
      departmentReactive.show = false
      levelList.value = shopLevelList
      proxy.$refs['formParamsRef']?.clearValidate('title')
      break;
    case totalOccupationList.value[6]:
      formParams.value.title = res.data.title
      titleFlag.value = 'level'
      departmentReactive.show = false
      levelList.value = levelSellerList
      proxy.$refs['formParamsRef']?.clearValidate('title')
      break;
    default:
      departmentReactive.show = false
      titleFlag.value = ''
      // proxy.$refs['formParamsRef'].clearValidate('title')
      // proxy.$refs['formParamsRef'].clearValidate('department')
      break;
  }
  departmentReactive.label = label

  oldFromInfo.value = deepClone(res.data);

}
/**返回校验 */
const back = () => {
  // console.log(formParams.value)
  // console.log(oldFromInfo.value)
  // return

  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (typeof formParams.value[key] !== 'object') {
      if (
        JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
      ) {
        console.log(key)
        bool = true;
        return;
      }
    } else {
      if (key === 'personality') {
        if (formParams.value[key].join(',') !== oldFromInfo.value[key]) {
          console.log(key)
          bool = true;
          return;
        }
      } else if (key === 'department' || key === 'title') {
        if (formParams.value[key]?.length > 0) {
          if (formParams.value[key].join('-') !== oldFromInfo.value[key]) {
            console.log(key)
            bool = true;
            return;
          }
        }
      } else if (key === 'tone') {
        if (JSON.stringify(formParams.value[key]) !== oldFromInfo.value[key]) {
          console.log(key)
          bool = true;
          return;
        }
      } else if (key === 'kbIds') {
        const currentIds = formParams.value.kbIds.map(item => item.id)
        const oldIds = oldFromInfo.value.kbList.map(item => item.kbId)
        if (JSON.stringify(currentIds) !== JSON.stringify(oldIds)) {
          console.log(key)
          bool = true;
          return;
        }
      } else if (
        JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
      ) {
        console.log(key)
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }

}

const checkSave = () => {
  if (editFlag.value) {
    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
      if (typeof formParams.value[key] !== 'object') {
        if (
          JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
        ) {
          bool = true;
          return;
        }
      } else {
        if (key === 'personality') {
          if (formParams.value[key].join(',') !== oldFromInfo.value[key]) {
            bool = true;
            return;
          }
        } else if (key === 'department' || key === 'title') {
          if (formParams.value[key]?.length > 0) {
            if (formParams.value[key].join('-') !== oldFromInfo.value[key]) {
              console.log(key)
              bool = true;
              return;
            }
          }
        } else if (key === 'tone') {
          if (JSON.stringify(formParams.value[key]) !== oldFromInfo.value[key]) {
            bool = true;
            return;
          }
        } else if (key === 'kbIds') {
          const currentIds = formParams.value.kbIds.map(item => item.id)
          const oldIds = oldFromInfo.value.kbList.map(item => item.kbId)
          if (JSON.stringify(currentIds) !== JSON.stringify(oldIds)) {
            console.log(key)
            bool = true;
            return;
          }
        } else if (
          JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
        ) {
          console.log(JSON.stringify(formParams.value[key]), JSON.stringify(oldFromInfo.value[key]))
          bool = true;
          return;
        }
      }
    });
    if (bool) {
      ElMessageBox.confirm(
        '未保存的内容将丢失',
        '确定要返回吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          _getEManDetail()
          editFlag.value = false
        })
        .catch(() => {
        })

    } else {
      editFlag.value = false
    }
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}


onMounted(() => {
  getData()
  _getEManDetail();
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.gap8 {
  gap: 0 4px;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;
  }
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-start;
  margin: 0 auto;
  height: calc(100vh - 16px - 84px - 100px);
  overflow-y: auto;
  width: 100%;

  // margin-bottom: 30px;
  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.icon {
  width: 18px;
}

.script-msg {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  .left {
    width: 90px;
    font-weight: 500;
    margin-right: 16px;
  }

  .right {
    flex: 1;
  }
}

.contentText {
  font-size: 14px;
  color: #272C47;
}

:deep(.el-card) {
  // height: calc(100vh - 84px - 32px);

  .el-card__body {
    padding: 16px 0 !important;
  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-select, ) {
  width: 100%;
}

:deep(.el-cascader) {
  width: 100%;
}

:deep(.el-form) {
  width: 100%;
}

.avatar-empty {
  width: 91px;
  height: 91px;
  border-radius: 8px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .empty {
    width: 40px;
    height: 41px;
  }
}

.avatar-img {
  position: relative;
  width: 91px;
  height: 91px;

  .tips-icon {
    position: absolute;
    bottom: 4px;
    right: 4px;
    cursor: pointer;
    width: 20px;
    z-index: 1;
    height: 20px;
  }
}

.avatar {
  width: 91px;
  height: 91px;
  cursor: pointer;
  border-radius: 8px;
}

.input {
  :deep(.el-input__suffix) {
    cursor: pointer;
  }

  :deep(.el-input__inner) {
    cursor: pointer;
  }
}

.chosen-list {
  // margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px 0;

  &-item {
    display: flex;
    width: 100%;
    padding: 5px 0;
    align-items: center;
    gap: 0 10px;

    // margin-bottom: 10px;
    img {
      width: 30px;
      height: 30px;
    }
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}


.introduction-dellay {
  margin-bottom: 0px;

  :deep(.el-input__inner) {
    text-align: center;
  }
}

.content {
  margin-bottom: 0px;
}

.tips {
  text-align: left;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  margin-left: 30px;
}

.introduction-content-list {}

.font-limit {
  color: #999999;
  font-size: 12px;
  position: absolute;
  bottom: 8px;
  right: 10px;
  background-color: #fff;
  height: 14px;
  line-height: 14px;
}

.intro-icon {
  width: 30px;
  height: 30px;
  margin-bottom: 18px;
  background-color: #f4f6ff;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button {
  margin-left: 120px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.title-tips {
  margin-top: 12px;
  color: #919090;
  font-size: 14px;
}
</style>
