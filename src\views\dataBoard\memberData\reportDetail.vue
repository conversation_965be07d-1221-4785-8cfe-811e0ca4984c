<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="box-container">
          <div class="box-title flex">
            <div class="user-info flex">
              <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />
              <img :src="dataList[0]?.avatar || avatar_default_man" class="avatar" />
              <div class="">{{ dataList[0]?.name }}</div>
            </div>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="dataList">
        <el-table-column label="脚本" align="center" prop="scriptName" />
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            <span v-if="scope.row.type === 1">技巧类</span>
            <span v-else-if="scope.row.type === 2">答题类</span>
            <span v-else-if="scope.row.type === 3">幻灯片演练</span>
          </template>
        </el-table-column>
        <el-table-column label="拜访对象" v-if="company === 'kangyuan' && dataList[0]?.type !== 3">
          <template #default="scope">
            <div class="visit-object">
              <div>{{ scope.row.emanName }}</div>
              <Star v-if="scope.row.emanDifficult" v-model="scope.row.emanDifficult" readonly size="mini" />
            </div>
            <div class="visit-title">
              {{ occupationList.slice(0, 4).includes(scope.row.occupation) ? scope.row.department ? (
                scope.row.department.includes('-') ?
                  scope.row.department.split('-')[1] : scope.row.department) : '' : scope.row.occupation }}
              <span v-if="scope.row.title" style="margin: 0 2px;">|</span>
              {{ scope.row.title ? scope.row.title.includes('-') ? scope.row.title.split('-')[1] : scope.row.title : ''
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="提问方式" v-if="dataList[0]?.type === 2" align="center" prop="randomName">
          <template #default="scope">
            {{ scope.row.randomName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="时长" align="center" prop="durationText">
          <template #default="scope">
            {{ scope.row.durationText || '-' }}
          </template>
        </el-table-column>

        <el-table-column label="得分" align="center" prop="score">
          <template #default="scope">
            {{ scope.row.status === 2 ? scope.row.score : statusMap.get(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center" prop="createTime" />
        <el-table-column label="操作" align="center" prop="score" v-if="mode !== 'production' && dataList[0]?.reGenerateReport">
          <template #default="scope">
            <el-button link text type="primary" @click="handleRegenerate">重新生成</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div class="flex content" style="align-items:flex-start;">
      <div class="card-container" style="width:60.5%;">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <img src="@/assets/icons/svg/voice44.svg" v-if="dataList[0]?.type===3" />
                <img src="@/assets/icons/png/report.png" v-else />

                {{ dataList[0]?.type === 3 ? "讲解技巧" : "评分报告" }}
                <span class="score" v-if="dataList[0]?.type === 3"> {{ contentScore }} 分</span></span
              >
            </div>
          </template>
          <div class="card-content" v-if="dataList[0]?.status === 2">
            <div class="total-sumup"><img v-if="dataList[0]?.type !== 3" src="@/assets/icons/svg/voice11.svg" class="icon" />整体评价</div>
            <div class="sumup">{{ dataList[0]?.sumup }}</div>
            <div v-if="dataList[0]?.type === 2">
              <div class="qa-item" v-for="(item, index) in qAList" :key="index">
                <div class="question">
                  {{ index + 1 }}.{{ item.question }}
                  <span class="font" style="font-size: 20px"> {{ item?.score }}分</span>
                </div>
                <div class="answer">
                  <span class="font">回答：</span>
                  {{ item.answer }}
                  <span class="font" @click="item.referenceAnswerShow = false" v-show="item.referenceAnswerShow" style="cursor: pointer"
                    >参考答案</span
                  >
                </div>
                <div class="answer" v-show="!item.referenceAnswerShow">
                  <span class="font">参考答案：</span>
                  <span style="white-space: pre-wrap;"> {{ item.referenceAnswer }}</span>
                </div>
                <div class="suggest-content">
                  <div><span class="font">点评：</span> {{ item.comment }}</div>
                  <div><span class="font">建议：</span> {{ item.suggest }}</div>
                </div>
              </div>
            </div>
            <div v-else-if="dataList[0]?.type === 1 || dataList[0]?.type === 3">
              <!-- <div class="total-sumup content-title" v-if="dataList[0]?.type === 3">
              <img src="@/assets/icons/svg/voice33.svg" class="icon" />
              内容完整性<span class="score">{{ contentScore }}分</span>
            </div> -->
              <div class="qa-item" v-for="(item, index) in dimension" :key="index">
                <div class="question">
                  {{ index + 1 }}.{{ item?.name }}
                  <span class="font" style="font-size: 20px;margin-left: 8px;">{{ item?.score }}/{{ item?.fullScore}}分</span>
                </div>
                <div class="suggest-content">
                  <div class="dimension-item" v-for="(it, i) in item.children" :key="i">
                    <div class="name font">{{ it.name }}</div>
                    <div style="width: 80px;text-align: left;">{{ it.score }}/{{ it.fullScore }}分</div>
                    <div style="flex:1;">
                      {{ it.remark }}
                      {{ it.suggestAnswer === '无' || !it.suggestAnswer ? '' : `可参考话术“${it.suggestAnswer}”`
                      }}
                    </div>
                  </div>
                  <span>建议： {{ item.suggest }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="card-content status-wrap" v-else>
            <div class="status-container" v-if="dataList[0]">
              <img :src="statusImgMap.get(dataList[0]?.status)" class="status-img" />
              <span>{{ statusMap.get(dataList[0]?.status) }}</span>
            </div>
          </div>
        </el-card>
        <el-card v-if="dataList[0]?.type === 3 && voiceInfo">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <img src="@/assets/icons/svg/voice22.svg" />
                声音流畅度
                <span class="score" v-if="dataList[0]?.type === 3">{{ voiceScore }}分</span></span
              >
            </div>
          </template>
          <div class="card-content" v-if="dataList[0]?.status === 2">
            <div>
              <div class="chart-list-container flex">
                <div class="chart-item flex" v-if="voiceInfo?.rateVoice?.enabled">
                  <div class="chart-top flex">
                    <div class="left">语速：{{ voiceInfo?.rateVoice?.description }}</div>
                    <div class="tips">平均{{ voiceInfo?.rateVoice?.averageSpeakSpeed }}字/分钟</div>
                  </div>
                  <div class="chart">
                    <LineChart ref="lineRef" :width="'100%'" height="300px"></LineChart>
                  </div>
                  <div class="suggest">建议：{{ voiceInfo?.rateVoice?.suggest }}</div>
                </div>
                <div class="chart-item flex" v-if="voiceInfo?.volumeVoice?.enabled">
                  <div class="chart-top flex">
                    <div class="left">音量：{{ voiceInfo?.volumeVoice?.description }}</div>
                    <div class="tips">平均{{ voiceInfo?.volumeVoice?.averageVolumeDB }}db</div>
                  </div>
                  <div class="chart">
                    <LineChart ref="lineRef1" :width="'100%'" height="300px"></LineChart>
                  </div>
                  <div class="suggest">建议：{{ voiceInfo?.volumeVoice?.suggest }}</div>
                </div>
                <div class="chart-item flex" v-if="voiceInfo?.redundantWordVoice?.enabled">
                  <div class="chart-top flex">
                    <div class="left">冗余词：{{ voiceInfo?.redundantWordVoice?.description }}</div>
                    <div class="tips">累计{{ voiceInfo?.redundantWordVoice?.num }}次</div>
                  </div>
                  <div class="chart">
                    <LineChart ref="lineRef2" :width="'100%'" height="300px"></LineChart>
                  </div>
                  <div class="suggest">建议：{{ voiceInfo?.redundantWordVoice?.suggest }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      <el-card class="history">
        <template #header>
          <div class="card-header">
            <span class="card-title">
              <img src="@/assets/icons/svg/content11.svg" v-if="dataList[0]?.type===3" />
              <img src="@/assets/icons/png/chat.png" v-else />
              {{ dataList[0]?.type === 3 ? '内容完整性' : '对话记录' }}
              <span v-if="dataList[0]?.type === 3" class="score">{{ integrityScore }}分</span>
            </span>
            <div class="top" v-if="dataList[0]?.type !== 3">对话为情景模拟，非真实场景</div>
          </div>
        </template>

        <div v-if="dataList[0]?.type === 3">
          <div>
            <audio ref="pptAudio" controls controlslist="noplaybackrate nodownload novolume noremoteplayback" class="ppt_audio" />
          </div>
          <div class="chat-content chat-content-ppt" v-if="pptSummary?.answer && pptDetailList.length === 0">
            {{ pptSummary?.answer }}
          </div>
          <div class="chat-content chat-content-ppt" v-else-if="pptDetailList.length > 0">
            <div class="ppt-item flex" v-for="(item, index) in pptDetailList" :key="index">
              <div class="img-container">
                <div class="page">P{{ index + 1 }}</div>
                <el-image :src="item.imageUrl" style="width: 100%;" lazy />
              </div>
              <div class="requirement flex">
                <div class="right">
                  <span class="name font">得分：</span>
                  <span class="name"> {{ item.score || 0 }}/100分</span>
                </div>
              </div>
              <div class="requirement flex">
                <div class="right">
                  <span v-if="pptScoringType === '1'"><span class="name font">演练要求：</span>{{ item.requirement ||'无'}}</span>
                  <div style="gap: 0; justify-content: flex-start;" v-else-if="['2', '3'].includes(pptScoringType) ">
                    <span class="font name">关键词：</span>
                    <template v-if="item.keywords">
                      <span v-for="(it, i) in JSON.parse(item.keywords) || []" :key="i">
                        {{ it.name }}
                        <template v-if="it.synonyms && it.synonyms.length > 0">/</template>
                        <template v-if="it.synonyms"> {{ it.synonyms.join('/') }}</template>
                        <template v-if="i !== (JSON.parse(item.keywords) || []).length - 1">、</template>
                      </span>
                    </template>
                    <template v-else>
                      <span>无</span>
                    </template>
                  </div>
                </div>
              </div>

              <div class="requirement flex">
                <div class="right">
                  <span class="name font">学员讲解：</span>
                  {{ item.answer || '无' }}
                </div>
              </div>
              <div class="requirement flex">
                <div class="right">
                  <span class="name font">点评：</span>
                  {{ item.comment || '无' }}
                </div>
              </div>
            </div>
          </div>

          <div class="chat-content status-wrap" v-else>
            <div class="status-container">
              <img :src="empty" style="width: 60%;" />
              <span>无演练记录</span>
            </div>
          </div>
        </div>
        <div class="chat-wrap" v-else>
          <div class="time">{{ dayjs(dataList[0]?.createTime).format('MM-DD HH:mm') }}</div>
          <div class="background">{{ dataList[0]?.scene }}</div>
          <div class="chat-content" v-if="chatHistory.length > 0">
            <div class="chat-item" :class="item.role === 'user' ? 'user' : ''" v-for="(item, index) in chatHistory" :key="index">
              <img :src="item.avatar || avatar_default_man" class="avatar" />
              <div class="chat-word" :style="{ paddingBottom: item.audioFlag ? '30px' : '#' }">
                {{ item.content }}
                <span @click="playAudio(item.audioUrl, item.id)" v-if="item.audioFlag && currentPlayId !== item.id">
                  <el-tooltip content="点击播放" placement="top" v-if="item.role === 'user' && item.audioFlag">
                    <img src="@/assets/icons/svg/voice1.svg" class="voice1" />
                  </el-tooltip>
                  <el-tooltip content="点击播放" placement="top" v-else-if="item.audioFlag">
                    <img src="@/assets/icons/svg/voice.svg" class="voice" />
                  </el-tooltip>
                </span>
                <span v-else-if="item.audioFlag && currentPlayId === item.id" @click="pauseAudio()">
                  <el-tooltip content="暂停" placement="top">
                    <img src="@/assets/icons/svg/pause1.svg" class="voice1" v-if="item.role === 'user'" />
                    <img src="@/assets/icons/svg/pause.svg" v-else class="voice" />
                  </el-tooltip>
                </span>
              </div>
            </div>
          </div>

          <div class="chat-content status-wrap" v-else>
            <div class="status-container">
              <img :src="empty" style="width: 60%;" />
              <span>双方都保持了沉默，无对话记录</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <div id="audioList" v-show="false"></div>
  </div>
</template>

<script setup name="user" lang="ts">
import dayjs from 'dayjs'
// import { onBeforeRouteUpdate } from "vue-router";
import { getChatHistory, getChatReport, reGenerateReport, getPPTSummaryDetail } from "@/api/dataBoard";
import { ChatReportDetailVo, ChatQADetailVo, ChatHistoryDetailVo, PPTSummaryVo } from "@/api/dataBoard/types";
import avatar_default_man from "@/assets/images/avatar_default_man.jpg";
import defaultImg from "@/assets/images/default.png";
import failImg from "@/assets/images/fail.png";
import reportingImg from "@/assets/images/reporting.png";
import empty from "@/assets/images/empty.png";
import axios from "axios";
import { occupationList } from "@/views/eManManage/components/data";
import useUserStore from "@/store/modules/user";
import LineChart from '../scriptData/components/lineChat.vue';
import { da, el } from 'element-plus/es/locale';
// import { da } from 'element-plus/es/locale';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const currentPlayId = ref<number | string>('')
const mode = import.meta.env.MODE;
const company = import.meta.env.VITE_APP_COMPANY;
console.log('mode', mode)
const route = useRoute();
const dataList = ref<(ChatReportDetailVo & { randomName?: string, durationText?: string })[]>([]);
const loading = ref(false);
const router = useRouter();
const audioContext = ref()
const source = ref()
// const audioBuffer = ref()
const userStore = useUserStore();
const pptScoringType = ref('')
const contentScore = ref(0)
const integrityScore = ref(0)
const voiceScore = ref(0)
const voiceInfo = ref<any>()
const lineRef = ref(null)
const lineRef1 = ref(null)
const lineRef2 = ref(null)
interface PptItem {
  answer: string
  audioUrl: string
  chatId: string
  comment: string
  id: number | string
  imageId: string
  imageUrl: string
  keywords: string
  requirement: string
  score: number | null
}
const pptDetailList = ref<PptItem[]>([])





const id = ref<number>(route.query.id as unknown as number)
const randomFlagMap = new Map([
  [0, '按顺序提问'],
  [1, '随机提问顺序'],
  [2, '随机提问'],
  [3, '随机提问']
])
const statusMap = new Map([
  [1, '无报告'],
  [2, '已生成'],
  [3, '生成错误'],
  [4, '生成中'],
])
const statusImgMap = new Map([
  [1, defaultImg],
  [3, failImg],
  [4, reportingImg],
])
const qAList = ref<ChatQADetailVo[]>([])
const chatHistory = ref<ChatHistoryDetailVo[]>([])
const dimension = ref<any>([])
const pptSummary = ref<PPTSummaryVo>()
const pptAudio = ref();
// randomFlag 0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目

const handleRegenerate = () => {
  reGenerateReport(id.value).then(res => {
    proxy.$modal.msgSuccess('操作成功')
    getList()
  })
}

var addWavHeader = function (samples: { byteLength: any; }, sampleRateTmp: number, sampleBits: number, channelCount: number) {
  var dataLength = samples.byteLength;
  var buffer = new ArrayBuffer(44 + dataLength);
  var view = new DataView(buffer);
  function writeString(view: DataView, offset: number, string: string) {
    for (var i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }
  var offset = 0;
  /* 资源交换文件标识符 */
  writeString(view, offset, 'RIFF'); offset += 4;
  /* 下个地址开始到文件尾总字节数,即文件大小-8 */
  view.setUint32(offset, /*32*/ 36 + dataLength, true); offset += 4;
  /* WAV文件标志 */
  writeString(view, offset, 'WAVE'); offset += 4;
  /* 波形格式标志 */
  writeString(view, offset, 'fmt '); offset += 4;
  /* 过滤字节,一般为 0x10 = 16 */
  view.setUint32(offset, 16, true); offset += 4;
  /* 格式类别 (PCM形式采样数据) */
  view.setUint16(offset, 1, true); offset += 2;
  /* 通道数 */
  view.setUint16(offset, channelCount, true); offset += 2;
  /* 采样率,每秒样本数,表示每个通道的播放速度 */
  view.setUint32(offset, sampleRateTmp, true); offset += 4;
  /* 波形数据传输率 (每秒平均字节数) 通道数×每秒数据位数×每样本数据位/8 */
  view.setUint32(offset, sampleRateTmp * channelCount * (sampleBits / 8), true); offset += 4;
  /* 快数据调整数 采样一次占用字节数 通道数×每样本的数据位数/8 */
  view.setUint16(offset, channelCount * (sampleBits / 8), true); offset += 2;
  /* 每样本数据位数 */
  view.setUint16(offset, sampleBits, true); offset += 2;
  /* 数据标识符 */
  writeString(view, offset, 'data'); offset += 4;
  /* 采样数据总数,即数据总大小-44 */
  view.setUint32(offset, dataLength, true); offset += 4;
  function floatTo32BitPCM(output: DataView, offset: number, input: string | any[] | Iterable<number>) {
    input = new Int32Array(input);
    for (var i = 0; i < input.length; i++, offset += 4) {
      output.setInt32(offset, input[i], true);
    }
  }
  function floatTo16BitPCM(output: DataView, offset: number, input: string | any[] | Iterable<number>) {
    if (input.byteLength % 2 !== 0) {
      proxy.$modal.msgError('音频文件格式错误')
      currentPlayId.value = ''
      return
    }
    input = new Int16Array(input);
    for (var i = 0; i < input.length; i++, offset += 2) {
      output.setInt16(offset, input[i], true);
    }
  }
  function floatTo8BitPCM(output: DataView, offset: number, input: string | any[] | Iterable<number>) {
    input = new Int8Array(input);
    for (var i = 0; i < input.length; i++, offset++) {
      output.setInt8(offset, input[i], true);
    }
  }
  if (sampleBits == 16) {
    floatTo16BitPCM(view, 44, samples);
  } else if (sampleBits == 8) {
    floatTo8BitPCM(view, 44, samples);
  } else {
    floatTo32BitPCM(view, 44, samples);
  }
  return view.buffer;
}

/** 查询报告详情 */
const getList = async () => {
  loading.value = true;
  const res = await getChatReport(id.value);
  const randomTypeName = randomFlagMap.get(res.data.randomFlag) || ''
  contentScore.value = res.data.contentScore||0
  integrityScore.value = res.data.integrityScore||0
  voiceScore.value = res.data.voiceRateScore||0
  pptScoringType.value = String(res.data.pptScoringType)
  let randomName = ''
  if (res.data.randomFlag === 2) {
    randomName = randomTypeName + res.data.randomNum + '题'
  } else if (res.data.randomFlag === 3) {
    randomName = randomTypeName + res.data.qaList.length + '题'
  } else {
    randomName = randomTypeName
  }
  if (res.data.pptList[0]?.score !== null) {
    pptDetailList.value = res.data.pptList
  }
  const finishTime = dayjs(res.data.finishTime)
  const createTime = dayjs(res.data.createTime)
  const durationTime = finishTime.diff(createTime, 'seconds');
  let durationText = ''
  if (durationTime) {
    const hours = Math.floor(Number(durationTime) / 3600)
    const min = Math.floor((Number(durationTime) % 3600) / 60)
    const seconds = Number(durationTime) % 60; // 计算剩余秒数
    durationText = hours > 0 ? hours + '小时' + min + '分' + seconds + '秒' : min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
  } else {
    durationText = '0秒'
  }

  res.data.qaList.forEach(element => {
    element.referenceAnswerShow = true
  });
  qAList.value = res.data.qaList
  dataList.value = [{ ...res.data, randomName: randomName, durationText: durationText }];
  dimension.value = res.data.dimension ? JSON.parse(res.data.dimension).dimension ? JSON.parse(res.data.dimension).dimension : JSON.parse(res.data.dimension) : []
  loading.value = false;
  voiceInfo.value = res.data.voiceRateDimension ? JSON.parse(res.data.voiceRateDimension) : null
  _getChatHistory()
  _getPPTSummaryDetail()
  if (res.data.voiceRateDimension) {
    nextTick(() => {
      console.log(voiceInfo.value)
      setChartData(voiceInfo.value)
      setChartData1(voiceInfo.value)
      setChartData2(voiceInfo.value)
    })
  }

}


const setChartData = (data: any) => {
  const voiceObj = data
  const max = voiceObj.rateVoice.averageSpeakSpeed > voiceObj.rateVoice.max ? voiceObj.rateVoice.averageSpeakSpeed * 1.05 : voiceObj.rateVoice.max * 1.5
  if (lineRef.value) {
    lineRef!.value.setOptions({
      xAxis: [{
        type: 'category',
        show: false,
        data: Array.from({ length: 10 }, () => 0), // 示例数据
        min: 0
      }],
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          return '平均语速：' + params[0].value + '字/分钟'
        }

      },
      yAxis: [{
        type: 'value',
        show: false,
        max: max,
        min: 0,
        splitLine: {
          show: false
        }
      }],
      grid: {
        top: 20,
        bottom: 20,
        left: 0,
        right: '10%',
        backgroundColor: '#F6F7FB',
      },
      series: [
        {
          data: Array.from({ length: 10 }, () => voiceObj.rateVoice.averageSpeakSpeed), // 示例数据
          type: 'line',
          smooth: true,
          showSymbol: false,

          lineStyle: {
            color: '#4F66FF', // 线条颜色
            width: 1, // 线条宽度
            type: 'dashed', // 虚线类型
            dashOffset: '0' // 虚线偏移量，可选
          },
          markArea: {
            silent: true,
            label: {
              position: ['95%', '50%'],
              color: '#9597A0'
            },
            data: [
              [
                {
                  name: '慢',
                  yAxis: 0,
                  x: '0',
                  itemStyle: {
                    color: '#F6F7FB'
                  }
                },
                { x: '100%', yAxis: voiceObj.rateVoice.min }
              ],
              [
                {
                  name: '中',
                  yAxis: voiceObj.rateVoice.max,
                  x: '0',
                  itemStyle: {
                    color: '#E2E8FF'
                  }
                },
                { x: '100%', yAxis: voiceObj.rateVoice.min }
              ],
              [
                {
                  name: '快',
                  yAxis: voiceObj.rateVoice.max,
                  x: '0',
                  itemStyle: {
                    color: '#F6F7FB'
                  }
                },
                { x: '100%', yAxis: max }
              ]
            ]
          }
        }
      ],
      // visualMap: {
      //     show: false,
      //     pieces: [
      //         { gt: 0, lte: 139, color: '#4F66FF' },
      //         { gt: 139, lte: 179, color: '#4F66FF' },
      //         { gt: 179, color: '#4F66FF' }
      //     ],
      //     outOfRange: {
      //         color: '#4F66FF'
      //     }
      // }
    })
  }
}

const setChartData1 = (data: any) => {
  const voice = data
  const indexArray = data.volumeVoice.dbList.map((item: any, index: number) => {
    return index
  })
  // const maxValue = voice.volumeVoice.dataList.reduce((max:number, current:number) => (current > max ? current : max), -Infinity);

  // let maxValue = data.volumeVoice.dbList[0];
  // for (var i = 1; i < data.volumeVoice.dbList.length; i++) {
  //   if (maxValue < data.volumeVoice.dbList[i]) maxValue = data.volumeVoice.dbList[i];
  // }

  // const max = maxValue > data.volumeVoice.max ? maxValue * 1.05 : data.volumeVoice.max * 1.5
  if (lineRef1.value) {
    lineRef1!.value.setOptions({
      xAxis: [{
        type: 'category',
        show: false,
        boundaryGap: false,
        data: indexArray, // 示例数据
        max: function (value) {
          return value.max + 0.05 * value.max
        },
      }],
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          return '音量：' + params[0].value + 'db'
        }
      },
      yAxis: [{
        type: 'value',
        show: false,
        splitLine: {
          show: false
        },
        max: 100

      }],
      grid: {
        top: 20,
        bottom: 20,
        left: 0,
        right: '10%',
        backgroundColor: '#F6F7FB',
        containLabel: true
      },
      color: '#4F66FF',
      series: [
        {
          data: voice.volumeVoice.dbList, // 示例数据
          type: 'bar',
          barWidth: '60%',
          // smooth: true,
          // showSymbol: false,
          legendHoverLink: false,
          silent: true,

          // markLine: {
          //   z: 99,
          //   symbol: ['none', 'none'], // none为标线两端的样式为无，可更改
          //   data: [
          //     {
          //       name: '低',
          //       silent: false,
          //       length: 100,
          //       x: 0,
          //       yAxis: voice.volumeVoice.min,
          //       label: {
          //         position: 'end', // 文字位置
          //         formatter: '低',  //文字
          //         color: '#000000'  // 文字颜色
          //       },
          //       lineStyle: { type: 'dashed', color: '#ff8372', width: 2 } // 样式： 线型、颜色、线宽
          //     },
          //     {
          //       name: '高',
          //       silent: false,
          //       length: 100,
          //       x: 0,
          //       yAxis: voice.volumeVoice.max,
          //       label: {
          //         position: 'end', // 文字位置
          //         formatter: '高',  //文字
          //         color: '#000000'  // 文字颜色
          //       },
          //       lineStyle: { type: 'dashed', color: '#ff8372', width: 2 } // 样式： 线型、颜色、线宽
          //     }
          //   ]
          // }

          markArea: {
            silent: true,
            label: {
              position: ['95%', '50%'],
              color: '#9597A0'
            },
            data: [
              [
                {
                  name: '慢',
                  yAxis: 0,
                  x: '0',
                  itemStyle: {
                    color: '#F6F7FB'
                  }
                },
                { x: '100%', yAxis: voice.volumeVoice.min }
              ],
              [
                {
                  name: '中',
                  yAxis: voice.volumeVoice.max,
                  x: '0',
                  itemStyle: {
                    color: '#E2E8FF'
                  }
                },
                { x: '100%', yAxis: voice.volumeVoice.min }
              ],
              [
                {
                  name: '快',
                  yAxis: voice.volumeVoice.max,
                  x: '0',
                  itemStyle: {
                    color: '#F6F7FB'
                  }
                },
                { x: '100%', yAxis: 100 }
              ]
            ]
          }

        },
        {
          type: 'pictorialBar',
          itemStyle: {
            color: '#F6F7FB'
          },
          symbolRepeat: true,
          symbolMargin: 3,
          symbol: 'rect',
          symbolSize: ['100%', 3], // 调整装饰条方向
          data: voice.volumeVoice.dbList,
          z: 3
        },
      ],
      // dataZoom: [
      //   {
      //     type: 'slider',
      //     xAxisIndex: [0], // 控制X轴的滚动条
      //     start: 0,
      //     end: 60
      //   },

      // ]

    })
  }
}

const setChartData2 = (data: any) => {
  const voiceObj = data
  const array: any = []
  Object.keys(voiceObj.redundantWordVoice.redundantWordCountMap).forEach((element: any) => {
    array.push({
      key: element,
      value: voiceObj.redundantWordVoice.redundantWordCountMap[element]
    })
  });
  const newArray = array.sort((a: any, b: any) => b.value - a.value)
  if (lineRef2.value) {
    lineRef2!.value.setOptions({
      xAxis: [{
        type: 'category',
        show: true,
        boundaryGap: false,
        data: newArray.map((item: any) => item.key), // 示例数据
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      }],
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          return '出现频率：' + params[0].value + '次'
        }
      },
      yAxis: [{
        type: 'value',
        show: false,
        splitLine: {
          show: false
        }
      }],
      grid: {
        top: 30,
        bottom: 20,
        left: 0,
        right: '10%',
        backgroundColor: '#F6F7FB',
        containLabel: true
      },
      color: '#4F66FF',
      series: [
        {
          data: newArray.map((item: any) => item.value), // 示例数据
          type: 'bar',
          // barWidth: 15,
          // smooth: true,
          // showSymbol: false,
          legendHoverLink: false,
          silent: true,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}次'
          },

        },

      ],

    })
  }
}

// const playAudio = async (url: string, id: string | number) => {
//   // console.log(url)
//   audioContext.value = null
//   currentPlayId.value = id
//   source.value = null
//   audioBuffer.value = null
//   if (!audioBuffer.value) {
//     await initPcmAudio(url);
//   }

//   // 创建AudioBufferSourceNode
//   source.value = audioContext.value!.createBufferSource();
//   source.value.buffer = audioBuffer.value;
//   source.value.connect(audioContext.value!.destination);
//   source.value.start();

//   // 监听音频结束事件
//   source.value.onended = () => {
//     currentPlayId.value = ''
//   };
// }
// function decodePCM(arrayBuffer: ArrayBufferLike, sampleBits: number, channelCount: number, sampleRate: number, audioContext: any) {
//   return new Promise((resolve, reject) => {
//     const dataView = new DataView(arrayBuffer);
//     const length = (arrayBuffer.byteLength / (sampleBits / 8) / channelCount);
//     const buffer = audioContext.createBuffer(channelCount, length, sampleRate);
//     let offset = 0;

//     for (let channel = 0; channel < channelCount; channel++) {
//       const channelBuffer = buffer.getChannelData(channel);
//       for (let i = 0; i < length; i++) {
//         const sample = dataView.getInt16(offset, true); // 假设PCM数据是16位有符号整数
//         channelBuffer[i] = sample / 32768; // 标准化到-1到1的范围
//         offset += 2; // 16位 = 2字节
//       }
//     }

//     resolve(buffer);
//   });
// }


const requestAudio = (url: string) => {
  return new Promise((resolve, reject) => {
    fetch(url)
      .then(response => {
        if (!response.ok) {
          reject(Error(`HTTP 错误！状态: ${response.status}`))
        }
        return response.arrayBuffer();
      })
      .then(buffer => {
        // 在这里处理获取到的 ArrayBuffer 数据
        resolve(buffer);
      })
      .catch(error => {
        console.error('发生错误:', error);
        reject(error)
      });
  })
}


const playAudio = (url: string, id: string) => {
  const file = new File([url], id, { type: "audio/mp3" })
  if (currentPlayId.value !== '') {
    source.value.pause()
    currentPlayId.value = ''
  }
  currentPlayId.value = id
  audioContext.value = null
  source.value = null
  // 加载音频文件
  requestAudio(url).then((res) => {
    const arraybuffer = res
    console.log(arraybuffer)

    var fileResult = addWavHeader(arraybuffer, 16000, 16, 1);
    var blob = new Blob([fileResult], { type: 'autio/wave' });
    var src = URL.createObjectURL(blob);
    console.log(src)
    var _audioControl = document.createElement('audio');
    source.value = _audioControl
    source.value.src = src;
    source.value.id = file.name;
    source.value.controls = true;
    source.value.addEventListener('ended', audioEnded);
    var _label = document.createElement('lable');
    _label.append(file.name);
    var div = document.createElement('div');
    div.append(_label);
    div.append(_audioControl);
    document.getElementById('audioList')!.append(div);
    source.value.play()
  })

  /*  axios.request({
     method: 'get',
     url: url,
     headers: {
       clientid: ''

     },
     responseType: "arraybuffer",
   }).then(res => {
     const arraybuffer = res.data
     console.log(arraybuffer)

     var fileResult = addWavHeader(arraybuffer, 16000, 16, 1);
     var blob = new Blob([fileResult], { type: 'autio/wave' });
     var src = URL.createObjectURL(blob);
     console.log(src)
     var _audioControl = document.createElement('audio');
     source.value = _audioControl
     source.value.src = src;
     source.value.id = file.name;
     source.value.controls = true;
     source.value.addEventListener('ended', audioEnded);
     var _label = document.createElement('lable');
     _label.append(file.name);
     var div = document.createElement('div');
     div.append(_label);
     div.append(_audioControl);
     document.getElementById('audioList')!.append(div);
     source.value.play()
     // source.value.onended(()=>{
     //   source.value.currentTime=0
     //   currentPlayId.value = ''
     // })


     // if (!audioContext.value) {
     //   audioContext.value = new (
     //     window.AudioContext)();
     // }
     // // const audioContext = new window.AudioContext();
     // decodePCM(arraybuffer, 16, 1, 16000, audioContext.value).then(buffer => {
     //   source.value = audioContext.value.createBufferSource();
     //   source.value.buffer = buffer;
     //   source.value.connect(audioContext.value.destination);
     //   source.value.start(); // 开始播放
     // }).catch(error => {
     //   console.error('Error decoding PCM:', error);
     // });

   }) */
}

const audioEnded = () => {
  // console.log(21234243)
  source.value.pause()
  currentPlayId.value = ''
}




const pauseAudio = () => {
  source.value.pause()
  currentPlayId.value = ''
}

const handlePPTAudio = (url: string) => {
  if (!url) {
    return
  };
  requestAudio(url).then((res) => {
    if (!res) return;
    pptAudio.value.style.display = 'block';
    const arraybuffer = res
    console.log(arraybuffer)
    var fileResult = addWavHeader(arraybuffer, 16000, 16, 1);
    var blob = new Blob([fileResult], { type: 'autio/wave' });
    var src = URL.createObjectURL(blob);
    console.log(src)
    pptAudio.value.src = src;
    pptAudio.value.controls = true;
    pptAudio.value.addEventListener('ended', () => {
      console.log('ende')
    });
  })
  pptAudio.value.src = url;
}

// 对话记录
const _getChatHistory = async () => {
  const res = await getChatHistory(dataList.value[0]?.chatId)
  chatHistory.value = res.data
}

const _getPPTSummaryDetail = async () => {
  const res = await getPPTSummaryDetail(dataList.value[0]?.chatId)
  if (res.data) {
    handlePPTAudio(res.data.audioUrl)
    pptSummary.value = res.data
  }
}

onMounted(() => {
  getList()
})
onBeforeUnmount(() => {
  if (source.value) {
    pauseAudio()
  }
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.top {
  position: absolute;
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: rgb(240, 246, 255);
  color: #67686F;
  bottom: -41px;
  padding: 0 15px;
  z-index: 2;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0 12px;

  img {
    width: 20px;
    height: 20px;
  }
}

.card-container {
  display: flex;
  flex-direction: column;
  gap: 10px 0;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content {
  margin-top: 16px;
  gap: 0 16px;
  position: relative;
}

.history {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 39%;

  :deep(.el-card__body) {
    height: calc(100% - 100px);
    overflow: auto;
    position: relative;
  }

  :deep(.el-card__header) {
    padding: 0 !important;
  }

  .card-title {
    padding: 14px 15px 7px;
  }
}


.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;
  align-items: flex-start;
  margin: 0 auto;
  padding: 30px 0;
  width: 100%;

  .box-title {
    justify-content: space-between;
    width: 100%;
  }

  .user-info {
    .avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin-right: 10px;
      margin-left: 12px;
    }
  }
}

.status-wrap {
  display: flex;
  align-items: center;
  justify-content: center;

  .status-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px 0;
    color: #999999;

    .status-img {
      width: 180px;
      height: 180px;
    }
  }
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: calc(100vh - 84px - 32px - 100px - 250px + 24px);

  .total-sumup {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    .icon {
      margin-right: 12px;
    }
  }

  .qa-item {
    display: flex;
    flex-direction: column;
    gap: 8px 0;
    margin-top: 16px;

    .question {
      font-size: 16px;
      font-weight: bold;
    }

    .answer {
      font-size: 14px;
    }
  }


}

.font {
    color: #4F66FF;
    font-weight: bold;
  }
  .name{
    font-weight: bold;
  }

.content-title {
  margin-top: 24px;
  font-weight: bold;
}

.score {
  margin-left: 15px;
  color: #4F66FF;
}

.chart-list-container {
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
  margin-top: 12px;
}

.chart-item {
  width: 48%;
  min-height: 400px;
  flex-direction: column;
  justify-content: flex-start;

  .chart-top {
    width: 100%;

    .tips {
      font-size: 15px;
      color: #999999;
    }
  }

  .chart {
    width: 100%;
    height: 300px;
  }
}

.suggest {
  color: #67686F;
  font-size: 14px;
  text-align: left;
  width: 100%;

}

.sumup,
.suggest-content {
  display: flex;
  flex-direction: column;
  padding: 16px 32px;
  background-color: rgb(246, 247, 251);
  font-size: 14px;
  gap: 8px 0;
  border-radius: 4px;
}

.bold {
  font-weight: bold;
}

.dimension-item {
  display: flex;
  align-items: flex-start;
  gap: 0 24px;

  .name {
    font-weight: bold;
    min-width: 80px;
  }
}

.chat-wrap {
  display: flex;
  flex-direction: column;
  gap: 20px 0;
  width: 100%;

  .background {
    width: 80%;
    margin: 0 auto;
    background-color: #f4f6ff;
    padding: 8px 16px;
    border-radius: 4px;
    color: #999;
    font-size: 14px;


  }


  .time {
    margin-top: 60px;
    text-align: center;
    color: #272C47;
    font-size: 12px;
  }
}

.ppt-item {
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 12px;
}
.img-container{
  width: 100%;
  position: relative;
  .page{
    position: absolute;
    top: 0;
    left: 0;
    padding: 4px 12px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    z-index: 1;
    border-radius: 0 0 8px 0;
  }
}

.requirement {
  justify-content: flex-start;
  margin-bottom: 8px;
  font-size: 14px;
}


.chat-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px 0;
  position: relative;
  min-height: calc(100vh - 84px - 32px - 100px - 550px);
  height: 100%;

  &-ppt {
    white-space: pre-wrap;

  }

  .chat-item {
    display: flex;
    align-items: flex-start;
    gap: 0 16px;
  }

  .chat-word {
    background-color: #f4f6ff;
    min-height: 40px;
    display: flex;
    border-radius: 4px;
    min-width: 50px;
    align-items: center;
    max-width: 60%;
    word-wrap: break-word;
    padding: 8px 16px;
    box-sizing: border-box;
    position: relative;
    font-size: 14px;
    position: relative;

    .voice {
      position: absolute;
      bottom: 8px;
      left: 16px;
      cursor: pointer;
    }
  }

  .chat-word::after {
    content: '';
    position: absolute;
    left: -5px;
    top: 12px;
    width: 15px;
    height: 15px;
    transform: rotate(45deg);
    background-color: rgb(246, 247, 251);
  }

  .user>.chat-word::after {
    left: unset !important;
    right: -5px !important;
    background-color: rgb(79, 102, 255);
  }

  .user {
    position: relative;
    flex-direction: row-reverse;

  }

  .user>.chat-word {
    background-color: #4f66ff;
    color: #fff;
    position: relative;

    .voice1 {
      position: absolute !important;
      bottom: 8px !important;
      right: 16px !important;
      cursor: pointer;
    }
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }
}


:deep(.el-card) {
  .el-card__body {
    // height: calc(100vh - 84px );
  }
}

:deep(.el-radio-button) {
  .el-radio-button__original-radio:checked+.el-radio-button__inner {
    color: var(--el-radio-button-checked-bg-color);
    border-color: var(--el-radio-button-checked-bg-color);
    background-color: var(--el-radio-button-checked-text-color);
  }
}

.visit {
  &-object {
    display: flex;
    align-items: center;
    gap: 10px;
    line-height: 1;
  }

  &-title {
    color: var(--el-text-color-disabled);
    font-size: 12px;
  }
}

.ppt_audio {
  width: 100%;
  // display: none;

  &::-webkit-media-controls-panel {
    background: rgb(255, 255, 255);
  }
}

// audio::-webkit-media-controls-play-button {
//   background-color: red;
// }


// audio::-webkit-media-controls-current-time-display,
// audio::-webkit-media-controls-time-remaining-display {
//   display: none; /* 隐藏时间显示 */
// }

audio::-webkit-media-controls-progress-bar {
  background: rgba(255, 0, 0, 0.5);
  /* 设置进度条背景颜色 */
}

audio::-webkit-progress-bar {
  background: transparent;
  /* 设置进度条轨道背景颜色 */
}

audio::-webkit-progress-value {
  background: #ffffff;
  /* 设置已播放部分的颜色 */
}
</style>
