<template>
  <el-dialog v-model="visibleDialog" width="750px" append-to-body>
    <template #title>
      <div>对话页面设置</div>
      <div class="sub-title">设置需要显示的E人类型</div>
    </template>
    <div class="dialog-content">
      <el-radio-group v-model="type">
        <el-radio value="SCENE_PRACTISE_AND_SMART_BRAIN" size="large">显示“情景演练+企业智脑”类E人 <el-tag>默认</el-tag></el-radio>
        <el-radio value="SMART_BRAIN" size="large">只显示“企业智脑”类E人</el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  value: {
    type: String,
    default: ''
  }
})
const emits = defineEmits(['update:visible', 'onSuccess'])
const type = ref()
// 弹窗组件显示隐藏
const visibleDialog = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const closeDialog = () => {
  visibleDialog.value = false
}
const handleSubmit = () => {
  closeDialog()
  emits('onSuccess','chatPage', type.value)
}
watch(() => props.visible, (val) => {
  if(val) {
    type.value = props.value
  }
})
</script>

<style scoped lang="scss">
@import "./style";
:deep {
  .el-radio-group {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
