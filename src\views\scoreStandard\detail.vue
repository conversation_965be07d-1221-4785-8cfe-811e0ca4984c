<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex" v-if="editFlag">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            {{ title }}</span
          >
          <span class="card-title flex" v-else>
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            {{ title }}</span
          >
          <div class="right-button" v-if="editFlag">
            <el-button plain type="info" @click="props.isEdit ? back() : checkSave()"> 取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
          <div class="right-button" v-else>
            <el-button type="primary" plain @click="emits('change-type', { flagType: 'list', id: '', listType: listType })">返回</el-button>
            <el-button type="primary" :disabled="form.system" @click="editFlag = true">编辑</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" v-if="editFlag">
        <el-form ref="formParamsRef" :model="form" :rules="rules" label-position="left">
          <div class="form-title">评分标准名称</div>
          <el-row style="margin-top: 15px;">
            <el-col :span="16">
              <el-form-item prop="name">
                <el-input v-model="form.name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="flex title-container">
            <div class="left flex">
              <div class="form-title">
                内容完整性评分设计<span>（权重{{ form.contentIntegrityWeight }}%）</span>
              </div>
              <div class="tips">
                内容完整性考察的是学员每页幻灯片的内容讲解是否到位，具体的考察方式和考察内容需要前往幻灯片脚本中的单页评分方式进行设置。
              </div>
            </div>
            <div class="right">
              <el-button class="right-btn" type="primary" link @click="openWeightDialog">修改权重</el-button>
            </div>
          </div>
          <div class="flex title-container">
            <div class="left flex">
              <div class="form-title">
                讲解技巧评分设计<span>（权重{{ form.skillWeight }}%）</span>
              </div>
              <div class="tips">
                讲解技巧考察的是学员对幻灯片整体的讲解节奏把控，可自定义评分维度，请确保所有评分主维度总分和为100分，评分主维度数量限制在3-6个，每个主维度下子维度数量限制在2-5个。
              </div>
            </div>
            <div class="right">
              <el-button class="right-btn" type="primary" link @click="openWeightDialog">修改权重</el-button>
            </div>
          </div>
          <div class="dimension-list">
            <div class="dimension-item dimension-head">
              <div class="dimension-type">评分维度</div>
              <div class="dimension-score">分数</div>
              <div class="dimension-standard">评分细则</div>
            </div>
            <template v-for="(item, index) in form.dimensions" :key="index">
              <!-- 主维度 -->
              <div class="dimension-item">
                <div class="dimension-type dimension-type-main">
                  <img src="@/assets/images/icon_star.svg" class="star" />
                  <el-form-item :prop="`dimensions.${index}.name`" :rules="setRules.name">
                    <el-input v-model="item.name" placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-score">
                  <el-form-item :prop="`dimensions.${index}.fullScore`" :rules="setRules.fullScore">
                    <el-input v-model="item.fullScore" placeholder="0"><template #suffix>分</template></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-standard">
                  <el-form-item :prop="`dimensions.${index}.description`" :rules="setRules.description">
                    <el-input type="textarea" resize="none" autosize v-model="item.description" placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-actions">
                  <el-button link type="primary" @click.stop="addNewSubDimension(index)" text>新增子维度</el-button>
                  <el-popconfirm
                    width="220"
                    @confirm.stop="deleteMainDimension(index)"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定删除吗?"
                    v-if="item.description || item.fullScore || item.name || item.children.length > 0"
                  >
                    <template #reference>
                      <el-button link type="primary" text>删除</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button v-else link type="primary" @click.stop="deleteMainDimension(index)" text>删除</el-button>
                </div>
              </div>
              <!-- 子维度 -->
              <div class="dimension-item" v-for="(child, indexChild) in item.children" :key="indexChild">
                <div class="dimension-type">
                  <el-form-item :prop="`dimensions.${index}.children.${indexChild}.name`" :rules="setRules.name">
                    <el-input style="width: 160px;" v-model="child.name" placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-score">
                  <el-form-item :prop="`dimensions.${index}.children.${indexChild}.fullScore`" :rules="setRules.fullScore">
                    <el-input style="width: 80px;" v-model="child.fullScore" placeholder="0"><template #suffix>分</template></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-standard">
                  <el-form-item :prop="`dimensions.${index}.children.${indexChild}.description`" :rules="setRules.description">
                    <el-input v-model="child.description" resize="none" type="textarea" autosize placeholder="请输入"></el-input>
                  </el-form-item>
                </div>
                <div class="dimension-actions">
                  <el-popconfirm
                    v-if="child.description || child.fullScore || child.name"
                    width="220"
                    @confirm.stop="deleteSubDimension(index, indexChild)"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="确定删除吗?"
                  >
                    <template #reference>
                      <el-button link type="primary" text>删除</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button v-else link type="primary" @click.stop="deleteSubDimension(index, indexChild)" text>删除</el-button>
                </div>
              </div>
              <el-divider />
            </template>

            <el-button type="primary" @click="addDimension">新增主维度</el-button>
          </div>
          <div class="flex" style="justify-content: space-between;space-between;margin-top: 24px;">
            <div class="left">
              <div class="form-title">
                声音流畅度评分设计 <span v-if="voiceSetting">（权重{{ form.voiceWeight }}%）</span>
              </div>
              <div class="tips">声音流畅度考察的是学员讲解的声音是否流畅自然，可从语速、音量、冗余词三个维度进行评分。</div>
            </div>
            <div class="right flex" v-if="voiceSetting" style="margin-right: 60px;">
              <el-button type="primary" link @click="openWeightDialog">修改权重</el-button>
              <el-popconfirm
                width="220"
                placement="top"
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定停用吗?"
                @confirm="initVoice"
                v-if="selectVoice.length > 0"
              >
                <template #reference>
                  <el-button type="primary" link>停用</el-button>
                </template>
              </el-popconfirm>
              <el-button v-else type="primary" link @click="initVoice">停用</el-button>
            </div>
          </div>

          <el-button type="primary" style="margin-top: 18px;" v-if="!voiceSetting" @click="resetVoiceSetting">启用</el-button>
          <template v-else>
            <el-form-item style="margin-top: 12px;" label="启用维度" label-width="100px">
              <el-checkbox-group v-model="selectVoice">
                <div class="flex">
                  <el-checkbox label="1">语速</el-checkbox>
                  <!-- <el-checkbox label="2">停顿</el-checkbox> -->
                  <el-checkbox label="3">音量</el-checkbox>
                  <el-checkbox label="4">冗余词</el-checkbox>
                </div>
              </el-checkbox-group>
            </el-form-item>
            <div class="dimension-list">
              <div class="dimension-item dimension-head" v-if="selectVoice.length > 0">
                <div class="dimension-type">评分维度</div>
                <div class="dimension-score">分数</div>
                <div class="dimension-standard" style=" margin-left: 24px;">评分细则</div>
              </div>
              <template v-for="(item, index) in form.rateDimension" :key="index">
                <div class="dimension-item" v-if="item.type === '1' && selectVoice.includes('1')">
                  <div class="dimension-type flex">
                    <img src="@/assets/images/icon_star.svg" class="star" />
                    语速
                    <el-tooltip placement="top" popper-class="popper">
                      <template #content>
                        <div class="flex" style="flex-direction: column;">
                          <span>设置学员每分钟说话语速要求，需要根据相应的练习场景进行填写。不同场景下常见的语速范围如下：</span>
                          <li>120-150字/分钟：日常交流中最常见的语速，适合轻松的聊天、讨论等场景。</li>
                          <li>140-160字/分钟：适合标准朗读，可以清晰、有节奏地表达内容。</li>
                          <li>140-180字/分钟：这是演讲中较为理想的语速范围。</li>
                          <li>120-140字/分钟：适合一些复杂内容的讲解，需要详细解释内容，语速应适当放慢。</li>
                        </div>
                      </template>
                      <el-icon class="label-tip" style=" margin-left: 4px;">
                        <img src="@/assets/icons/svg/help1.svg" style=" width: 16px; " />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <div class="dimension-score">
                    <el-form-item :rules="setRules.score" :prop="`rateDimension.${index}.score`">
                      <el-input placeholder="0" v-model="item.score"><template #suffix>分</template></el-input>
                    </el-form-item>
                  </div>
                  <div class="dimension-detail flex">
                    语速在
                    <div class="min flex">
                      <el-form-item :rules="setRules.speendMin" :prop="`rateDimension.${index}.min`">
                        <el-input class="small-input" v-model="item.min" placeholder="0"></el-input>
                      </el-form-item>
                    </div>
                    -
                    <div class="max flex">
                      <el-form-item :rules="setRules.speedMax" :prop="`rateDimension.${index}.max`">
                        <el-input class="small-input" v-model="item.max" placeholder="0"></el-input>
                      </el-form-item>
                    </div>
                    字/分钟得满分。
                  </div>
                </div>
                <!-- <div class="dimension-item" v-if="item.type === '2' && selectVoice.includes('2')">
                  <div class="dimension-type ">
                    <img src="@/assets/images/icon_star.svg" class="star" />
                    停顿
                  </div>
                  <div class="dimension-score">
                    <el-form-item :rules="setRules.score" :prop="`rateDimension.${index}.score`">
                      <el-input placeholder="0" v-model="item.score"><template #suffix>分</template></el-input>
                    </el-form-item>
                  </div>

                  <div class="dimension-detail flex">
                    统计＞1.5s的停顿次数，<el-form-item :rules="setRules.pauseMax" :prop="`rateDimension.${index}.max`">
                      <el-input placeholder="0" v-model="item.max" class="small-input"></el-input>
                    </el-form-item>
                    次（含）以内得满分。
                  </div>
                </div> -->

                <div class="dimension-item" v-if="item.type === '3' && selectVoice.includes('3')">
                  <div class="dimension-type flex">
                    <img src="@/assets/images/icon_star.svg" class="star" />
                    音量
                    <el-tooltip placement="top" popper-class="popper">
                      <template #content>
                        <div class="flex" style="flex-direction: column;">
                          <span>设置学员练习时音量的标准。按普通人的听觉，不同声压级的主观感觉如下：</span>
                          <li>0~20分贝：很静，几乎感觉不到。</li>
                          <li>20~40分贝：安静，犹如轻声絮语。</li>
                          <li>40~60分贝：一般，普通室内谈话。</li>
                          <li>60~70分贝：吵闹，如车流的噪声。</li>
                          <li>70~90分贝：很吵，听神经细胞受损，类似装修电钻声。</li>
                          <li>90~100分贝：吵闹加剧，引起噪声性耳聋，飞机引擎的轰鸣声。</li>
                        </div>
                      </template>
                      <el-icon class="label-tip" style=" margin-left: 4px;">
                        <img src="@/assets/icons/svg/help1.svg" style=" width: 16px; " />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <div class="dimension-score">
                    <el-form-item :rules="setRules.score" :prop="`rateDimension.${index}.score`">
                      <el-input placeholder="0" v-model="item.score"><template #suffix>分</template></el-input>
                    </el-form-item>
                  </div>
                  <div class="dimension-detail flex">
                    音量在
                    <div class="min flex">
                      <el-form-item :rules="setRules.voiceMin" :prop="`rateDimension.${index}.min`">
                        <el-input class="small-input" v-model="item.min" placeholder="0"></el-input>
                      </el-form-item>
                    </div>
                    -
                    <div class="max flex">
                      <el-form-item :rules="setRules.voiceMax" :prop="`rateDimension.${index}.max`">
                        <el-input class="small-input" v-model="item.max" placeholder="0"></el-input>
                      </el-form-item>
                    </div>
                    db得满分。
                  </div>
                </div>
                <div class="dimension-item" v-if="item.type === '4' && selectVoice.includes('4')">
                  <div class="dimension-type flex">
                    <img src="@/assets/images/icon_star.svg" class="star" />
                    冗余词
                    <el-tooltip placement="top" popper-class="popper">
                      <template #content>
                        <span
                          >冗余词指不必要的口头禅、语气词、重复词汇，一般表达逻辑不清晰或者不自信时容易出现。常见的冗余词有
                          “嗯”、“啊”、“然后”、“就是说”等等，可自定义，最多可选中10个。</span
                        >
                      </template>
                      <el-icon class="label-tip" style=" margin-left: 4px;">
                        <img src="@/assets/icons/svg/help1.svg" style=" width: 16px; " />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <div class="dimension-score">
                    <el-form-item :rules="setRules.score" :prop="`rateDimension.${index}.score`">
                      <el-input placeholder="0" v-model="item.score"><template #suffix>分</template></el-input>
                    </el-form-item>
                  </div>
                  <div class="dimension-detail flex" style="flex-direction: column;align-items: flex-start;">
                    <div class="flex">
                      统计以下选中的冗余词出现的次数，
                      <div class="max flex">
                        <el-form-item :rules="setRules.pleonasmMax" :prop="`rateDimension.${index}.max`">
                          <el-input class="small-input" placeholder="0" v-model="item.max"></el-input>
                        </el-form-item>
                      </div>
                      次（含）以内得满分。
                    </div>
                    <div class="tag-container">
                      <el-form-item :rules="setRules.redundantWordList" :prop="`rateDimension.${index}.redundantWordList`">
                        <tag-select
                          :min-word-length="1"
                          :max-word-length="4"
                          v-model="item.redundantWordList"
                          :tags="defaultTags"
                          :new-tags="newTags"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </template>
        </el-form>
      </div>
      <div class="box-container" v-else>
        <div class="form-title">
          内容完整性评分设计<span>（权重{{ form.contentIntegrityWeight }}%）</span>
        </div>
        <div class="form-title">
          讲解技巧评分设计<span>（权重{{ form.skillWeight }}%）</span>
        </div>
        <!-- <div class="tips">请确保所有评分主维度总分和为100分，评分主维度数量限制在3-6个，每个主维度下子维度数量限制在2-5个。</div> -->
        <div class="dimension-list">
          <div class="dimension-item dimension-head">
            <div class="dimension-type">评分维度</div>
            <div class="dimension-score">分数</div>
            <div class="dimension-standard">评分细则</div>
          </div>
          <template v-for="(item, index) in form.dimensions" :key="index">
            <!-- 主维度 -->
            <div class="dimension-item">
              <div class="dimension-type dimension-type-main">
                <img src="@/assets/images/icon_star.svg" class="star" />{{
                  item.name }}
              </div>
              <div class="dimension-score">{{ item.fullScore }}</div>
              <div class="dimension-standard">{{ item.description }}</div>
            </div>
            <!-- 子维度 -->
            <div class="dimension-item" v-for="(child, index) in item.children" :key="index">
              <div class="dimension-type">{{ child.name }}</div>
              <div class="dimension-score">{{ child.fullScore }}</div>
              <div class="dimension-standard">{{ child.description }}</div>
            </div>
            <el-divider />
          </template>
        </div>
        <div class="flex" style="justify-content: space-between;" v-if="voiceSetting">
          <div class="left">
            <div class="form-title">声音流畅度评分设计（权重{{ form.voiceWeight }}%）</div>
            <!-- <div class="tips">声音表现力和内容表现力将进行加权计算得出评分报告的总分。例如：综合得分=内容表现力得分*85%+声音表现力得分*15%。</div> -->
          </div>
        </div>

        <!-- <el-button type="primary" style="margin-top: 18px;" v-if="!voiceSetting" @click="voiceSetting = !voiceSetting">启用声音表现力</el-button> -->
        <template v-if="voiceSetting">
          <!-- <el-form-item style="margin-top: 12px;" label="启用维度" label-width="100px">
            <el-checkbox-group v-model="selectVoice">
              <div class="flex">
                <el-checkbox label="1">语速</el-checkbox>
                <el-checkbox label="2">停顿</el-checkbox>
                <el-checkbox label="3">音量</el-checkbox>
                <el-checkbox label="4">冗余词</el-checkbox>
              </div>
            </el-checkbox-group>
          </el-form-item> -->
          <div class="dimension-list">
            <div class="dimension-item dimension-head" v-if="selectVoice.length > 0">
              <div class="dimension-type">评分维度</div>
              <div class="dimension-score">分数</div>
              <div class="dimension-standard" style=" margin-left: 24px;">评分细则</div>
            </div>
            <template v-for="(item, index) in form.rateDimension" :key="index">
              <div class="dimension-item" v-if="item.type === '1' && selectVoice.includes('1')">
                <div class="dimension-type">
                  <img src="@/assets/images/icon_star.svg" class="star" />
                  语速
                </div>
                <div class="dimension-score">
                  {{ item.score }}
                </div>
                <div class="dimension-detail flex">
                  语速在
                  {{ item.min }}-{{ item.max }}字/分钟得满分。
                </div>
              </div>
              <!-- <div class="dimension-item" v-if="item.type === '2' && selectVoice.includes('2')">
                <div class="dimension-type ">
                  <img src="@/assets/images/icon_star.svg" class="star" />
                  停顿
                </div>
                <div class="dimension-score">
                  {{ item.score }}
                </div>

                <div class="dimension-detail flex">统计＞1.5s的停顿次数，{{ item.max }}次（含）以内得满分。</div>
              </div> -->

              <div class="dimension-item" v-if="item.type === '3' && selectVoice.includes('3')">
                <div class="dimension-type ">
                  <img src="@/assets/images/icon_star.svg" class="star" />
                  音量
                </div>
                <div class="dimension-score">
                  {{ item.score }}
                </div>
                <div class="dimension-detail flex">
                  音量在
                  {{ item.min }}-{{ item.max }}db 得满分。
                </div>
              </div>
              <div class="dimension-item" v-if="item.type === '4' && selectVoice.includes('4')">
                <div class="dimension-type ">
                  <img src="@/assets/images/icon_star.svg" class="star" />
                  冗余词
                </div>
                <div class="dimension-score">
                  {{ item.score }}
                </div>
                <div class="dimension-detail flex" style="flex-direction: column;align-items: flex-start;">
                  <div class="flex">
                    统计以下选中的冗余词出现的次数，
                    {{ item.max }}
                    次（含）以内得满分。
                  </div>
                  <div class="tag-container">冗余词: {{ item.redundantWordList?.join('、') }}</div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </el-card>
    <Slider
      ref="sliderRef"
      v-model:visible="voiceWeightDialog"
      @sendContentWight="setVoiceWeight"
      :weightArray="[form.contentIntegrityWeight, form.contentIntegrityWeight + form.skillWeight]"
    />
  </div>
</template>

<script setup name="ScoreStandardDetail" lang="ts">
import { queryDetail, handleSave } from '@/api/scoreStandard';
import { ElMessageBox, ElLoading } from 'element-plus'
import Slider from './slider.vue';
import TagSelect from '@/views/script/components/TagSelect.vue'
import type { FormRules } from 'element-plus'
import { deepClone } from '@/utils';
// import { it } from 'element-plus/es/locale';
const sliderRef = ref()

const btnLoading = ref(false)
const formParamsRef = ref()
// const visibleFlag = ref(false)
const form = ref({
  name: '',
  type: undefined,
  dimensions: [],
  children: [],
  system: true,
  voiceWeight: 0,
  skillWeight: 0,
  contentIntegrityWeight: 0,
  enable: false,
  rateDimension: [
    { type: "1", min: 140, max: 180, enable: true, score: 30 },
    { type: "3", min: 40, max: 60, enable: true, score: 30 },
    { type: "4", redundantWordList: ['呃', '嗯', '啊', '哦', '那个'], max: 3, enable: true, score: 40 },
  ]
})

const oldDimension = ref()
const oldName = ref()


//声音相关
const newTags = ref<{ inputVisible: boolean, value: string }[]>([])
// const array = ref<string[]>([])
const selectVoice = ref<string[]>([])
const voiceSetting = ref(false)
const voiceWeightDialog = ref(false)
const contentWight = ref(85)
const defaultTags = ['呃', '嗯', '啊', '哦', '那个', '然后', '这个', '诶', '呢']

const emits = defineEmits(['change-type'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  scriptType: {
    type: Number
  },
  listType: {
    type: Number
  }
})
const editFlag = ref(props.isEdit)
const initForm = {
  name: '',
  type: undefined,
  dimensions: [{
    name: '',
    fullScore: '',
    description: '',
    children: []
  }],
  contentIntegrityWeight: 50,
  skillWeight: 40,
  voiceWeight: 10,
  enable: true,
  rateDimension: [
    { type: "1", min: 140, max: 180, enable: true, score: 30 },
    { type: "3", min: 40, max: 60, enable: true, score: 30 },
    { type: "4", redundantWordList: ['呃', '嗯', '啊', '哦', '那个'], max: 3, enable: true, score: 40 },
  ]
}

const title = computed(() => {
  return editFlag.value ? '编辑评分标准' : form.value.name
})


const rules = reactive<FormRules<any>>({
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    },
    {
      max: 16,
      message: '最多16个字符',
      trigger: ['blur']
    },
  ],
})
const checkScoreLimit = (rule: any, value: number, callback: any) => {
  // console.log(rule, value)
  if (!rule.field.includes('children')) {
    // 获取当前层数
    const currentLevel = rule.field.match(/\d+/g).join(',')
    console.log(currentLevel)
    //子层的总分
    const subTotal = form.value.dimensions[currentLevel].children.reduce((total: number, item: any) => {
      return total + Number(item.fullScore)
    }, 0)
    console.log(value, subTotal)
    if (Number(value) !== Number(subTotal)) {
      callback(new Error('请确保所有子维度总分和等于主维度'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}



const checkSpeendMinLimit = (rule: any, value: number, callback: any) => {
  if (value < 80) {
    callback(new Error('最小不能小于80字/分钟'))
  } if (value > 300) {
    callback(new Error('最大不能大于300字/分钟'))
  } else if (form.value.rateDimension[0].max) {
    if (Number(value) > Number(form.value.rateDimension[0].max)) {
      callback(new Error('最小值需要小于最大值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

const checkSpeendMaxLimit = (rule: any, value: number, callback: any) => {
  if (value < 80) {
    callback(new Error('最小不能小于80字/分钟'))
  } if (value > 300) {
    callback(new Error('最大不能大于300字/分钟'))
  } else if (form.value.rateDimension[0].min) {
    console.log(value, form.value.rateDimension[0].min)
    if (Number(value) < Number(form.value.rateDimension[0].min)) {
      callback(new Error('最大值需要大于最小值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

const checkPauseMaxLimit = (rule: any, value: number, callback: any) => {
  console.log(value)
  if (value > 1000) {
    callback(new Error('最大不能超过1000'))
  } else {
    callback()
  }
}
const checkVoiceMinLimit = (rule: any, value: number, callback: any) => {
  if (value < 30) {
    callback(new Error('最小不能小于30'))
  } else if (value > 100) {
    callback(new Error('最大不能超过100'))
  } else if (form.value.rateDimension[1].max) {
    if (Number(value) > Number(form.value.rateDimension[1].max)) {
      callback(new Error('最小值需要小于最大值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const checkVoiceMaxLimit = (rule: any, value: number, callback: any) => {
  if (value < 30) {
    callback(new Error('最小不能小于30'))
  } else if (value > 100) {
    callback(new Error('最大不能超过100'))
  } else if (form.value.rateDimension[1].min) {
    if (Number(value) < Number(form.value.rateDimension[1].min)) {
      callback(new Error('最大值需要大于最小值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// const checkPleonasmMinLimit = (rule: any, value: number, callback: any) => {
//   if (value < 1) {
//     callback(new Error('最小不能小于1'))
//   } else if (value > 1000) {
//     callback(new Error('最大不能超过1000'))
//   } else if (form.value.rateDimension[3].max) {
//     if (Number(value) > Number(form.value.rateDimension[3].max)) {
//       callback(new Error('最小值需要小于最大值'))
//     }
//   } else {
//     callback()
//   }
// }
const checkPleonasmMaxLimit = (rule: any, value: number, callback: any) => {
  if (value > 1000) {
    callback(new Error('最大不能超过1000'))
  } else {
    callback()
  }
}

// 定义自定义规则函数
const checkArrayLength = (rule: any, value: any, callback: any) => {
  console.log(value)
  if (value.length < 2 || value.length === 0) {
    callback(new Error('至少选择2个关键词'));
  } else if (value.length > 10) {
    callback(new Error('最多选择10个关键词'));
  } else {
    callback();
  }
};


const setRules = reactive({
  speendMin: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur', 'submit']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur', 'submit']
    },
    {
      validator: checkSpeendMinLimit,
      trigger: ['blur', 'submit']
    },
  ],
  speedMax: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur', 'submit']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur', 'submit']
    },
    {
      validator: checkSpeendMaxLimit,
      trigger: ['blur', 'submit']
    },
  ],
  // pauseMax: [
  //   {
  //     required: true,
  //     message: '请输入',
  //     trigger: ['blur', 'submit']
  //   },
  //   {
  //     pattern: /^([1-9][0-9]*)$/,
  //     message: '请输入大于0的正整数',
  //     trigger: ['blur', 'submit']
  //   },
  //   {
  //     validator: checkPauseMaxLimit,
  //     trigger: ['blur', 'submit']
  //   },
  // ],
  voiceMin: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur', 'submit']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur', 'submit']
    },
    {
      validator: checkVoiceMinLimit,
      trigger: ['blur', 'submit']
    },
  ],
  voiceMax: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur', 'submit']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur', 'submit']
    },
    {
      validator: checkVoiceMaxLimit,
      trigger: ['blur', 'submit']
    },
  ],

  pleonasmMax: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur', 'submit']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur', 'submit']
    },
    {
      validator: checkPleonasmMaxLimit,
      trigger: ['blur', 'submit']
    },
  ],
  redundantWordList: [{ required: true, validator: checkArrayLength, trigger: ['blur', 'submit'] }],
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    },
    {
      max: 16,
      message: '最多16个字符',
      trigger: ['blur']
    },
  ],

  fullScore: [
    {
      required: true,
      message: '请输入分数',
      trigger: ['blur']
    },
    {
      pattern: /^([1-9][0-9]*)$/,
      message: '请输入大于0的正整数',
      trigger: ['blur']
    },
    {
      validator: checkScoreLimit,
      trigger: ['submit']
    },
  ],
  description: [
    { required: true, message: '请输入评分标准的描述', trigger: 'blur' },
    { max: 500, message: '最多500个字符', trigger: 'blur' }
  ]
})

const back = () => {
  // console.log(oldDimension.value, form.value.dimensions)
  let bool = JSON.stringify(JSON.parse(oldDimension.value).dimensions) !== JSON.stringify(form.value.dimensions)
  let bool1 = oldName.value !== form.value.name
  if (bool || bool1) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}

const checkSave = () => {
  if (editFlag.value) {
    let bool = JSON.stringify(JSON.parse(oldDimension.value).dimensions) !== JSON.stringify(form.value.dimensions)
    let bool1 = oldName.value !== form.value.name
    if (bool || bool1) {
      ElMessageBox.confirm(
        '未保存的内容将丢失',
        '确定要返回吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          editFlag.value = false
          _getDetail()
        })
        .catch(() => {
        })
    } else {
      editFlag.value = false
      _getDetail()

    }
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}

const addDimension = () => {
  if (form.value.dimensions.length >= 6) {
    ElMessage({
      message: '评分主维度数量限制在3-6个',
      type: 'warning'
    })
    return
  }
  form.value.dimensions.push({
    name: '',
    fullScore: undefined,
    description: '',
    children: []
  })
}

// 新增子维度
const addNewSubDimension = (index: number) => {
  if (form.value.dimensions[index].children.length >= 5) {
    ElMessage({
      message: '子维度的数量为2-5个',
      type: 'warning'
    })
    return
  }
  // proxy.$refs['formParamsRef'].resetFields('dimensions.' + index + '.fullScore')

  form.value.dimensions[index].children.push({
    name: '',
    fullScore: undefined,
    description: ''
  })

  // console.log(form.value.dimensions[index].children.length)
}

const deleteMainDimension = (index: number) => {
  if (form.value.dimensions.length <= 3) {
    ElMessage({
      message: '评分主维度数量限制在3-6个',
      type: 'warning'
    })
    return
  }
  form.value.dimensions.splice(index, 1)
}

const deleteSubDimension = (index: number, indexChild: number) => {
  form.value.dimensions[index].children.splice(indexChild, 1)
  proxy.$refs['formParamsRef'].validateField('dimensions.' + index + '.fullScore')
}

const _getDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
  })
  let res = await queryDetail(props.id);
  nextTick(() => {
    loading.close()
    oldDimension.value = res.data.dimensions
    oldName.value = res.data.name
    form.value.dimensions = JSON.parse(res.data.dimensions).dimensions
    form.value.name = res.data.name
    form.value.system = res.data.system
    form.value.type = res.data.type
    // console.log(JSON.parse(res.data.voiceRateDimension).voiceWeight)
    form.value.voiceWeight = res.data.voiceRateDimension ? (JSON.parse(res.data.voiceRateDimension)?.enable ? JSON.parse(res.data.voiceRateDimension)?.voiceWeight : 0) : 0

    // contentWight.value = 100 - form.value.voiceWeight
    form.value.skillWeight = JSON.parse(res.data.dimensions).skillWeight || 0
    console.log(JSON.parse(res.data.dimensions).skillWeight)

    form.value.contentIntegrityWeight = JSON.parse(res.data.dimensions).skillWeight ? 100 - (form.value.skillWeight + form.value.voiceWeight) : 100 - form.value.voiceWeight
    console.log(form.value.contentIntegrityWeight)

    voiceSetting.value = res.data.voiceRateDimension ? JSON.parse(res.data.voiceRateDimension).enable : false
    form.value.rateDimension = res.data.voiceRateDimension ? JSON.parse(res.data.voiceRateDimension).rateDimension : [
      { type: "1", min: 140, max: 180, enable: true, score: 30 },
      { type: "3", min: 40, max: 60, enable: true, score: 30 },
      { type: "4", redundantWordList: ['呃', '嗯', '啊', '哦', '那个'], max: 3, enable: true, score: 40 },
    ]
    if (form.value.rateDimension[2].redundantWordList && form.value.rateDimension[2].redundantWordList.length > 0) {
      (form.value.rateDimension[2].redundantWordList as string[]).forEach((item: string) => {
        if (!defaultTags.includes(item)) {
          newTags.value.push({ inputVisible: false, value: item })
        }
      })
    }
    selectVoice.value = res.data.voiceRateDimension ? JSON.parse(res.data.voiceRateDimension).rateDimension.filter((item: any) => item.enable).map((item: any) => item.type) : []
    // oldFromInfo.value = deepClone(res.data)
  })
}

const openWeightDialog = () => {
  voiceWeightDialog.value = true
  sliderRef.value.initWeight(form.value.contentIntegrityWeight, form.value.skillWeight, voiceSetting.value)
}




const handleSubmit = async () => {
  if (form.value.dimensions.length > 6 || form.value.dimensions.length < 3) {
    ElMessage({
      message: '评分主维度数量限制在3-6个',
      type: 'warning'
    })
    return
  }
  for (let i = 0; i < form.value.dimensions.length; i++) {
    const item = form.value.dimensions[i]
    if (item?.children.length > 5 || item?.children.length < 2) {
      ElMessage({
        message: '请确保每个主维度下，子维度数量为2-5个',
        type: 'warning'
      })
      return
    }
  }
  const totalScore = form.value.dimensions.reduce((total: number, item: any) => {
    return total + Number(item.fullScore)
  },
    0)
  if (totalScore !== 100) {
    ElMessage({
      message: '请确保内容完整性评分设计所有维度总和为100分',
      type: 'warning'
    })
    return
  }
  if (voiceSetting.value) {
    const voiceScore = form.value.rateDimension.reduce((total: number, item: any) => {
      return total + Number(item.score)
    },
      0)
    if (voiceScore !== 100) {
      ElMessage({
        message: '请确保声音流畅度评分设计所有维度总和为100分',
        type: 'warning'
      })
      return
    }
    selectVoice.value.forEach(element => {
      const index = form.value.rateDimension.findIndex((item: any) => item.type === element);
      form.value.rateDimension[index].enable = true
    });
  }
  if (voiceSetting.value ? form.value.voiceWeight === 0 || form.value.skillWeight === 0 || form.value.contentIntegrityWeight === 0:form.value.skillWeight === 0 || form.value.contentIntegrityWeight === 0) {
    ElMessage({
      message: '权重不允许设置为0',
      type: 'warning'
    })
    return
  }
  if (voiceSetting.value ? form.value.skillWeight + form.value.voiceWeight + form.value.contentIntegrityWeight !== 100 : form.value.skillWeight + form.value.contentIntegrityWeight !== 100) {
    ElMessage({
      message: '评分设计权重总和请等于100分',
      type: 'warning'
    })
    return
  }

  const voiceRateDimension = {
    enable: voiceSetting.value,
    voiceWeight: voiceSetting.value ? form.value.voiceWeight : 0,
    rateDimension: form.value.rateDimension
  }

  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const formData = deepClone(form.value)
      delete formData.rateDimension
      const data = {
        ...form.value,
        id: props.id,
        voiceRateDimension: JSON.stringify(voiceRateDimension)
      }
      data.dimensions = JSON.stringify({
        dimensions: data.dimensions,
        contentIntegrityWeight: form.value.contentIntegrityWeight,
        skillWeight: form.value.skillWeight
      })
      btnLoading.value = true

      await handleSave(data).then((res) => {
        proxy.$modal.msgSuccess('保存成功');
        emits('change-type', { flagType: 'list', })

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  })


}

_getDetail()

// 声音相关
const initVoice = () => {
  voiceSetting.value = false
  selectVoice.value = []
  form.value.rateDimension = deepClone(initForm.rateDimension)
  form.value.contentIntegrityWeight += form.value.voiceWeight
  form.value.voiceWeight = 0
}
const setVoiceWeight = (val: number[]) => {
  if (voiceSetting.value) {
    form.value.contentIntegrityWeight = val[0]
    form.value.skillWeight = val[1] - val[0]
    form.value.voiceWeight = 100 - ((val[1] - val[0]) + val[0])
  } else {
    form.value.contentIntegrityWeight = val[0]
    form.value.skillWeight = 100 - val[0]
    form.value.voiceWeight = 0
  }
}
const openVoiceSetting = () => {
  selectVoice.value = ['1', '3', '4']
  voiceSetting.value = true
  form.value.rateDimension = deepClone(initForm.rateDimension)
}

const resetVoiceSetting = () => {
  selectVoice.value = ['1', '3', '4']
  // console.log(form.value.contentIntegrityWeight , form.value.skillWeight)
  if (form.value.contentIntegrityWeight + form.value.skillWeight === 100) {
    // 判断总和是否为100，如果100移10分给声音权重
    form.value.voiceWeight = 10
    if (form.value.contentIntegrityWeight > form.value.voiceWeight) {
      form.value.contentIntegrityWeight = form.value.contentIntegrityWeight - form.value.voiceWeight
    } else {
      form.value.skillWeight = form.value.skillWeight - form.value.voiceWeight
    }
  } else {
    // 判断总和是否不为100，剩下分给声音权重
    form.voiceWeight = 100 - form.value.contentIntegrityWeight - form.value.skillWeight
  }
  console.log(form.value.skillWeight)
  voiceSetting.value = true
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  // margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}


@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;

  }
}

.box-container {
  display: flex;
  flex-direction: column;
  padding: 0 30px 30px 30px;
  gap: 12px 0;
  width: 100%;
  align-items: flex-start;
  margin: 0 auto;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;
  }
}



:deep(.el-card) {
  // height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 4px - 162px);
    overflow-y: auto;
  }
}



:deep(.el-form) {
  width: 100%;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}


.delete {
  cursor: pointer;
}


.tips {
  color: #999999;
  font-size: 12px;
  padding: 12px 0 4px;
}

:deep(.el-form-item--default) {
  margin-bottom: 0;
}

.form-title {
  font-size: 16px;
  font-weight: bold;
  margin-top: 20px;
}

.dimension-list {
  width: 100%;
}

.dimension-item {
  display: flex;
  padding: 8px 0;
  gap: 0 15px;
  align-items: flex-start;
}

.name {
  :deep(.el-form-item__error) {
    position: absolute;
    width: 300px !important;
    left: 0 !important;
    font-weight: normal;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
  cursor: pointer;
}

.dimension-type {
  width: 200px;
  padding-left: 34px;
  position: relative;
  // font-size: 14px;

  &-main {
    font-weight: bold;
  }

  :deep(.el-form-item__error) {
    position: absolute;
    width: 300px !important;
    left: 0 !important;
    font-weight: normal;
  }
}

.dimension-score {
  width: 80px;

  :deep(.el-form-item__error) {
    position: absolute;
    width: 300px !important;
    right: 0;
    text-align: right;
    left: auto !important;
  }
}

.dimension-detail {
  flex: 1;
  // font-size: 14px;
  color: #333;
  // align-items: flex-start;
  justify-content: flex-start;
  // align-items: center;
  margin-left: 24px;

  .min {
    :deep(.el-form-item__error) {
      position: absolute;
      width: 300px !important;
      left: -55px !important;
      font-weight: normal;
    }
  }

  .max {
    :deep(.el-form-item__error) {
      position: absolute;
      width: 300px !important;
      left: 0px !important;
      font-weight: normal;
    }
  }
}

.dimension-standard {
  flex: 1;
}

:deep(.el-textarea) {
  .el-textarea__inner {
    min-height: 34px !important;
    line-height: 34px !important;
    padding: 0 11px;
  }
}

.dimension-actions {
  flex: 0 0 180px;
  display: flex;
  align-items: center;
}

.dimension-head {
  margin-top: 4px;
  font-size: 14px;
  color: #999999;
}

.star {
  width: 24px;
  height: 24px;
  position: absolute;
  left: 0;
}

.el-divider--horizontal {
  border-top: 1px #f3f3f3 var(--el-border-style);
  margin: 8px 0;
}

.small-input {
  width: 50px;
}

.tag-container {
  margin-top: 20px;
}

.title-container {
  justify-content: space-between;

  .left {
    flex-direction: column;
    align-items: flex-start;
  }

  .right-btn {
    margin-right: 60px;
  }

  .right {
    width: 180px;
  }
}
</style>
