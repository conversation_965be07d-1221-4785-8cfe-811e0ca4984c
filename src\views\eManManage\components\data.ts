export const occupationList = ['执业医师', '执业药师', '护士', '技师', '药店职员'];
export const occupationOtherList = ['患者'];
export const occupationIntelligenceList = ['学术经理', '资深销售', '资深行业顾问'];
export const targetList = ['医药代表', '药店职员', '企业职员'];
export const shopLevelList = ['店长', '普通店员'];
export const levelExcutiveList = ['营销负责人'];
export const levelSellerList = ['VP', '总监', '经理', '无'];
export const issueTags = [
  '适应症',
  '作用',
  '安全性',
  '副作用',
  '禁忌症',
  '适用人群',
  '特殊人群用药',
  '作用机制',
  '相互作用',
  '主要成分',
  '用法用量',
  '临床数据',
  '临床反馈',
  '药品优势',
  '价格'
];
export const locationTags = ['医院', '药店', '药房', '诊所', '办公室', '会议室', '咖啡厅', '实验室', '康复中心', '卫生站', '护理院'];
export const titleList = ['医士', '医师', '主治医师', '副主任医师', '主任医师'];

export function convertArrayToObject(selectedItems: string[]) {
  const newObj: any = {};
  selectedItems.forEach((item) => {
    newObj[item] = undefined;
  });
  return newObj;
}
