<template>
  <div>
    <el-dialog ref="formDialogRef" v-model="visibleFlag" width="800px" append-to-body :close-on-click-modal="false" :show-close="false">
      <template #title>
        {{ title }}
        <div style="font-size: 14px;color: #999999;margin-top: 12px;">可查看上传的音频文件和转成文字的音频内容</div>
      </template>
      <div class="box-container">
        <div class="upload-inner">
          <el-form ref="formParamsRef" label-width="100px" label-position="left">
            <div class="merge-method" @click.stop>
              <el-form-item prop="mergeMethod" label-width="150px" label-position="left">
                <template #label>
                  <span class="label-text">音频文件</span>
                </template>
                <div class="upload-file">
                  <div class="file-container flex">
                    <div class="left flex">
                      <img src="@/assets/icons/svg/voice2.svg" />
                      {{ file.name }}
                    </div>
                    <div class="right flex" style="gap: 0 12px;">
                      <el-tooltip content="下载音频文件" placement="top">
                        <img
                          @click="downloadVioce(file.url)"
                          class="export"
                          style="width: 20px;cursor: pointer;"
                          src="@/assets/icons/png/export.png"
                        />
                      </el-tooltip>
                      <el-tooltip content="删除" placement="top">
                        <el-icon @click="removeVoice" style="cursor: pointer;" size="20">
                          <Close />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>

            <div class="merge-method" @click.stop>
              <el-form-item prop="mergeMethod" label-width="150px" label-position="left">
                <template #label>
                  <span class="label-text flex">音频内容</span>
                </template>
                <div class="content" style="white-space: pre-wrap;">
                  {{ file.content }}
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visibleFlag = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { getVoiceDetail, removeKnowledgeBaseItemVoice } from "@/api/knowledgeBase";
import { KnowledgeBaseItemVoiceVo } from "@/api/knowledgeBase/types"
import { ElLoading } from 'element-plus'
const emits = defineEmits(['update:visible', 'success'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import { ElMessageBox } from "element-plus";
const file = ref<KnowledgeBaseItemVoiceVo>({} as KnowledgeBaseItemVoiceVo)


// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '查看音频'
  },
  activeId: {
    type: String,
    default: ''
  },

})

// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const _getVoiceDetail = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '加载中',
    target: '.box-container',
  })
  const res = await getVoiceDetail({ id: props.activeId })

  loadingInstance.close()
  file.value = res.data
}
watch(() => props.visible, val => {
  if (val) {
    _getVoiceDetail()
  }
})
const downloadVioce = (url: string) => {
  window.open(url)
}


const removeVoice = async () => {
  ElMessageBox.confirm(
    '确定要删除该音频吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      await removeKnowledgeBaseItemVoice(props.activeId).then((res) => {
        proxy.$modal.msgSuccess('删除成功');
        visibleFlag.value = false
        emits('success')
      }).finally(() => {
      });
    })
    .catch(() => {
    })
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  // display: flex;
  // flex-direction: column;
  position: relative;
  max-height: 500px;
}

.upload-file-container {
  width: 100%;
  position: absolute;
  z-index: 3;
}

.flex {
  display: flex;
  align-items: center;
  gap: 0 10px;
}

.upload-file {
  width: 100%;
}

.upload-inner {
  // position: absolute;
  padding: 16px;
  width: 100%;
  // z-index: 4;
}


.upload-file-uploader {
  margin-bottom: 5px;
}


.file-container {
  padding: 12px;
  width: 100%;
  background-color: var(--el-color-primary-light-9);
  box-sizing: border-box;

  .left {
    width: 90%;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
}
</style>
