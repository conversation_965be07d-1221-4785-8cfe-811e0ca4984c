<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container" v-loading="loading">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="上级部门" prop="parentId">
                <el-tree-select
                  style="width: 100%;"
                  v-model="formParams.parentId"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  placeholder="选择上级部门"
                  check-strictly
                  :default-expand-all="true"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="部门名称" prop="deptName">
                <el-input v-model="formParams.deptName" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { getDeptTreeList, saveDept, checkNameUnique } from "@/api/department/index"
import { TreeLong, DeptFormVo } from "@/api/department/types";
import { reactive, ref } from 'vue'
// import { saveOrUpdateUser } from '@/api/user'
import {departmentTreeDisable} from '@/utils'

import { ElMessageBox } from 'element-plus'
import type { FormRules } from 'element-plus'
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success', 'freezeUser'])
const formParamsRef = ref()
const btnLoading = ref(false)
const deptOptions = ref<TreeLong[]>([])
const initForm = {
  deptName: '',
  parentId: ''
}
const formParams = ref<DeptFormVo>(initForm)
const loading = ref(false)
const userStore = useUserStore();

interface RuleForm {
  deptName: string
  parentId: string
}
const checkTextLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('名称不能超过64个字符'))
  } else {
    callback()
  }
}

const checkSameNameLimit = async (rule: any, value: any, callback: any) => {
  if (formParams.value.deptName.length > 0 && formParams.value.parentId) {
    const res = await checkNameUnique({ name: formParams.value.deptName, id: null,parentId:formParams.value.parentId})
    if (!res.data) {
      callback(new Error('部门已存在'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}




const rules = reactive<FormRules<RuleForm>>({
  deptName: [
    { required: true, message: '部门名称不能为空', trigger: 'blur' },
    {
      validator: checkTextLimit,
      trigger: 'blur'
    },
    {
      validator: checkSameNameLimit,
      trigger: 'blur'
    },
  ],
  parentId: [
    { required: true, message: '请选择上级部门', trigger: ['blur', 'change'] },

  ],
})


// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增'
  },

})


watch(() => props.visible, val => {
  if (val) {
    _getDeptTreeList()
  }
})


const _getDeptTreeList = async () => {
  loading.value = true
  const res = await getDeptTreeList()
  if (res.data) {
    deptOptions.value = userStore.cpFlag ? departmentTreeDisable(res.data) : res.data
  }
  loading.value = false
}

// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})




const back = () => {
  let bool = false
  Object.keys(formParams.value).forEach((key) => {
    console.log(JSON.stringify(formParams.value[key]))
    if (
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]'
    ) {
      bool = true;
      return;
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}

const closeDialog = () => {
  formParamsRef.value.resetFields()
  formParams.value = initForm
  visibleAdd.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
      };
      await saveDept(params).then(() => {
        proxy.$modal.msgSuccess('保存成功');
        closeDialog()
        emits('success')

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}
</style>
