<template>
  <el-space>
    <el-button type="primary" @click="handleRewrite">AI改写</el-button>
    <el-button @click="handleInput">直接输入</el-button>
  </el-space>
</template>
<script lang="ts" setup name="KeyIssue">
import { computed } from 'vue'
import { useFormItem } from 'element-plus'

const props = defineProps<{modelValue: string}>()

const emits = defineEmits(['update:modelValue'])

const { formItem } = useFormItem()
  const value = computed({
      get() {
          return props.modelValue
      },
      set(val) {
          emits('update:modelValue', val)
          formItem?.validate('blur')
      }
  })

  const handleRewrite = () => {

  }

  const handleInput = (e: any) => {

  }
</script>
