<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">管理员配置</span>
          <el-row :gutter="10">
            <el-col :span="1.5">
              <el-button type="primary" icon="Plus" @click="handleAdd()" v-has-permi="['system:user:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5" v-if="!userStore.enterpriseFlag">
              <el-button type="success" plain @click="handleUpdate()" :disabled="single" v-has-permi="['system:user:add']" icon="Edit">
                修改
              </el-button>
            </el-col>
            <el-col :span="1.5" v-if="!userStore.enterpriseFlag">
              <el-button type="danger" plain @click="handleDelete()" :disabled="multiple" v-has-permi="['system:user:delete']" icon="Delete">
                删除
              </el-button>
            </el-col>
            <el-col :span="1.5" v-if="!userStore.enterpriseFlag">
              <el-dropdown class="mt-[1px]">
                <el-button plain type="info">
                  更多
                  <el-icon class="el-icon--right"><arrow-down /></el-icon
                ></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="importTemplate" icon="Download">下载模板</el-dropdown-item>
                    <el-dropdown-item @click="handleImport" icon="Top"> 导入数据</el-dropdown-item>
                    <el-dropdown-item @click="handleExport" icon="Download"> 导出数据</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-col>
            <right-toolbar
              v-if="!userStore.enterpriseFlag"
              v-model:showSearch="showSearch"
              @queryTable="getList"
              :columns="columns"
              :search="true"
            ></right-toolbar>
          </el-row>
        </div>
      </template>

      <el-row :gutter="20">
        <!-- 部门树 -->
        <el-col :lg="4" :xs="24" style="" v-if="!userStore.enterpriseFlag">
          <el-card shadow="none">
            <el-input v-model="deptName" placeholder="请输入部门名称" prefix-icon="Search" clearable />
            <el-tree
              class="mt-2"
              node-key="id"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              highlight-current
              default-expand-all
              @node-click="handleNodeClick"
            />
          </el-card>
        </el-col>
        <el-col :lg="userStore.enterpriseFlag ? 24 : 20" :xs="24">
          <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div class="mb-[10px]" v-show="showSearch" v-if="!userStore.enterpriseFlag">
              <el-card shadow="none">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
                  <el-form-item label="账号" prop="userName">
                    <el-input
                      v-model="queryParams.userName"
                      placeholder="请输入账号"
                      aut="false"
                      clearable
                      style="width: 240px"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="手机号码" prop="phonenumber">
                    <el-input
                      v-model="queryParams.phonenumber"
                      placeholder="请输入手机号码"
                      clearable
                      style="width: 240px"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>

                  <el-form-item label="状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
                      <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="创建时间" style="width: 308px;">
                    <el-date-picker
                      v-model="dateRange"
                      value-format="YYYY-MM-DD"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" icon="Search">搜索</el-button>
                    <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>
          </transition>

          <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" v-if="!userStore.enterpriseFlag" />
            <!-- <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" /> -->
            <el-table-column
              label="用户昵称"
              align="center"
              key="nickName"
              prop="nickName"
              v-if="columns[2].visible"
              width="120"
              :show-overflow-tooltip="true"
            />

            <el-table-column label="管理部门" align="center" key="userDeptNames" prop="userDeptNames" v-if="columns[4].visible" />
            <el-table-column label="角色" align="center" key="roleName" prop="roleName" v-if="columns[4].visible" width="120" />
            <el-table-column
              label="账号"
              width="180"
              align="center"
              key="userName"
              prop="userName"
              v-if="columns[1].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="部门"
              align="center"
              key="deptName"
              prop="dept.deptName"
              v-if="columns[3].visible && !userStore.enterpriseFlag"
              :show-overflow-tooltip="true"
            />

            <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[6].visible" width="160">
              <template #default="scope">
                <span v-formatTime="scope.row.createTime"></span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" key="status" width="120" v-if="columns[5].visible">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  :disabled="scope.row.roles[0].roleKey === 'admin'"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="180" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="修改" placement="top" v-if="scope.row.userId !== 1">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:user:edit']"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top" v-if="scope.row.userId !== 1">
                  <el-button
                    link
                    :disabled="scope.row.roles[0].roleKey === 'admin'"
                    type="primary"
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['system:user:remove']"
                  ></el-button>
                </el-tooltip>

                <el-tooltip content="重置密码" placement="top" v-if="scope.row.userId !== 1">
                  <el-button link type="primary" icon="Key" @click="handleResetPwd(scope.row)" v-hasPermi="['system:user:resetPwd']"></el-button>
                </el-tooltip>

                <el-tooltip content="分配角色" placement="top" v-if="scope.row.userId !== 1 && !userStore.enterpriseFlag">
                  <el-button link type="primary" icon="CircleCheck" @click="handleAuthRole(scope.row)" v-hasPermi="['system:user:edit']"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- 添加或修改用户配置对话框 -->
      <el-dialog
        ref="formDialogRef"
        :title="dialog.title"
        v-model="dialog.visible"
        width="700px"
        append-to-body
        @close="closeDialog"
        :close-on-click-modal="false"
      >
        <el-form :model="form" :rules="rules" ref="userFormRef" label-width="80px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户昵称" prop="nickName">
                <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="角色" prop="roleIds">
                <el-select v-model="form.roleIds" multiple placeholder="请选择" :disabled="isAdmin">
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.roleId"
                    :label="item.roleName"
                    v-show="item.roleKey !== 'admin'"
                    :value="item.roleId"
                    :disabled="item.status == '1'"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="!userStore.enterpriseFlag">
            <el-col :span="24">
              <el-form-item label="手机号码" prop="phonenumber">
                <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item v-if="form.userId == undefined" label="账号" prop="userName">
                <el-input v-model="form.userName" autocomplete="off" placeholder="请输入账号" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
                <el-input
                  v-model="form.password"
                  autocomplete="new-password"
                  placeholder="请输入用户密码"
                  type="password"
                  maxlength="20"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item prop="userDeptIds" label="管理部门">
                <!-- <el-tree-select
                  v-model="form.userDeptIds"
                  :data="selectDeptOptions"
                  node-key="id"
                  check-on-click-node
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="2"
                  multiple
                  show-checkbox
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  placeholder="请选择部门"
                  :default-expand-all="true"
                /> -->

                <!-- <el-popover placement="bottom" :width="700" trigger="click">
                  <template #reference>
                    <div style="width:100%;display: flex;">
                      <el-button v-click-outside="onClickOutside" :disabled="isAdmin">请选择部门</el-button>
                    </div>
                  </template>

                </el-popover> -->
                <!-- <div>
                  {{ form.userDeptNames?.join(',') }}
                </div> -->

                <div ref="scrollDiv" class="tree-border">
                  <el-tree
                    show-checkbox
                    @check-change="checkChange"
                    ref="treeRef"
                    node-key="id"
                    check-strictly
                    :data="selectDeptOptions"
                    :props="{ value: 'id', label: 'label', children: 'children' }"
                    :default-expand-all="false"
                    :default-expanded-keys="defaultExpandedKeys"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="!userStore.enterpriseFlag">
            <el-col :span="24">
              <el-form-item label="用户性别">
                <el-select v-model="form.sex" placeholder="请选择">
                  <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
            dict.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="!userStore.enterpriseFlag">
            <el-col :span="24">
              <el-form-item label="岗位">
                <el-select v-model="form.postIds" multiple placeholder="请选择">
                  <el-option
                    v-for="item in postOptions"
                    :key="item.postId"
                    :label="item.postName"
                    :value="item.postId"
                    :disabled="item.status == '1'"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="归属部门" prop="deptId">
                <el-tree-select
                  v-model="form.deptId"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  placeholder="请选择归属部门"
                  check-strictly
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="!userStore.enterpriseFlag">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancel()">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 用户导入对话框 -->
      <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body :close-on-click-modal="false">
        <el-upload
          ref="uploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload">
            <i-ep-upload-filled />
          </el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="text-center el-upload__tip">
              <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
            </div>
          </template>
        </el-upload>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="upload.open = false">取 消</el-button>
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="User" lang="ts">
import { ClickOutside as vClickOutside } from "element-plus"
import api from "@/api/system/user"
import { UserForm, UserQuery, UserVO } from '@/api/system/user/types';
import { treeselect } from "@/api/system/dept";
import { DeptVO } from "@/api/system/dept/types";
import { TreeLong } from "@/api/department/types";
import { RoleVO } from "@/api/system/role/types";
import { PostVO } from "@/api/system/post/types";
import { to } from "await-to-js";
import { globalHeaders } from "@/utils/request";
// import { FormValidators } from '@/utils/validate'
import { useUserStore } from '@/store/modules/user';
const defaultExpandedKeys=ref<string[]>([])
const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { sys_normal_disable, sys_user_sex } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'sys_user_sex'));
const userStore = useUserStore();
const isAdmin = ref(false)
const userList = ref<UserVO & { roleName: string }[]>();
const loading = ref(true);
const showSearch = ref(true)
const ids = ref<UserVO[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const deptName = ref('');
const deptOptions = ref<DeptVO[]>([]);
const initPassword = ref<string>('');
const postOptions = ref<PostVO[]>([]);
const roleOptions = ref<RoleVO[]>([]);
const scrollDiv = ref()
import { ArrowDown } from '@element-plus/icons-vue'
const treeRef = ref()
const selectDeptOptions = ref<TreeLong[]>([])
/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
})
// 列显隐信息
const columns = ref<FieldOption[]>([
  { key: 0, label: `用户编号`, visible: false, children: [] },
  { key: 1, label: `账号`, visible: true, children: [] },
  { key: 2, label: `用户昵称`, visible: true, children: [] },
  { key: 3, label: `部门`, visible: true, children: [] },
  { key: 4, label: `角色`, visible: true, children: [] },
  { key: 5, label: `状态`, visible: true, children: [] },
  { key: 6, label: `创建时间`, visible: true, children: [] }
])

// console.log()

const deptTreeRef = ref<ElTreeInstance>();
const queryFormRef = ref<ElFormInstance>();
const userFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();
const formDialogRef = ref<ElDialogInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: UserForm = {
  userId: undefined,
  deptId: undefined,
  userName: '',
  nickName: undefined,
  password: '',
  phonenumber: undefined,
  email: undefined,
  sex: undefined,
  status: "0",
  remark: '',
  userDeptIds: [],
  postIds: [],
  roleIds: [],
  userDeptNames: []
}


const checkRole = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error('请选择角色'));
  } else {
    callback();
  }
}

const data = reactive<PageData<UserForm, UserQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    phonenumber: '',
    status: '',
    deptId: ''
  },
  rules: {
    userDeptIds: [
      { required: true, type: 'array', message: "管理部门不能为空", trigger: ["blur", "change"] },
    ],
    userName: [{ required: true, message: "账号不能为空", trigger: "blur" },
    { min: 2, max: 20, message: "账号长度必须介于 2 和 20 之间", trigger: "blur" },
    { pattern: /^[A-Za-z0-9]+$/, message: "请输入数字和字母", trigger: "blur" },
    ],
    roleIds: [
      { required: true, type: 'array', message: "用户角色不能为空", trigger: "blur" },
      { validator: checkRole, trigger: "change" }
    ],
    nickName: [{ required: true, message: "用户昵称不能为空", trigger: "blur" },
    { min: 1, max: 32, message: "用户昵称长度最多32个字符", trigger: "blur" }
    ],
    password: [{ required: true, message: "用户密码不能为空", trigger: "blur" }, { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" }],
    email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
    phonenumber: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
  }
})

const { queryParams, form, rules } = toRefs<PageData<UserForm, UserQuery>>(data)

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}
/** 根据名称筛选部门树 */
watchEffect(
  () => { deptTreeRef.value?.filter(deptName.value); },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

const checkChange = (node, checked) => {
  const setChecked = (arr, ifChecked) => {
    arr?.map((item) => {
      treeRef.value.setChecked(item.id, ifChecked);
      if (item?.children?.length) {
        setChecked(item?.children, ifChecked);
      }
    });
  };
  //如果为取消
  if (checked === false) {
    //如果当前节点有子集
    //循环子集将他们的选中取消
    //修复子父级勾选子集勾选问题
    setChecked(node?.children ?? [], false);
  } else {
    //否则(为选中状态)
    //判断父节点packageType 是否为First
    setChecked(node?.children ?? [], true);
  }



  const array = treeRef.value!.getCheckedNodes()
  form.value.userDeptNames = array.map((item: any) => item.label)
}

/** 查询部门下拉树结构 */
const getTreeSelect = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
};

/** 查询用户列表 */
const getList = async () => {
  loading.value = true;
  const res = await api.listUser(proxy?.addDateRange(queryParams.value, dateRange.value));
  loading.value = false;
  const newArray = [...res.rows]
  newArray.forEach((element: any) => {
    const roleNameArray = element.roles.map((item: any) => {
      return item.roleName
    })
    element.roleName = roleNameArray.join('、')
  });
  userList.value = newArray;
  total.value = res.total;
}

/** 节点单击事件 */
const handleNodeClick = (data: DeptVO) => {
  queryParams.value.deptId = data.id;
  handleQuery()
}
const _getDeptTreeList = async () => {
  const res = await userStore.getDeptTreeListFn()
  if (res) {
    console.log(isAdmin.value)
    nextTick(() => {
      selectDeptOptions.value = res
      defaultExpandedKeys.value=res.length>0?[res[0].id]:[]

    })

  }
}

const addDisabledField = (node: any, disabled: boolean) => {
  // 为当前节点添加level字段
  node.disabled = disabled;
  // 如果有子节点，递归处理每个子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      addDisabledField(child, disabled);
    });
  }
}


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', '']
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
}

/** 删除按钮操作 */
const handleDelete = async (row?: UserVO) => {
  const userIds = row?.userId || ids.value.map((item) => item.userId);
  const userName = row?.userName || ids.value.map((item) => item.userName);
  const [err] = await to(proxy?.$modal.confirm('是否确认删除账号为"' + userName + '"的数据项？') as any);
  if (!err) {
    await api.delUser(userIds);
    await getList();
    proxy?.$modal.msgSuccess("删除成功");
  }
}

/** 用户状态修改  */
const handleStatusChange = async (row: UserVO) => {
  let text = row.status === "0" ? "启用" : "停用"
  try {
    await proxy?.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗?');
    await api.changeUserStatus(row.userId, row.status);
    proxy?.$modal.msgSuccess(text + "成功");
  } catch (err) {
    row.status = row.status === "0" ? "1" : "0";
  }
}
/** 跳转角色分配 */
const handleAuthRole = (row: UserVO) => {
  const userId = row.userId;
  router.push("/system/user-auth/role/" + userId);
}

/** 重置密码按钮操作 */
const handleResetPwd = async (row: UserVO) => {
  const [err, res] = await to(ElMessageBox.prompt('请输入"' + row.userName + '"的新密码', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    inputPattern: /^.{5,20}$/,
    inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
  }))
  if (!err) {
    await api.resetUserPwd(row.userId, res.value);
    proxy?.$modal.msgSuccess("修改成功，新密码是：" + res.value);
  }
}

/** 选择条数  */
const handleSelectionChange = (selection: UserVO[]) => {
  ids.value = selection;
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = "用户导入";
  upload.open = true;
}
/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download("system/user/export", {
    ...queryParams.value,
  }, `user_${new Date().getTime()}.xlsx`);
};
/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download("system/user/importTemplate", {
  }, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
}
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
}

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

/** 初始化部门数据 */
const initTreeData = async () => {
  // 判断部门的数据是否存在，存在不获取，不存在则获取
  if (deptOptions.value === undefined) {
    const { data } = await treeselect();
    deptOptions.value = data;
  }
}


/** 重置操作表单 */
const reset = () => {
  form.value = { ...initFormData };
  isAdmin.value = false;
  userFormRef.value?.resetFields();
}
/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  isAdmin.value = false;
  reset();
}

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  const { data } = await api.getUser();
  dialog.visible = true;
  dialog.title = "新增用户";
  await initTreeData();
  postOptions.value = data.posts;
  roleOptions.value = data.roles;
  form.value.password = initPassword.value.toString();
  selectDeptOptions.value.forEach(element => {
    addDisabledField(element, false);
  });

  setTimeout(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: 0, behavior: 'smooth' });
  }, 10);

  // checkChange()
}
/** 修改按钮操作 */
const handleUpdate = async (row?: UserForm) => {
  reset();
  const userId = row?.userId || ids.value[0]
  const { data } = await api.getUser(userId)
  dialog.visible = true;
  dialog.title = "修改用户";
  await initTreeData();
  Object.assign(form.value, data.user);
  postOptions.value = data.posts;
  roleOptions.value = data.roles;
  form.value.postIds = data.postIds;
  form.value.roleIds = data.roleIds;
  form.value.userDeptIds = data.userDeptIds.length === 0 ? [] : data.userDeptIds
  form.value.password = "";
  isAdmin.value = data.user.roles[0].roleKey === 'admin'

  if (isAdmin.value) {
    selectDeptOptions.value.forEach(element => {
      addDisabledField(element, true);
    });
  } else {
    selectDeptOptions.value.forEach(element => {
      addDisabledField(element, false);
    });
  }
  setTimeout(() => {
    let scrollElem = scrollDiv.value;
    scrollElem.scrollTo({ top: 0, behavior: 'smooth' });
  }, 10);
  data.userDeptIds.forEach((item => {
    treeRef.value!.setChecked(item, true)
  }))
  // checkChange()
}

/** 提交按钮 */
const submitForm = () => {
  const array = treeRef.value!.getCheckedNodes()
  if (array.findIndex(item => item.parentId === 0) === -1) {
    form.value.userDeptIds = array.map((item: any) => item.id)
  } else {
    form.value.userDeptIds = [array.find((item: any) => item.parentId === 0).id]
  }


  userFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.userId ? await api.updateUser(form.value) : await api.addUser(form.value);
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  })
}


const handleCheckChange = (node, checked) => {
}




/**
 * 关闭用户弹窗
 */
const closeDialog = () => {
  dialog.visible = false;
  resetForm();
}

/**
 * 重置表单
 */
const resetForm = () => {
  userFormRef.value?.resetFields();
  userFormRef.value?.clearValidate();
  treeRef.value!.setCheckedNodes([])
  isAdmin.value = false;
  form.value.id = undefined;
  form.value.status = '1';
}
onMounted(() => {
  getTreeSelect() // 初始化部门数据
  getList() // 初始化列表数据
  _getDeptTreeList()
  // proxy?.getConfigKey("sys.user.initPassword").then(response => {
  //   initPassword.value = response.data;
  // });
});
</script>

<style lang="scss" scoped>
.card-header {
  .el-form-item {
    margin-bottom: 0px;
  }
}

:deep(.el-select) {
  width: 100%;
}

.tree-border {
  height: 300px;
  overflow-y: auto;
}
</style>
