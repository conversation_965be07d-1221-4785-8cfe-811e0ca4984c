import request from '@/utils/request';
import {
  scriptQuery,
  scriptListResult,
  ChatReportScoringStandardVo,
  scriptFormVo,
  ScriptDetailVo,
  CozeChatDto,
  CozeChatRetrieveDto,
  ScriptQABo,
  ScriptNotifyBo,
  LevelVo,
  LevelFormBo,
  ScriptGenerateParams
} from './types';
import { userResult } from '../user/types';
import { AxiosPromise } from 'axios';

// 查询脚本列表
export function getAllProduct(status: number | string): AxiosPromise<string[]> {
  return request({
    url: '/biz/script/getAllProduct',
    method: 'get',
    params: { status }
  });
}

// 查询脚本列表
export function getScriptList(query: scriptQuery): AxiosPromise<scriptListResult[]> {
  return request({
    url: '/biz/script/list',
    method: 'get',
    params: query
  });
}
export function getUsedProduct(query: { type: number | null }): AxiosPromise<string[]> {
  return request({
    url: '/biz/script/getUsedProduct',
    method: 'post',
    params: query
  });
}
export function getChatReportScoringStandard(query: PageQuery & { type: number | null }): AxiosPromise<ChatReportScoringStandardVo[]> {
  return request({
    url: '/biz/chatReportScoringStandard/list',
    method: 'get',
    params: query
  });
}
export function saveOrUpdateScript(data: scriptFormVo): AxiosPromise<any> {
  return request({
    url: '/biz/script/saveOrUpdate',
    method: 'post',
    data
  });
}

export function getScriptDetail(query: { id: string }): AxiosPromise<ScriptDetailVo> {
  return request({
    url: '/biz/script/detail',
    method: 'get',
    params: query
  });
}
export function onlineScript(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/script/online',
    method: 'post',
    params: query
  });
}
export function offlineScript(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/script/offline',
    method: 'post',
    params: query
  });
}
/**恢复 */
export function recoverScript(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/script/recover',
    method: 'post',
    params: query
  });
}
/**作废 */
export function invalidScript(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/script/invalid',
    method: 'post',
    params: query
  });
}

/**科室分类 */
export function getDepartmentList(): AxiosPromise<any> {
  return request({
    url: '/biz/common/department',
    method: 'get'
  });
}
/**AI改写背景 */
export function aiRewriteBackground(data: { original: string }): AxiosPromise<string> {
  return request({
    url: '/biz/ai/aiRewriteBackground',
    method: 'post',
    data
  });
}
/**AI改写目标 */
export function aiRewriteGoal(data: { original: string }): AxiosPromise<string> {
  return request({
    url: '/biz/ai/aiRewriteGoal',
    method: 'post',
    data
  });
}
/**coze 生成问题 */
export function aiGenerateQuestion(data: string[]): AxiosPromise<CozeChatDto> {
  return request({
    url: '/biz/ai/coze/aiGenerateQuestion',
    method: 'post',
    data
  });
}
/**coze 判断问题是否生成完成 */
export function checkGenerateQuestionStatus(query: { chatId: string; conversationId: string }): AxiosPromise<CozeChatRetrieveDto> {
  return request({
    url: '/biz/ai/coze/checkGenerateQuestionStatus',
    method: 'get',
    params: query
  });
}
/**coze 获取生成问题 */
export function getGenerateQuestion(query: { chatId: string; conversationId: string }): AxiosPromise<ScriptQABo[]> {
  return request({
    url: '/biz/ai/coze/getGenerateQuestion',
    method: 'get',
    params: query
  });
}
/**脚本通知 */
export function scriptNotify(data: ScriptNotifyBo): AxiosPromise<any> {
  return request({
    url: '/biz/script/scriptNotify',
    method: 'post',
    data
  });
}
/**获取未练习的用户列表 */
export function unPractiseUserList(query: { scriptId: string }): AxiosPromise<userResult[]> {
  return request({
    url: '/biz/script/unPractiseUserList',
    method: 'get',
    params: query
  });
}
/**获取闯关列表 */
export function getLevelList(query: PageQuery): AxiosPromise<LevelVo[]> {
  return request({
    url: '/biz/level/list',
    method: 'get',
    params: query
  });
}
/**关卡脚本列表 */
export function getLevelScriptList(): AxiosPromise<ScriptDetailVo[]> {
  return request({
    url: '/biz/script/levelScriptList',
    method: 'get'
  });
}

/**添加关卡 */
export function addLevelScriptGroup(data: LevelFormBo): AxiosPromise<any> {
  return request({
    url: '/biz/level/add',
    method: 'post',
    data
  });
}

/**关卡脚本列表 */
export function scriptLevelDuplicateCheck(query: { scriptId: string; levelId: string | null }): AxiosPromise<boolean> {
  return request({
    url: '/biz/level/scriptLevelDuplicateCheck',
    method: 'get',
    params: query
  });
}
/**上线关卡 */
export function onlineLevelScriptGroup(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/level/online',
    method: 'post',
    params: query
  });
}
/**下线关卡 */
export function offlineLevelScriptGroup(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/level/offline',
    method: 'post',
    params: query
  });
}

/**获取闯关未上线脚本 */
export function getOfflineScript(query: { levelId: string }): AxiosPromise<any> {
  return request({
    url: '/biz/level/getOfflineScript/' + query.levelId,
    method: 'get'
  });
}

/**删除 */
export function deleteLevel(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/level/delete?id=' + id,
    method: 'post'
  });
}
/**脚本解锁权限检查 */
export function scriptUnlockPermissionCheck(query: { scriptId: string; unlockConditionScriptIds: string[] | string }): AxiosPromise<any> {
  query.unlockConditionScriptIds = (query.unlockConditionScriptIds as string[]).join(',');
  return request({
    url: '/biz/level/scriptUnlockPermissionCheck',
    method: 'get',
    params: query
  });
}
/**详情 */
export function getLevelDetail(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/level/detail/' + id,
    method: 'get'
  });
}
/**修改关卡 */
export function updateLevelScriptGroup(data: LevelFormBo): AxiosPromise<any> {
  return request({
    url: '/biz/level/update',
    method: 'post',
    data
  });
}
/**判断脚本是否在闯关中 */
export function checkScriptInLevel(query: { scriptId: string }): AxiosPromise<any> {
  return request({
    url: '/biz/level/checkScriptInLevel',
    method: 'get',
    params: query
  });
}

/**批量编辑禁用文字输入开关 */
export function batchUpdateDisableTextInputFlag(data: { scriptIdList: string[]; disableTextInputFlag: boolean }): AxiosPromise<any> {
  return request({
    url: '/biz/script/batchUpdateDisableTextInputFlag',
    method: 'post',
    data
  });
}

/**coze 生成问题 */
export function aiGenerateQuestionWithFile(data: { files: any }): AxiosPromise<CozeChatDto> {
  const formData = new FormData();
  formData.append('files', data.files);
  return request({
    url: '/biz/ai/coze/aiGenerateQuestionWithFile',
    method: 'post',
    data: formData
  });
}

// 生成脚本
export function generateScript(data: ScriptGenerateParams): AxiosPromise<any> {
  return request({
    url: '/biz/script/generate',
    method: 'post',
    data
  });
}

// 重新生成脚本
export function regenerateScript(data: ScriptGenerateParams): AxiosPromise<any> {
  return request({
    url: '/biz/script/regenerate',
    method: 'post',
    data
  });
}

// 获取脚本生成信息
export function getGenerateInfo(scriptId: string): AxiosPromise<ScriptGenerateParams> {
  return request({
    url: '/biz/script/getScriptGenerateInfo',
    method: 'get',
    params: {
      scriptId
    }
  });
}

// 获取脚本生成结果
export function getGenerateResult(id: string): AxiosPromise<ScriptDetailVo> {
  return request({
    url: '/biz/script/getGenerateResult',
    method: 'get',
    params: {
      id
    }
  });
}
// 重新解析PDF
export function pptParseRetry(data: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/script/pptParseRetry?id=' + data.id,
    method: 'post'
  });
}

//解析PPT
export function pptParse(data: { parseFileOssId: string; product: string; aiPPTParseFlag: boolean }): AxiosPromise<any> {
  return request({
    url: '/biz/script/pptParse',
    method: 'post',
    data
  });
}

// 删除
export function deleteScript(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/script/remove?id=' + id,
    method: 'post'
  });
}
