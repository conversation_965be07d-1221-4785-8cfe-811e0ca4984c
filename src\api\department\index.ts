import request from '@/utils/request';
import { DeptVo, <PERSON>Long, DeptFormVo } from './types';
import { AxiosPromise } from 'axios';

// 查询部门列表
export function getDeptList(query: { parentId: string }): AxiosPromise<DeptVo[]> {
  return request({
    url: '/biz/dept/list',
    method: 'get',
    params: query
  });
}

// 查询部门列表树结构
export function getDeptTreeList(): AxiosPromise<TreeLong[]> {
  return request({
    url: '/biz/dept/tree',
    method: 'get'
  });
}
// 查询部门列表树结构
export function getDeptTreeSelectedList(): AxiosPromise<TreeLong[]> {
  return request({
    url: '/biz/dept/treeSelected',
    method: 'get'
  });
}

// 删除
export function delDept(query: { id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/dept/delete',
    method: 'post',
    params: query
  });
}
// 保存
export function saveDept(data: DeptFormVo): AxiosPromise<any> {
  return request({
    url: '/biz/dept/save',
    method: 'post',
    data
  });
}
// 检查是否同名
export function checkNameUnique(query: { name: string; id: string | null; parentId: string | number }): AxiosPromise<any> {
  return request({
    url: '/biz/dept/checkNameUnique',
    method: 'get',
    params: query
  });
}
// 修改
export function updateDept(data: DeptFormVo): AxiosPromise<any> {
  return request({
    url: '/biz/dept/update',
    method: 'post',
    data
  });
}
// 修改脚本所属部门
export function updateDeptIds(data: { deptIds: string[]; id: string }): AxiosPromise<any> {
  return request({
    url: '/biz/script/updateDeptIds',
    method: 'post',
    data
  });
}
// 修改脚本所属部门
export function changePermission(data: { deptIds: string | null; ids: string[]; onlyEnableInEventLevel: boolean }): AxiosPromise<any> {
  return request({
    url: '/biz/script/changePermission',
    method: 'post',
    data
  });
}
