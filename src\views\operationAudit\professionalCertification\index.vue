<template>
  <component :is="componentList[currentCom]" :from="currentFrom" :id="currentId" v-model:flag="currentType" @change-type="handleChangeType" />
</template>

<script setup>
import List from './list.vue'
import Detail from './detail.vue'
const route = useRoute();
/** 当前显示组件切换判断 */
//  组件列表
const componentList = {
  List,
  Detail
}
const currentCom = ref('') // 当前组件
const currentId = ref() // 当前行数据id
const currentType = ref() // 点击按钮类型：isDetail-详情，isBack-返回
const currentFrom = ref()

onMounted(()=>{
  currentCom.value=route.query?.flagType||'List'
  currentId.value=route.query.id
  currentFrom.value=route.query.from
})

// 监听组件切换事件-改变当前显示
const handleChangeType = payload => {
  currentId.value = payload.id
  currentType.value = payload.flagType
  payload?.flagType === 'isDetail'
    ? (currentCom.value = 'Detail') // 事件详情
    : (currentCom.value = 'List') // 公告详情
}
</script>

<style lang="scss" scoped></style>
