export interface EmanQuery extends PageQuery {
  status: string | number;
}

/**
 * EmanDetailVo
 */
export interface EmanDetailVo {
  zipFileUrl: string | null;
  responseType: number;
  /**
   * 头像
   */
  avatar: string;
  /**
   * 背景图
   */
  background: string;
  /**
   * 科室
   */
  department: string;
  /**
   * 性别，1-男性 2-女性
   */
  gender: number;
  id: number;
  /**
   * 名称
   */
  name: string;
  /**
   * e人编号
   */
  number: string;
  /**
   * 职业
   */
  occupation: string;
  /**
   * 单位
   */
  organization: string;
  /**
   * 性格
   */
  personality: string;
  /**
   * 擅长方向
   */
  skill: string;
  /**
   * 状态 1-下架，2-审核中，3-上架，4-审核失败
   */
  status: number;
  /**
   * 职称
   */
  title: string;
  /**
   * 语气
   */
  tone: string;
  [property: string]: any;
  introductionType: number;
  introductionDelay: number;
  introductionList: string[];
  introductionContentList: { content: string }[];
  emanDifficult?: number;
}

/**
 * 创建E人对象
 */
export interface EmanFormVo {
  createTime?: string;
  creator?: string;
  show3dFlag?: boolean;
  id?: number | string;
  status?: number | string;
  toneName: string;
  responseType: number;
  zipFileUrl: string;
  videoUrl: string;
  /**
   * 头像
   */
  avatar: string;
  /**
   * 背景图
   */
  background: string;
  /**
   * 部门
   */
  department: string | string[] | null;
  /**
   * 性别，1-男性 2-女性
   */
  gender: number | null;
  /**
   * 昵称
   */
  name: string;
  /**
   * 职业
   */
  occupation: string;
  /**
   * 性格
   */
  personality: string | string[];
  /**
   * 擅长方向
   */
  skill: string;
  /**
   * 职称/级别
   */
  title: string | string[];
  /**
   * 声音
   */
  tone: any;
  /**
   * E人类型，1-情景演练 2-企业智脑
   */
  type: number | null;
  kbIds: number[] | { emanId: number; kbId: number; kbName: string }[] | null | undefined;
  kbList?: { emanId: number; kbId: number; kbName: string; type: number }[];
  introductionType: number;
  introductionDelay: number;
  introductionList: string[];
  introductionContentList: { content: string }[];
  emanDifficult?: number;
}
