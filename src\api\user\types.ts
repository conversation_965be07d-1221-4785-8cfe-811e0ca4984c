export interface userListQuery extends PageQuery {
  keyword: string;
  status: number | string | null;
  deptIds?: string[];
  activateFlag: boolean | null;
  kanbanFlag?: boolean | null;
  whiteFlag?: boolean | null;
  shareFloag?: boolean | null;
}

export interface userResult {
  /**
   * 激活标记
   */
  activateFlag?: boolean;
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 今天是否可以发送短信 ： true 可以发送 ，false 不可以发送
   */
  canSendSms?: boolean;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）
   */
  dataScope?: string;
  /**
   * 部门id
   */
  deptId?: number | string | undefined;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 性别 1-男性 2-女性
   */
  gender?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 小程序数据看板是否开启
   */
  kanbanFlag?: boolean;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 微信openId
   */
  openId?: string;
  /**
   * 手机号
   */
  phoneNumber?: string;
  /**
   * 职务
   */
  post?: string;
  /**
   * 状态，0-未激活，1-已激活，2-已冻结
   */
  status?: number;
  /**
   * 学员部门
   */
  userDeptIds?: number[];
  whiteFlag?: boolean;
  [property: string]: any;
}

export interface userFormVo {
  id?: number | string | undefined;
  name: string;
  gender?: number | null | string;
  phoneNumber: string;
  deptId?: string | null;
}
/**
 * UserImportResultVo
 */
export interface UserImportResultVo {
  /**
   * 错误列表缓存id
   */
  errorListCacheId: string;
  /**
   * 导入失败数量
   */
  failureNum: number;
  /**
   * 导入成功数量
   */
  successNum: number;
  [property: string]: any;
}
