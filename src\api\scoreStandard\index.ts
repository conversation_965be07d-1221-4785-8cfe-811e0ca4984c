import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ChatReportScoringStandardVo, ChatReportScoringStandardDetailVo, ChatReportScoringStandardBo } from './types';

// 查询列表
export function queryList(query: PageQuery): AxiosPromise<ChatReportScoringStandardVo[]> {
  return request({
    url: '/biz/chatReportScoringStandard/list',
    method: 'get',
    params: query
  });
}

export function online(data: { id: string }): AxiosPromise<boolean> {
  return request({
    url: '/biz/chatReportScoringStandard/enable',
    method: 'post',
    params: data
  });
}

// 详情
export function queryDetail(id: string): AxiosPromise<any> {
  return request({
    url: '/biz/chatReportScoringStandard/detail',
    method: 'get',
    params: { id }
  });
}

export function handleSave(data: ChatReportScoringStandardBo): AxiosPromise<string> {
  return request({
    url: '/biz/chatReportScoringStandard/saveOrUpdate',
    method: 'post',
    data
  });
}

// 复制
export function duplicate(id: string): AxiosPromise<string> {
  return request({
    url: '/biz/chatReportScoringStandard/duplicate',
    method: 'post',
    params: { id }
  });
}
