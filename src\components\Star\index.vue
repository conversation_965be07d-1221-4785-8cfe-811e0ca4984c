<template>
  <div class="star-box">
    <div class="star-item" :class="[i <= modelValue ? 'active': 'in-active', size, readonly? 'readonly': '']" v-for="i in count" :key="i" @click="changeStar(i)"></div>
  </div>
</template>
<script setup lang="ts">
const props = withDefaults(defineProps<{
  modelValue: number;
  count?: number;
  readonly?: boolean;
  size?: 'mini' | 'small' | 'large'
}>(),{
  modelValue: 0,
  count: 3,
  readonly: false,
  size: 'large'
})

const emit = defineEmits(['update:modelValue'])

const changeStar = (i: number) => {
  if (props.readonly) return;
  console.log(i, props.modelValue);

  if(i === props.modelValue) {
    emit('update:modelValue', i - 1)
  } else {
    emit('update:modelValue', i)
  }
}
</script>

<style scoped lang="scss">
.star {
  &-box {
    display: flex;
    gap: 6px;
  }
  &-item {
    width: 24px;
    height: 24px;
    cursor: pointer;
    background-image: url("@/assets/images/star_off.svg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;

    &.small {
      width: 12px;
      height: 12px;
    }
    &.mini {
      width: 10px;
      height: 10px;
    }
    &.active {
      background-image: url("@/assets/images/star_on.svg");
    }

    &.in-active.readonly {
      visibility: hidden;
    }

    &.readonly {
      cursor: default;
    }
  }

}
</style>
