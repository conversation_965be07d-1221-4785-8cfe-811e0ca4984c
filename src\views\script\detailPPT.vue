<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex" v-if="editFlag">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="props.isEdit ? back() : checkSave()" class="back-icon" />
            编辑幻灯片</span
          >
          <span class="card-title flex detail-title" v-else>
            <img
              src="@/assets/icons/png/back.png"
              style="cursor: pointer;margin-top:7px;"
              @click="props.isEdit ? back() : checkSave()"
              class="back-icon"
            />
            {{ oldFromInfo.name }}</span
          >
          <div class="right-button" v-if="editFlag">
            <el-button plain type="info" @click="props.isEdit ? back() : checkSave()">取消</el-button>
            <el-button type="primary" @click="handleSubmit()" :loading="btnLoading">保存</el-button>
          </div>
          <div class="right-button" v-else>
            <el-button type="primary" plain @click="emits('change-type', { flagType: 'list', id: '', listType: listType })">返回</el-button>
            <el-button type="primary" @click="editFlag = true">编辑</el-button>
          </div>
        </div>
      </template>
      <div class="box-container" ref="scrollDiv" v-if="editFlag">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="130px" label-position="left">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="幻灯片名称" prop="name">
                <el-input v-model="formParams.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="产品" prop="product" v-if="formParams.type">
                <el-select clearable v-model="formParams.product" placeholder="请选择或者输入" filterable allow-create>
                  <el-option v-for="item in productList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="scoringStandardId" v-if="formParams.type">
                <template #label>
                  <span class="label-text">整体评分标准</span>
                  <el-tooltip placement="top">
                    <template #content>
                      <div class="tip-content">评分标准是系统判断学员演练水平的主要依据，系统将根据此评分标准生成结果报告。</div>
                    </template>
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-select v-model="formParams.scoringStandardId" placeholder="请选择">
                  <el-option v-for="item in scoringStandardArray" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item prop="pptScoringType" v-if="formParams.type">
                <template #label>
                  <span class="label-text">单页评分方式</span>
                  <el-tooltip placement="top">
                    <template #content>
                      <div class="tip-content flex" style="flex-direction: column;">
                        <span> 将会根据每页设定的演练要求，在学员进行幻灯片演练时进行打分。可选择三种： </span>
                        <span> 1.演练要求+语义识别：AI根据学员此页讲解的内容，判断是否符合设定的演练要求，进行自动打分。 </span>
                        <span>
                          2.关键词+语义识别：需设置每页幻灯片需要讲解的关键词，AI根据学员讲解内容，判断是否提及这些关键词，并进行自动打分。
                        </span>
                        <span>
                          3.关键词+关键词匹配：需设置每页幻灯片需要讲解到的关键词，系统进行匹配学员此页是否提及演练要求中的关键词，根据比例计算得分。
                        </span>
                      </div>
                    </template>
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="formParams.pptScoringType">
                  <el-radio v-for="item in pptScoringTypeArray" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" style="align-items: flex-start;">
            <el-col :span="9">
              <el-form-item prop="timeLimit">
                <template #label>
                  <span class="label-text">时间限制</span>
                  <el-tooltip placement="top">
                    <template #content>
                      <div class="tip-content">
                        整个幻灯片演练的时间限制，系统会根据设定的时限在学员练习时进行倒计时，超时将自动结束练习，最多填写30分钟。
                      </div>
                    </template>
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-input v-model="formParams.timeLimit" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <div class="tips" style="margin-bottom: 22px;color: #333333; margin-left: 10px;">分钟</div>
          </el-row>
          <el-divider />
          <div class="ppt-list">
            <el-row :gutter="24" class="ppt-list-header">
              <el-col :span="3">
                <div class="ppt-list-head">页码</div>
              </el-col>
              <el-col :span="6">
                <div class="ppt-list-head">页面预览</div>
              </el-col>
              <el-col :span="9">
                <div class="ppt-list-head" v-if="formParams.pptScoringType === '1'">
                  <span class="label-text">演练要求</span>
                  <el-tooltip placement="top">
                    <template #content>
                      <div class="tip-content">
                        此页幻灯片的演练要求是什么，可以填写讲解要点、过渡语、注意事项、重点等等，学员在练习过程中可查看演练要求进行话术调整，如果不填写默认无要求。
                      </div>
                    </template>
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="ppt-list-head" v-if="['2', '3'].includes(formParams.pptScoringType)">
                  <span class="label-text">关键词</span>
                  <el-tooltip placement="top">
                    <template #content>
                      <div class="tip-content">此页幻灯片需要讲解到的关键词是什么，可填写关键词及其同义词，如果不填写默认无关键词。</div>
                    </template>
                    <el-icon class="label-tip">
                      <img src="@/assets/icons/svg/help1.svg" class="help" />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="ppt-list-head">操作</div>
              </el-col>
            </el-row>
            <el-row class="mt20" :gutter="24" v-for="(item, index) in formParams.pptList" :key="item.id">
              <el-col :span="3">
                <div class="ppt-list-page">
                  <div class="ppt-list-page-num">P{{ index + 1 }}</div>
                  <div class="ppt-list-page-name">{{ item.name }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="ppt-list-preview">
                  <el-form-item :prop="'pptList.' + index + '.imageUrl'" :rules="setRules.image">
                    <el-image :src="item.imageUrl" class="ppt-list-image" fit="contain" hide-on-click-modal :preview-src-list="[item.imageUrl]" />
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="9">
                <div class="ppt-list-description" v-if="formParams.pptScoringType === '1'">
                  <el-form-item :prop="'pptList.' + index + '.requirement'" :rules="setRules.requirement">
                    <div class="backdrop-text">
                      <el-input
                        v-model="item.requirement"
                        :disabled="item.aiCreateFlag"
                        type="textarea"
                        :placeholder="item.aiCreateFlag ? 'AI生成中……' : '请输入演练关键点'"
                        :autosize="false"
                        resize="none"
                      />
                      <span class="ai" @click="item.aiCreateFlag ? stopLabel(item.imageId) : confirmLabel(item.imageId)">{{
                        item.aiCreateFlag ? '停止生成' : 'AI生成' }}</span>
                    </div>
                  </el-form-item>
                </div>
                <div class="keyword-list" v-else>
                  <div>
                    <div class="word-box" style="gap: 0;" v-if="item.keywords">
                      <span v-for="(it, i) in JSON.parse(item.keywords) || []" :key="i">
                        {{ it.name }}
                        <template v-if="it.synonyms && it.synonyms.length > 0">/</template>
                        <template v-if="it.synonyms"> {{ it.synonyms.join('/') }}</template>
                        <template v-if="it.remark && formParams.pptScoringType === '2'">( {{ '备注：' + it.remark }})</template>
                        <template v-if="i !== (JSON.parse(item.keywords) || []).length - 1">、</template>
                      </span>
                    </div>
                  </div>
                  <div>
                    <div class="info-btn" @click="openKeywordDialog(item.imageId)">编辑关键词</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="ppt-list-actions">
                  <el-tooltip placement="top" trigger="hover" content="替换">
                    <div class="ppt-list-icon " :class="item.aiCreateFlag ? 'replace-disabled' : 'replace'" @click="replacePPT(index)" />
                  </el-tooltip>

                  <el-tooltip placement="top" trigger="hover" content="上移">
                    <div class="ppt-list-icon" :class="index === 0 || item.aiCreateFlag ? 'up-disabled' : 'up'" @click="moveUp(index)" />
                  </el-tooltip>

                  <el-tooltip placement="top" trigger="hover" content="下移">
                    <div
                      class="ppt-list-icon"
                      :class="(index === formParams.pptList.length - 1) || item.aiCreateFlag ? 'down-disabled' : 'down'"
                      @click="moveDown(index)"
                    />
                  </el-tooltip>

                  <el-tooltip
                    placement="top"
                    trigger="hover"
                    content="删除"
                    v-if="(index === 0 && formParams.pptList.length === 1) || item.aiCreateFlag"
                  >
                    <div class="ppt-list-icon " :class="item.aiCreateFlag ? 'delete-disabled' : 'delete'" @click="removePPT(index)" />
                  </el-tooltip>

                  <el-popconfirm
                    v-else
                    width="220"
                    placement="top"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="removePPT(index)"
                    title="确定删除吗?"
                  >
                    <template #reference>
                      <div>
                        <el-tooltip placement="top" trigger="hover" content="删除">
                          <div class="ppt-list-icon " :class="item.aiCreateFlag ? 'delete-disabled' : 'delete'" />
                        </el-tooltip>
                      </div>
                    </template>
                  </el-popconfirm>
                </div>
              </el-col>
            </el-row>
            <div class="button">
              <el-button type="primary" @click="addPPT" icon="plus">添加幻灯片图片</el-button>
            </div>
          </div>
        </el-form>
      </div>
      <div class="box-container" v-else>
        <div class="script-msg">
          <div class="left">幻灯片名称</div>
          <div class="right">
            <span>{{ oldFromInfo.name }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">产品</div>
          <div class="right">
            <span>{{ oldFromInfo.product }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">整体评分标准</div>
          <div class="right">
            <span>{{ oldFromInfo.scoringStandardName }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">单页评分方式</div>
          <div class="right">
            <span>{{pptScoringTypeArray.find((item) => item.key === oldFromInfo.pptScoringType)?.name}}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">时间限制</div>
          <div class="right">
            <span>{{ oldFromInfo.timeLimit }}分钟</span>
          </div>
        </div>
        <el-divider />
        <div class="ppt-list">
          <el-row :gutter="24" class="ppt-list-header">
            <el-col :span="2">
              <div class="ppt-list-head">页码</div>
            </el-col>
            <el-col :span="6">
              <div class="ppt-list-head">页面预览</div>
            </el-col>
            <el-col :span="16">
              <div class="ppt-list-head">{{ formParams.pptScoringType === '1' ? '演练要求' : '关键词' }}</div>
            </el-col>
          </el-row>
          <el-row class="mt20" :gutter="24" v-for="(item, index) in formParams.pptList" :key="item.id">
            <el-col :span="2">
              <div class="ppt-list-page">
                <div class="ppt-list-page-num">P{{ index + 1 }}</div>
                <div class="ppt-list-page-name">{{ item.name }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="ppt-list-preview">
                <el-image :src="item.imageUrl" class="ppt-list-image" fit="contain" hide-on-click-modal :preview-src-list="[item.imageUrl]" />
              </div>
            </el-col>
            <el-col :span="16">
              <div class="ppt-list-description ppt-list-description-detail" v-if="formParams.pptScoringType === '1'">
                <span>{{ item.requirement || '无要求' }}</span>
              </div>
              <div class="keyword-list" v-else>
                <div>
                  <div style="gap: 0;" v-if="item.keywords">
                    <span v-for="(it, i) in JSON.parse(item.keywords) || []" :key="i">
                      {{ it.name }}
                      <template v-if="it.synonyms && it.synonyms.length > 0">/</template>
                      <template v-if="it.synonyms"> {{ it.synonyms.join('/') }}</template>
                      <template v-if="it.remark && formParams.pptScoringType === '2'">( {{ '备注：' + it.remark }})</template>
                      <template v-if="i !== (JSON.parse(item.keywords) || []).length - 1">、</template>
                    </span>
                  </div>
                  <div v-else>无</div>
                </div>
                <div>
                  <!-- <div class="info-btn" @click="openKeywordDialog(item.imageId)">编辑关键词</div> -->
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="script-msg">
          <div class="left">创建时间</div>
          <div class="right">
            <span>{{ oldFromInfo.createTime }}</span>
          </div>
        </div>
        <div class="script-msg">
          <div class="left">权限</div>
          <div class="right wrapper" v-if="extra.deptNames">
            <input id="exp1" class="exp" type="checkbox" />
            <span class="more-expand">
              <label class="more-btn" for="exp1"></label>
              {{ extra.deptNames }}
            </span>
          </div>
          <div class="right" v-else>
            <span>全部成员</span>
          </div>
        </div>
      </div>

      <!-- AI标注选择 -->
      <!-- <el-dialog v-model="showBatchAiLabel" :close-on-click-modal="false" :show-close="false" width="400px" append-to-body>
      <template #title>
        <div style="font-size: 16px;margin-top: 12px;">请选择AI标注内容的合并方式</div>
      </template>
      <el-radio-group v-model="currentMergeMethod" style="display: flex;">
        <el-radio :value="1" :label="1">
          <span>添加在末尾，与现有标注内容合并</span>
        </el-radio>
        <el-radio :value="2" :label="2">
          <span>替换原内容，替换现有标注内容</span>
        </el-radio>
      </el-radio-group>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeLabelDialog">取 消</el-button>
          <el-button type="primary" :loading="labelLoading" @click="handleBatchAiLabelOrVoice(currentImageId)">开始标注</el-button>
        </div>
      </template>
    </el-dialog> -->

      <PPTUpload
        v-model:visible="showAddPPT"
        :title="uploadTitle"
        :limit="pptUploadLimit"
        :file-size="10"
        :file-type="['jpg', 'jpeg', 'png', 'JPG', 'JPEG', 'PNG']"
        @success="onAddSuccess"
      />
      <KeywordDialog
        v-model:visible="showKeywordDialog"
        :pptScoringType="formParams.pptScoringType"
        @success="getKeywords"
        :currentKeywords="currentKeywords"
      >
      </KeywordDialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ChatReportScoringStandardVo, PPTBo, ScriptDetailVo, scriptFormVo } from "@/api/script/types";
import { ElLoading, ElMessageBox, FormRules } from "element-plus";
import { getChatReportScoringStandard, getScriptDetail, getAllProduct, saveOrUpdateScript } from "@/api/script";
import { deepClone, moveToError } from "@/utils";
import PPTUpload from './components/PPTUpload'
import { globalHeaders } from "@/utils/request";
import KeywordDialog from './components/keywordsDialog.vue'
const showKeywordDialog = ref(false)
const scrollDiv = ref()
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['change-type'])
const btnLoading = ref(false)
const scoringStandardArray = ref<ChatReportScoringStandardVo[]>([])
const productList = ref<string[]>([])
const oldFromInfo = ref<ScriptDetailVo>({} as ScriptDetailVo)
import { fetchEventSource } from "@microsoft/fetch-event-source";

const formParams = ref<scriptFormVo>({
  type: 3,
  name: '',
  pptScoringType: '1',
  timeLimit: null,
  scoringStandardId: null,
  product: '',
  pptList: []
})
interface RuleForm {
  name: string
  timeLimit: string
  type: number
  scoringStandardId: string | number
  pptScoringType: string
  product: string
}
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  listType: {
    type: Number
  },
  extra: {
    type: Object,
    default: () => {
      return {
        deptNames: ''
      }
    }
  }
})
// const showBatchAiLabel = ref(false)
// const currentMergeMethod = ref(1)
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const headers = ref(globalHeaders());
const ctrl = new AbortController(); // 用于中断请求
// const labelLoading = ref(false)
const currentImageId = ref()
const currentKeywords = ref<string>('')
const pptScoringTypeArray = [{ name: '演练要求+语义识别', key: '1' }, { name: '关键词+语义识别', key: '2' }, { name: '关键词+关键词匹配', key: '3' }]


// const currentImageId = ref()
// const labelLoading = ref(false)
// const currentImageId = ref()

const editFlag = ref(props.isEdit)
const showAddPPT = ref(false)
const pptUploadLimit = ref(100)
const uploadTitle = ref('')
const replaceIndex = ref<number>();
const addType = ref('') // add replace

const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 1000) {
    callback(new Error('最多1000个字符'))
  } else {
    callback()
  }
}

const checkTimeLimit = (rule: any, value: any, callback: any) => {
  if (value > 30) {
    callback(new Error('最多30分钟'))
  } else {
    callback()
  }
}
const checkNameLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('最多64个字符'))
  } else {
    callback()
  }
}
const checkProductLimit = (rule: any, value: any, callback: any) => {
  if (value.length > 64) {
    callback(new Error('最多64个字符'))
  } else {
    callback()
  }
}

const setRules = reactive({
  image: [
    { required: true, message: '请上传图片', trigger: 'blur' },
  ],
  requirement: [
   {
      validator: checkFontLengthLimit,
      trigger: ["change"]
    }
  ]
})

const rules = reactive<FormRules<RuleForm>>({
  pptScoringType: [
    { required: true, message: '请选择单页评分标准', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入幻灯片名称', trigger: 'blur' },
    {
      validator: checkNameLimit,
      trigger: ["blur"]
    }
  ],
  timeLimit: [
    { required: true, message: '请输入整体演练时限', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入大于0的整数",
      trigger: 'blur'
    },
    {
      validator: checkTimeLimit,
      trigger: 'blur'
    }
  ],
  scoringStandardId: [
    { required: true, message: '请选择评分标准', trigger: ["blur", "change"] },
  ],
  product: [
    { required: true, message: '请选择产品', trigger: ["blur", "change"] },
    {
      validator: checkProductLimit,
      trigger: ["blur", "change"]
    }
  ],
})


const openKeywordDialog = (imageId: string) => {
  showKeywordDialog.value = true
  currentImageId.value = imageId
  currentKeywords.value = formParams.value.pptList.find((item: any) => item.imageId === imageId).keywords || ''
  console.log(currentKeywords.value)
}

const getKeywords = (keywords: PPTBo[]) => {
  const index = formParams.value.pptList.findIndex((item: any) => item.imageId === currentImageId.value)
  formParams.value.pptList[index].keywords = keywords.length > 0 ? JSON.stringify(keywords) : null

}



// const closeLabelDialog = () => {
// showBatchAiLabel.value = false
// currentMergeMethod.value = 1
// }

// const handleBatchAiLabelOrVoice = (imageId) => {
//   showBatchAiLabel.value = false //隐藏弹框

// }

const confirmLabel = (imageId: any) => {
  const index = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  if (formParams.value.pptList[index].requirement !== '') {
    ElMessageBox.confirm(
      '已存在的内容将会被覆盖。',
      '确定要进行AI生成吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        // currentImageId.value = imageId
        // showBatchAiLabel.value = true
        createAiLabel(imageId)
      })
      .catch(() => {
      })

  } else {
    createAiLabel(imageId)
  }
}
// 终止标注
const stopLabel = (imageId: any) => {
  const index = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  formParams.value.pptList[index].aiCreateFlag = false
  ctrl.abort();
}

// Ai生成标注描述
const createAiLabel = async (imageId: string) => {
  const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  formParams.value.pptList[ctIndex].requirement = ''
  let createResult = formParams.value.pptList[ctIndex].requirement

  const requestOptions = {
    method: "POST", // 请求方法，SSE 通常是 GET 请求。如果涉及到双向通信，需要改为 POST。
    headers: {
      "Content-Type": "text/event-stream", // 设置内容类型为 SSE，即
      ...headers.value
    },
    signal: ctrl.signal,
    // 可以添加其他需要的配置：
    // 如果为 POST 请求，需要携带 body 参数以传递请求体；
    // 如果希望用户切换到另一个页面后仍能保持 SSE 连接，可以配置 openWhenHidden 属性为 true；
    // 如果需要使用 AbortController 来实现检测到问题终止连接的，可以配置 signal 属性等。
  };
  fetchEventSource(baseUrl + '/biz/script/pptAiLabel?imageId=' + imageId, {
    ...requestOptions,
    onopen(response) {
      console.log("Connection opened!", response);
      formParams.value.pptList[ctIndex].aiCreateFlag = true
    },
    onmessage(event) {
      const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
      if (event.data !== '') {
        const data = JSON.parse(event.data)
        // console.log(data.content)
        if (formParams.value.pptList[ctIndex].aiCreateFlag) {
          createResult += data.content
          setResult(imageId, createResult)
        }
      }
    },
    onerror(error) {
      // console.log(error)
      const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
      formParams.value.pptList[ctIndex].aiCreateFlag = false
      ctrl.abort();
      throw error
    },
    onclose() {
      const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
      formParams.value.pptList[ctIndex].aiCreateFlag = false
      console.log("Connection closed!");
    },
  });
}
const setResult = (imageId: string, result: string) => {
  const ctIndex = formParams.value.pptList.findIndex((item: any) => item.imageId === imageId)
  formParams.value.pptList[ctIndex].requirement = result
  // nextTick(() => {
  //   setTimeout(() => {
  //     const textarea = document.getElementById('textarea_id');
  //     textarea!.scrollTop = textarea!.scrollHeight;
  //   }, 100);
  // });
}



const removePPT = (index: number) => {
  if (formParams.value.pptList[index].aiCreateFlag) {
    return
  }
  if (index === 0 && formParams.value.pptList.length === 1) {
    proxy.$modal.msgError('最后一页幻灯片无法删除')
  } else {
    formParams.value.pptList.splice(index, 1);
    proxy.$modal.msgSuccess('删除成功')
  }
}

const moveUp = (index: number) => {
  if (index === 0 || formParams.value.pptList[index].aiCreateFlag) {
    return;
  }

  const temp = formParams.value.pptList[index];
  formParams.value.pptList.splice(index, 1);
  formParams.value.pptList.splice(index - 1, 0, temp);
}

const moveDown = (index: number) => {
  if ((index === formParams.value.pptList.length - 1) || formParams.value.pptList[index].aiCreateFlag) {
    return;
  }

  const temp = formParams.value.pptList[index];
  formParams.value.pptList.splice(index, 1);
  formParams.value.pptList.splice(index + 1, 0, temp);
}


const addPPT = () => {
  // tood: 最多添加100个
  if (formParams.value.pptList.length >= 100) {
    proxy.$modal.msgError('最多添加100页幻灯片');
    return;
  }

  pptUploadLimit.value = 100;
  addType.value = 'add';
  uploadTitle.value = '上传幻灯片图片'
  showAddPPT.value = true;
}

const onAddSuccess = (fileLiet: any) => {
  console.log('onAddSuccess', fileLiet)
  if (pptUploadLimit.value === 1) {
    formParams.value.pptList[replaceIndex.value].imageId = fileLiet[0].ossId;
    formParams.value.pptList[replaceIndex.value].imageUrl = fileLiet[0].url;
    formParams.value.pptList[replaceIndex.value].name = fileLiet[0].name;
  } else {
    formParams.value.pptList.push(...fileLiet.map((item) => {
      return {
        name: item.name,
        imageUrl: item.url,
        imageId: item.ossId,
        id: item.uid,
        requirement: '',
        aiCreateFlag: false
      }
    }))

    // 滚动到底部
    nextTick(() => {
      let scrollElem = scrollDiv.value;
      scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' });
    });
  }

}

const replacePPT = (index: number) => {
  if (formParams.value.pptList[index].aiCreateFlag) {
    return
  }

  pptUploadLimit.value = 1;
  replaceIndex.value = index;
  addType.value = 'replace'
  uploadTitle.value = '替换幻灯片图片'
  showAddPPT.value = true
}

const back = () => {
  console.log('back')
  let bool = false;
  Object.keys(formParams.value).forEach((key) => {
    if (
      key !== 'type' &&
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]'
    ) {
      if (key === 'pptList') {
        formParams.value[key].forEach(element => {
          console.log()
          if (JSON.stringify(element.imageUrl) !== JSON.stringify('') && JSON.stringify(element.imageUrl) !== JSON.stringify('')) {
            bool = true;
            return;
          }
        });
      } else {
        bool = true;
        return;
      }
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        emits('change-type', { flagType: 'list', id: '', listType: props.listType })
      })
      .catch(() => {
      })
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })
  }
}
const checkSave = () => {
  if (editFlag.value) {

    let bool = false;
    Object.keys(formParams.value).forEach((key) => {
      if (typeof formParams.value[key] !== 'object') {
        if (
          JSON.stringify(String(formParams.value[key])) !== JSON.stringify(String(oldFromInfo.value[key]))
        ) {
          bool = true;
          return;
        }
      } else {
        if (
          JSON.stringify(formParams.value[key]) !== JSON.stringify(oldFromInfo.value[key])
        ) {
          bool = true;
          return;
        }
      }
    });
    if (bool) {
      ElMessageBox.confirm(
        '未保存的内容将丢失',
        '确定要返回吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          _getScriptDetail()
          editFlag.value = false
        })
        .catch(() => {
        })

    } else {
      editFlag.value = false
    }
  } else {
    emits('change-type', { flagType: 'list', id: '', listType: props.listType })

  }

}
const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = deepClone(formParams.value);
      const aiHandleIndex = params.pptList.findIndex((item: any) => item.aiCreateFlag)
      if (aiHandleIndex !== -1) {
        proxy.$modal.msgError('AI生成中，请等待完成后再进行保存')
        return
      }
      params.pptList = params.pptList.map((item: PPTBo, index: number) => {
        if (typeof item.id === "number") {
          return {
            orderNum: index,
            imageId: item.imageId,
            imageUrl: item.imageUrl,
            keywords: item.keywords,
            requirement: item.requirement
          }
        } else {
          return {
            ...item,
            orderNum: index
          };
        }
      })
      btnLoading.value = true;
      delete params.createTime
      await saveOrUpdateScript(params).then((res) => {
        proxy.$modal.msgSuccess('保存成功');
        if (props.isEdit) {
          emits('change-type', { flagType: 'list', id: '', listType: 1 })
        } else {
          editFlag.value = false
          _getScriptDetail()
        }

      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
        moveToError();
      console.log('error submit!!');
      return false;
    }
  })
}

const getProductList = async () => {
  const res = await getAllProduct(2);
  productList.value = res.data
}
const _getChatReportScoringStandard = async () => {
  const res = await getChatReportScoringStandard({ type: formParams.value.type, pageNum: 1, pageSize: 99 });
  scoringStandardArray.value = res.rows
}

const _getScriptDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    // background: 'rgba(0, 0, 0, 0.7)',
  })

  const res = await getScriptDetail({ id: props.id });
  loading.close()
  const pptListArray = (res.data.pptList || []).map((item: PPTBo) => {
    return {
      id: item.id,
      imageId: item.imageId,
      imageUrl: item.imageUrl,
      requirement: item.requirement || '',
      name: item.name,
      aiCreateFlag: false,
      keywords: item.keywords || ''
    }
  })
  formParams.value = {
    id: res.data.id,
    type: res.data.type,
    scoringStandardName: res.data.scoringStandardName,
    deptIds: res.data.deptIds,
    name: res.data.name,
    randomNum: res.data.randomNum,
    randomType: res.data.randomType,
    pptScoringType: res.data.pptScoringType ? String(res.data.pptScoringType) : '1',
    timeLimit: res.data.timeLimit,
    scoringStandardId: res.data.scoringStandardId,
    product: res.data.product,
    createTime: res.data.createTime,
    randomFlag: res.data.randomFlag,
    pptList: res.data.pptList?.length === 0 ? [{
      id: '',
      imageId: '',
      imageUrl: '',
      keywords: '',
      requirement: '',
      aiCreateFlag: false,
    }] : pptListArray
  }
  oldFromInfo.value = deepClone(formParams.value)
  _getChatReportScoringStandard()
  getProductList()

}
onMounted(() => {
  _getScriptDetail();
})
</script>

<style lang="scss" scoped>
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.flex {
  display: flex;
  gap: 0 10px;
  align-items: center;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1300px) {
  .box-container {
    padding: 0 30px 100px 30px;
  }
}

@media screen and (min-width: 1301px) {
  .box-container {
    padding: 0 15% 100px 15%;
  }
}

.box-container {
  // display: flex;
  // flex-direction: column;
  // gap: 12px 0;
  // align-items: flex-start;
  margin: 0 auto;
  box-sizing: border-box;
  height: calc(100vh - 84px - 32px - 100px);
  overflow-y: auto;
  width: 100%;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.flex-d {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  span {
    line-height: 28px;
  }
}

:deep(.el-radio) {
  height: auto;
}

:deep(.el-select) {
  width: 100%;
}


:deep(.el-form) {
  width: 100%;
}

.mt8 {
  margin-top: 8px;
}

.button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.tips {
  color: #999999;
  margin-top: 4px;
  font-size: 14px;
}

.ppt-list {
  margin-bottom: 20px;

  &-header {
    margin-bottom: 16px;
  }

  &-head {
    color: #999999;
    font-size: 14px;
    line-height: 22px;
  }

  &-page {
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;

    &-num {
      font-size: 16px;
      line-height: 22px;
      font-weight: bold;
    }

    &-name {
      color: #999999;
      font-size: 12px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
    }
  }

  &-preview {
    width: 202px;
    height: 115px;
    overflow: hidden;
    border: 1px solid #D9D9D9;
    border-radius: 4px;

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }

  &-image {
    display: block;
    width: 200px;
    height: 113px;
  }

  &-description {
    height: 115px;

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }

    :deep(.el-textarea__inner) {
      height: 115px !important;
    }

    &-detail {
      overflow-y: auto;
      font-size: 14px;
      white-space: pre-wrap;
    }
  }

  &-actions {
    width: 210px;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 25px;

    img {
      cursor: pointer;
    }
  }

  &-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;

    &.replace {
      background-image: url("@/assets/images/icon_replace.svg");
    }

    &.replace-disabled {
      background-image: url("@/assets/images/icon_replace_disabled.svg");
    }

    &.up {
      background-image: url("@/assets/images/icon_up.svg");
    }

    &.up-disabled {
      background-image: url("@/assets/images/icon_up_disabled.svg");
    }

    &.down {
      background-image: url("@/assets/images/icon_down.svg");
    }

    &.down-disabled {
      background-image: url("@/assets/images/icon_down_disabled.svg");
    }

    &.delete {
      background-image: url("@/assets/images/icon_delete.svg");
    }

    &.delete-disabled {
      background-image: url("@/assets/images/icon_delete_disabled.svg");
    }
  }
}

.script-msg {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  .left {
    width: 120px;
    font-weight: 500;
    margin-right: 16px;
  }

  .right {
    flex: 1;
  }
}

.label-tip {
  align-self: center;
  color: #999999;
  vertical-align: middle;
}

.tip-content {
  max-width: 220px;
}

.help {
  width: 16px;
  margin-left: 8px;
}

.wrapper {
  flex: 1;
  display: flex;
  margin: 0 auto;
  overflow: hidden;
}

.more-expand {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  line-height: 32px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
}

.more-expand::before {
  content: '';
  height: calc(100% - 24px);
  float: right;
}

.more-expand::after {
  content: '';
  width: 999vw;
  height: 999vw;
  position: absolute;
  box-shadow: inset calc(100px - 999vw) calc(30px - 999vw) 0 0 #fff;
  margin-left: -100px;
}

.more-btn {
  float: right;
  clear: both;
  margin-left: 10px;
  font-size: 14px;
  padding: 0 8px;
  // background: #3F51B5;
  line-height: 14px;
  color: #333333;
  cursor: pointer
}
:deep(.el-card) {
  height: calc(100vh - 32px - 32px);

  .el-card__body {
    height: calc(100vh - 162px);
    overflow-y: auto;
  }
}

.more-btn::before {
  content: '展开'
}

.exp {
  display: none;
}

.exp:checked+.more-expand {
  -webkit-line-clamp: 999;
}

.exp:checked+.more-expand::after {
  visibility: hidden;
}

.exp:checked+.more-expand .more-btn::before {
  content: '收起'
}

.info-btn {
  background-color: rgb(246, 247, 255);
  color: #4F66FF;
  // padding: 5px;
  border-radius: 4px;
  padding: 4px 8px;
  line-height: 20px;
  cursor: pointer;
  width: 100px;
  border: 4px;
  margin-top: 8px;
  text-align: center;
  font-size: 14px;
}

.ai {
  background-color: rgb(246, 247, 255);
  color: #4F66FF;
  // padding: 5px;
  position: absolute;
  border-radius: 4px;
  bottom: 10px;
  left: 10px;
  padding: 4px 8px;
  line-height: 20px;
  cursor: pointer;
}

.backdrop-text {
  position: relative;
  width: 100%;

  :deep(.el-textarea__inner) {
    padding-bottom: 40px;
    height: 250px;
  }
}

.word-box {
  word-break: break-all;
}
</style>
