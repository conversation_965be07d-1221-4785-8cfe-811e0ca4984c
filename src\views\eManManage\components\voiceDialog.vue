<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      :title="title"
      v-model="visibleDialog"
      width="750px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="box-container">
        <div class="top-sex-select">
          <div class="gender" :class="currentGender !== 2 ? 'select' : ''" @click="changeTab(1)">
            <img src="@/assets/icons/png/male.png" class="gender-icon" />
          </div>
          <div class="gender" :class="currentGender === 2 ? 'select' : ''" @click="changeTab(2)">
            <img src="@/assets/icons/png/female.png" class="gender-icon" />
          </div>
        </div>
        <div class="voice-container">
          <el-radio-group v-model="toneRadio" class="list" @change="onVoiceChange">
            <div class="voice-item" v-for="(item, index) in toneList" :key="index">
              <el-radio :label="item.voiceType" size="large">
                <template #default>
                  <div class="left">
                    <el-icon v-if="audioLoading === item.voiceType" size="18" class="rotating-image">
                      <Loading />
                    </el-icon>
                    <img v-else src="@/assets/icons/png/voice.png" class="icon" />
                    {{ item.name }}
                    <img v-if="item.hotFlag" src="@/assets/icons/svg/hot.svg" class="icon" />
                  </div>
                </template>
              </el-radio>
            </div>
          </el-radio-group>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()" :disabled="audioLoading">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { ElMessageBox, ElLoading } from 'element-plus'
import { getMenVoiceList, getWomenVoiceList, getTencentVoice } from "@/api/eman/common";
// const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const currentGender = ref(0)
const emits = defineEmits(['update:visible', 'update:gender', 'toneSuccess'])
const toneRadio = ref(null)
const toneList = ref<any>([])
const audio = ref<HTMLAudioElement>(new Audio())
const audioLoading = ref()

watch(() => props.visible, val => {
  if (val) {
    console.log(props.gender)
    currentGender.value = props.gender === 0 ? 1 : props.gender
    _getvoiceList()
  }
})




const _getvoiceList = async () => {
  const loadingInstance = ElLoading.service({ lock: true, text: 'Loading' })
  const fn = currentGender.value === 2 ? getWomenVoiceList() : getMenVoiceList()
  const res: any = await fn;
  nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
    loadingInstance.close()
  })
  toneList.value = res.data
}

// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增'
  },
  gender: {
    type: Number,
    default: 0
  },
  avatar: {
    type: String,
    default: ''
  },
})

// 弹窗组件显示隐藏
const visibleDialog = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})
// 修改父组件性别
const genderFlag = computed({
  get() {
    return props.gender
  },
  set(value) {
    emits('update:gender', value)
  }
})

/**切换*/
const changeTab = (gender: number) => {
  toneRadio.value = null
  currentGender.value = gender
  _getvoiceList()
}

const closeDialog = () => {
  audio.value.pause()
  visibleDialog.value = false
}


const handleSubmit = () => {
  if (!toneRadio.value) {
    ElMessage.error('请选择声音');
    return
  }
  const tone = toneList.value.find((item: any) => item.voiceType === toneRadio.value)
  if (props.gender !== 0) {
    if (props.gender !== currentGender.value) {
      if (props.avatar) {
        ElMessageBox.confirm(
          '继续修改将重置头像，确定吗', '声音与头像的性别冲突',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(() => {
          genderFlag.value = currentGender.value === 2 ? 2 : 1
          emits('toneSuccess', tone, true)
          closeDialog()
        }).catch(() => {
        })
      } else {
        genderFlag.value = currentGender.value === 2 ? 2 : 1
        emits('toneSuccess', tone, false)
        closeDialog()
      }

    } else {
      emits('toneSuccess', tone)
      closeDialog()
    }
  } else {
    genderFlag.value = currentGender.value === 2 ? 2 : 1
    emits('toneSuccess', tone, false)
    closeDialog()
  }
}
/**
  * desc: base64对象转blob文件对象
  * @param base64  ：数据的base64对象
  * @param fileType  ：文件类型 mp3等;
  * @returns {Blob}：Blob文件对象
  */
function base64ToBlob(base64: string, fileType: string) {
  let typeHeader = 'data:application/' + fileType + ';base64,'; // 定义base64 头部文件类型
  let audioSrc = typeHeader + base64; // 拼接最终的base64
  let arr = audioSrc.split(',');
  let array = arr[0].match(/:(.*?);/);
  console.log(array)
  let mime = (array && array.length > 1 ? array[1] : 'application/mp3') || 'application/mp3';
  // 去掉url的头，并转化为byte
  let bytes = window.atob(arr[1]);
  // 处理异常,将ascii码小于0的转换为大于0
  let ab = new ArrayBuffer(bytes.length);
  // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
  let ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], {
    type: mime
  });
}
const onVoiceChange = (e: any) => {
  const item = toneList.value.find((item: any) => item.voiceType === e)
  audio.value.pause()
  audioLoading.value = e
  getTencentVoice({
    name: item.name,
    type: currentGender.value,
    text: import.meta.env.VITE_APP_TITLE?'AI陪练系统':'你好，欢迎使用语贝，让我们一起开启全新的旅程'
  }).then(res => {
    console.log(res)
    if (res.code === 200 && e === item.voiceType) {
      audio.value.src = URL.createObjectURL(base64ToBlob(res.data, 'mp3'))
      audio.value.play()
      audioLoading.value = null
    }
  })


}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.rotating-image {
  animation: rotate 2s linear infinite;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .top-sex-select {
    width: 300px;
    margin: 0 auto;
    height: 32px;
    background-color: rgba(118, 118, 128, 0.12);
    display: flex;
    border-radius: 6px;
    padding: 2px;
    box-sizing: border-box;

    .gender {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      &-icon {
        width: 24px;
      }
    }

    .select {
      background-color: #fff;
      border-radius: 6px;
    }
  }

  .voice-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding-left: 15px;
    margin-top: 24px;

    .list {
      display: flex;
      flex-direction: column;
      gap: 10px 0;
      width: 100%
    }

    .voice-item {
      width: 100%;
      border-radius: 4px;
      background-color: rgba(241, 241, 241, 1);
      display: flex;
      align-items: center;
      padding: 8px 16px;
      box-sizing: border-box;
      justify-content: space-between;

      .left {
        display: flex;
        align-items: center;
        gap: 0 12px;
      }

      .icon {
        width: 18px;
      }
    }

    // .select-voice {
    //     border: 2px solid #000000;
    // }
  }
}


:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}

:deep(.el-radio) {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
}
</style>
