<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      :title="`拜访${title}`"
      v-model="visibleShow"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      :show-close="false"
    >
      <div class="box-container" v-show="hasAiText">
        <div class="before">
          <div class="title">改写前</div>
          <div class="content">{{ formParams.background }}</div>
        </div>
        <div class="after">
          <div class="title">改写后</div>
          <div class="tips">为了保持人物称呼的一致性，达到更好的对话效果，人物称呼将使用变量【拜访对象】代替，同时移除科室、职称信息。</div>
          <div id="typedjs" class="typedClass"></div>
        </div>
      </div>
      <div class="box-container" v-show="!hasAiText">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules">
          <el-form-item prop="background">
            <el-input
              v-model="formParams.background"
              resize="none"
              rows="8"
              :disabled="btnLoading"
              :placeholder="`请输入${title}，AI会自动改写${title}中的人物称呼信息`"
              type="textarea"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer" v-if="hasAiText">
          <el-button @click="getAiData" :disabled="handleDisabled" :loading="btnLoading">再试一次</el-button>
          <div class="footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="sendBackdrop" :disabled="handleDisabled">确定</el-button>
          </div>
        </div>
        <div class="footer" v-else>
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="btnLoading">AI改写</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { reactive, ref } from 'vue'
import { aiRewriteBackground, aiRewriteGoal } from '@/api/script'
import Typed from 'typed.js';
// import { Typed } from "@duskmoon/vue3-typed-js";
// import type { TypedOptions } from "@duskmoon/vue3-typed-js";
// import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'sendBackdrop'])
// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  preText: {
    type: String,
    default: ''
  },
  dialogType: {
    type: String,
    default: ''
  },
})
const titleMap = new Map([
  ['backdrop', '背景'],
  ['goal', '目标'],
])
const title = computed(() => titleMap.get(props.dialogType))

const options = {
  strings: ['Hello', 'World'],
  typeSpeed: 50,
  backSpeed: 10,
  loop: false,
  showCursor: false,
  onComplete: () => {
    handleDisabled.value = false
  }
};
const formParamsRef = ref()
const btnLoading = ref(false)
const hasAiText = ref(false)
const handleDisabled = ref(false)
// console.log(props.preText)

const initForm = {
  background:  '',
}

watch(()=>props.visible,(val)=>{
  console.log(val)
  formParams.value.background=props.preText
})
// onMounted(()=>{
//   formParams.value.background=props.preText
// })

const backdrop = ref('')
const formParams = ref(initForm)
import type { FormRules } from 'element-plus'
interface RuleForm {
  background: string
}
let typed: any = null

const checkFontLengthLimit = (rule: any, value: any, callback: any) => {
  if (value.length === 0) {
    callback(new Error('请输入'+title.value))
  } else {
    callback()
  }
}

const rules = reactive<FormRules<RuleForm>>({
  background: [
    {
      required: true,
      validator: checkFontLengthLimit,
      trigger: ["blur"]
   },
  ],
})
watch(() => props.preText, (val) => {
  formParams.value.background = val
})


// 弹窗组件显示隐藏
const visibleShow = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})


const closeDialog = () => {
  if (!hasAiText.value) {
    formParamsRef.value.resetFields()
  }
  // formParams.value.background = ''
  hasAiText.value = false
  formParams.value = {...initForm}
  visibleShow.value = false
}

const sendBackdrop = () => {
  closeDialog()
  emits('sendBackdrop', backdrop.value)
}

const getAiData = async () => {
  btnLoading.value = true;
  handleDisabled.value = true
  const Fn = props.dialogType === 'backdrop' ? aiRewriteBackground : aiRewriteGoal
  await Fn({ original: formParams.value.background }).then((res) => {
    hasAiText.value = true
    // handleDisabled.value = true
    backdrop.value = res.data
    if (typed) {
      typed.destroy()
      typed = null
    }
    updateTypedText(res.data)

  }).finally(() => {
    btnLoading.value = false;

  });
}
const updateTypedText = (newText: string) => {
  console.log(newText)
  options.strings = [newText];
  typed = new Typed('#typedjs', options);
  if (typed) {
    typed.reset(); // 重置动画
  }
}
// 改写
const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      getAiData()
    } else {
      console.log('error submit!!');
      return false;
    }
  });

}
onMounted(() => {
})
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-container {
  display: flex;
  flex-direction: column;
  padding: 0 15px;
  min-height: 200px;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}

.title {
  font-size: 16px;
  font-weight: bold;
}

.before,
.after {
  display: flex;
  flex-direction: column;
  gap: 8px 0;
}

.tips {
  font-size: 12px;
  color: #999
}

.typedClass {
  // display: flex;

  // align-items: center;
  // font-size: 40px;
  .typing {
    // position: absolute;
    // right: 0;
  }

  :deep(.typed-cursor) {
    // font-size: 70px;
    // margin: 0.6em 0;
  }
}

// :deep(.el-form){
//   padding: 0 15px;
// }
</style>
