<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title flex">
            <img src="@/assets/icons/png/back.png" style="cursor: pointer;" @click="back()" class="back-icon" />
            {{ rowName }}</span
          >
          <div class="search-container">
            <el-tooltip content="刷新状态" placement="top">
              <el-button @click="refresh">
                <el-icon> <Refresh /> </el-icon
              ></el-button>
            </el-tooltip>
            <el-dropdown style="margin: 0 8px;" v-if="knowledgeBaseItemList.length > 0 && knowledgeBaseItemList[0].type === 2">
              <el-button>
                <!-- <el-icon>
                    <SetUp />
                  </el-icon> -->
                <img src="@/assets/icons/png/more.png" style="width:20px;height: 20px;" />
              </el-button>
              <template #dropdown>
                <el-dropdown-item @click="labelBatchKbItem" :disabled="multipleSelection.length === 0">AI标注</el-dropdown-item>
                <el-dropdown-item @click="colorBatchKbItem" :disabled="multipleSelection.length === 0"> AI润色</el-dropdown-item>
                <el-dropdown-item @click="handleBatchVoiceKbItem" :disabled="multipleSelection.length === 0"> 处理音频内容</el-dropdown-item>
                <el-dropdown-item @click="deleteBatchItem" :disabled="multipleSelection.length === 0"> 删除</el-dropdown-item>
              </template>
            </el-dropdown>
            <el-button type="primary" icon="Plus" @click="handleAdd">导入文件</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="knowledgeBaseItemList"
        ref="tableRef"
        @row-click="handleRowClick"
        @sort-change="sortChange"
        :default-sort="{ prop: 'name', order: 'descending' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="35" align="center" />
        <el-table-column label="名称" align="left" prop="name" min-width="300" sortable="custom" :sort-orders="['ascending', 'descending']">
          <template #default="scope">
            <div class="flex">
              <img src="@/assets/icons/png/file1.png" v-if="scope.row.type === 1" class="icon" />
              <img src="@/assets/icons/png/file2.png" v-if="scope.row.type === 2" class="icon" />
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="描述" align="left" prop="labelInfo" width="300">
          <template #default="scope">
            <div
              :style="{ color: (scope.row.labelInfo && scope.row?.labelInfo?.content && scope.row.labelInfo?.content.length > 1500) ? '#ff0000' : '' }"
              style="width: 100%;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;"
            >
              {{ scope.row.labelInfo ? scope.row.labelInfo.content || '-' : '-' }}
              <!-- {{scope.row.labelInfo.content.length}} -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="大小" align="left" prop="size" width="80px" />
        <el-table-column label="格式" align="left" prop="fileSuffix" width="70px">
          <template #default="scope">
            {{ scope.row.fileSuffix?.replace('.', '') }}
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="left" prop="createTime" width="140px" sortable="custom" :sort-orders="['ascending', 'descending']" />
        <el-table-column label="状态" align="left" prop="status" width="170px">
          <template #default="scope">
            <div class="flex">
              <div class="dot" :style="{ backgroundColor: colorMap.get(scope.row.status) }"></div>
              {{ statusMap.get(scope.row.status) }}
              <el-tooltip v-if="scope.row.status === 8&&scope.row.errorMsg" effect="dark" :content="scope.row.errorMsg" placement="top">
                <img src="@/assets/icons/svg/error.svg" style="width: 16px; height: 16px;margin-left: 4px;margin-bottom: 2px;" />
              </el-tooltip>
              <el-tooltip
                v-if="[3, 6, 7, 8].includes(scope.row.status) && !scope.row.errorMsg"
                effect="dark"
                :content="scope.row.errorMsg"
                placement="top"
                popper-class="popper"
              >
                <template #content>
                  <div class="flex" style="flex-direction: column;align-items: flex-start;">
                    <span>文档处理失败，可能是以下原因： </span>
                    <span>未识别到文字内容，有些文件虽然是文档格式，但是实际上都是图片，请转成图片后上传到图片类型知识库进行使用。 </span>
                  </div>
                </template>
                <img src="@/assets/icons/svg/error.svg" style="width: 16px; height: 16px;margin-left: 4px;margin-bottom: 2px;" />
              </el-tooltip>
              <el-button link type="primary" v-if="[3, 6, 7, 8].includes(scope.row.status) && !scope.row.errorMsg" @click.stop="retry(scope.row)"
                >重试</el-button
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column width="250px" label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="flex button" style="justify-content: center;">
              <el-button link type="primary" :disabled="scope.row.status !== 2" v-if="scope.row.type === 1" @click.stop="gotoSection(scope)"
                >查看切片</el-button
              >
              <el-button
                link
                type="primary"
                :disabled="[5, 1].includes(scope.row.status)"
                v-if="scope.row.type === 2"
                @click.stop="showEditLabelFn(scope)"
                >编辑</el-button
              >
              <el-button v-if="scope.row.type === 1" link type="primary" text @click.stop="handleDelete(scope)">删除</el-button>

              <el-button
                link
                type="primary"
                :disabled="[5, 1].includes(scope.row.status)"
                v-if="scope.row.type === 2 && !scope.row.voiceInfo"
                @click.stop="showVoiceDialog(scope)"
                >上传音频</el-button
              >
              <el-button
                link
                type="primary"
                :disabled="[5, 1].includes(scope.row.status)"
                v-if="scope.row.type === 2 && scope.row.voiceInfo"
                @click.stop="showVoiceDetailDialog(scope)"
                >查看音频</el-button
              >
              <el-dropdown v-if="scope.row.type === 2" :disabled="[5, 1].includes(scope.row.status)" @click.stop>
                <el-button type="primary" @click.stop :disabled="[5, 1].includes(scope.row.status)" link> 更多 </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="labelKbItem(scope)">AI标注</el-dropdown-item>
                    <el-dropdown-item @click="handleColorKbItem(scope)"> AI润色</el-dropdown-item>
                    <el-dropdown-item @click="handleVoiceKbItem(scope)"> 处理音频内容</el-dropdown-item>
                    <el-dropdown-item @click="handleDelete(scope)"> 删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
    </el-card>
    <EditLabelDialog
      v-model:visible="showEditLabel"
      :is-asc="queryParams.isAsc"
      :order-by-column="queryParams.orderByColumn"
      :kb-id="id"
      :active-id="currentRowId"
      @success="reload"
    >
    </EditLabelDialog>
    <VoiceDialog v-model:visible="showVoice" :active-id="currentRowId" @success="reload"> </VoiceDialog>
    <VioceDetail v-model:visible="showVoiceDetail" :active-id="currentRowId" @success="reload"> </VioceDetail>
    <!-- AI标注选择 -->
    <el-dialog v-model="showBatchAiLabel" :close-on-click-modal="false" :show-close="false" width="400px" append-to-body>
      <template #title>
        <div style="font-size: 16px;margin-top: 12px;">
          {{ batchTypeMap.get(batchType) }}
        </div>
      </template>
      <el-radio-group v-model="currentMergeMethod" style="display: flex;" v-if="batchType === 1 || batchType === 2">
        <el-radio :value="1" :label="1">
          <span>添加在末尾，与现有标注内容合并</span>
        </el-radio>
        <el-radio :value="2" :label="2">
          <span>替换原内容，替换现有标注内容</span>
        </el-radio>
      </el-radio-group>
      <div class="" v-else-if="batchType === 3">
        <li>润色将去除标注中的重复、冗余、口语化内容</li>
        <li>如果标注内容为空将不执行润色。</li>
        <li>润色后的结果将覆盖原标注内容，请谨慎操作。</li>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeLabelDialog">取 消</el-button>
          <el-button type="primary" :loading="batchLoading" @click="handleBatchAiLabelOrVoice">{{
            batchTypeButtonMap.get(batchType) }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { queryKnowledgeBaseItemList, removeKnowledgeBaseItem, retryKnowledgeBase, batchAutoLabel, batchColor, batchRemoveKnowledgeBaseItem, batchHandleVoice } from '@/api/knowledgeBase';
import { knowledgeItemListQuery, KnowledgeBaseItemVo } from '@/api/knowledgeBase/types';
// import router from '@/router';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const knowledgeBaseItemList = ref<KnowledgeBaseItemVo[]>([]);
const multipleSelection = ref<KnowledgeBaseItemVo[]>([])
const loading = ref(true);
const total = ref(0);
const currentRowId = ref('')
const currentRowIds = ref<string[]>([])
import { ElMessageBox } from 'element-plus'
import EditLabelDialog from './components/editLabelDialog.vue';
import VoiceDialog from './components/voiceDialog.vue';
import VioceDetail from './components/voiceDetail.vue';
import { deepClone } from '@/utils';
const emits = defineEmits(['change-type']);
const type = ref<number>(0)
const showEditLabel = ref(false)
const initFlag = ref(false)
const showVoice = ref(false)
const showVoiceDetail = ref(false)
const showBatchAiLabel = ref(false)
const currentMergeMethod = ref(1)
const batchLoading = ref(false)
const batchType = ref(1)
let intervalId: any = null
let time = 0
const batchTypeMap = new Map([
  [1, '请选择AI标注内容的合并方式'],
  [2, '请选择音频内容处理方式'],
  [3, '确认AI润色吗？'],
])
const batchTypeButtonMap = new Map([
  [1, '开始标注'],
  [2, '开始执行'],
  [3, '开始润色'],
])
const statusMap = new Map([
  [1, '处理中'],
  [2, '可用'],
  [3, '失败'],
  [4, '待标注'],
  [5, '标注中'],
  [6, '标注失败'],
  [7, '语音识别失败'],
  [8, 'AI润色失败']
])
const colorMap = new Map([
  [1, '#447AFF'],
  [2, '#2DDF29'],
  [3, '#FF1C1C'],
  [4, '#C3C3C3'],
  [5, '#2387F3'],
  [6, '#2387F3'],
  [7, '#2387F3'],
  [8, '#2387F3']
])

const data = reactive<PageQueryData<knowledgeItemListQuery>>({
  queryParams: {
    kbId: '',
    isAsc: 'desc',
    orderByColumn: 'name',
  }
});
const { queryParams } = toRefs(data);

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  rowName: {
    type: String,
    default: ''
  },
  initFlag: {
    type: Boolean,
    default: false
  },
})

/**刷新 */
const refresh = () => {
  if (intervalId) {
    clearInterval(intervalId);
    time = 0
  }
  getList()
}

const showVoiceDialog = (scope: any) => {
  showVoice.value = true
  currentRowId.value = scope.row.id
}
const showVoiceDetailDialog = (scope: any) => {
  showVoiceDetail.value = true
  currentRowId.value = scope.row.id
}


/** 查询列表 */
const getList = async () => {
  loading.value = true;
  intervalId = setInterval(async () => {
    const res = await queryKnowledgeBaseItemList(queryParams.value);
    loading.value = false;
    if (res.code === 200) {
      if (res.rows.length > 0) {
        type.value = res.rows[0].type
        if (initFlag.value && type.value === 2) {
          currentRowId.value = res.rows[0].id
          showEditLabel.value = true
        }
        nextTick(() => {
          initFlag.value = false
        })
        time++
        res.rows.forEach((item: KnowledgeBaseItemVo) => {
          let num = Number(item.size) / 1024
          item.size = num > 1024 ? Math.round((num / 1024) * 10) / 10 + 'MB' : Math.round(num * 10) / 10 + 'KB'
        })
        total.value = res.total;

        if (JSON.stringify(knowledgeBaseItemList.value) !== JSON.stringify(res.rows)) {
          knowledgeBaseItemList.value = deepClone(res.rows)
        }
        const index = knowledgeBaseItemList.value.findIndex(item => (item.status === 1 || item.status === 5))
        console.log(index)

        if (index === -1 || time > 20) {
          console.log(intervalId)
          if (intervalId) {
            clearInterval(intervalId);
            intervalId = null
            time = 0
          }

        }
      } else {
        knowledgeBaseItemList.value = deepClone(res.rows)
        clearInterval(intervalId);
        intervalId = null
      }
    } else {
      clearInterval(intervalId);
      intervalId = null
    }
  }, 1000); // 每1秒查询一次


}
/**删除 */
const handleDelete = async (scope: any) => {
  ElMessageBox.confirm(
    '此操作不可恢复',
    '确定要删除吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      // dangerouslyUseHTMLString: true,
      type: 'warning',
    }
  )
    .then(async () => {
      loading.value = true;
      await removeKnowledgeBaseItem(scope.row.id).then((res) => {
        loading.value = false;
        if (res.code === 200) {
          proxy.$modal.msgSuccess('删除成功');
          if (intervalId) {
            clearInterval(intervalId);
            intervalId = null
          }
          getList();
        }
      });
    })
    .catch(() => {
    })

}

const gotoSection = (scope: any) => {
  emits('change-type', { flagType: 'section', id: scope.row.id, rowName: scope.row.name, preId: props.id, preRowName: props.rowName })
}

const retry = async (row: any) => {
  const res = await retryKnowledgeBase(row.id)
  if (res.code === 200) {
    getList()
  }
}

const showEditLabelFn = (scope: any) => {
  currentRowId.value = scope.row.id
  showEditLabel.value = true
}
const handleRowClick = (row, event, column) => {
  if (row.type === 1) {
    emits('change-type', { flagType: 'section', id: row.id, rowName: row.name, preId: props.id, preRowName: props.rowName })
  } else {
    currentRowId.value = row.id
    showEditLabel.value = true
  }

}


const back = () => {
  emits('change-type', { flagType: 'list' })
}


const handleAdd = () => {
  emits('change-type', { flagType: 'importDoc', id: props.id, })
}

const reload = () => {
  currentRowId.value = ''
  getList();
}
onUnmounted(() => {
  // 清理定时器
  if (intervalId) {
    clearInterval(intervalId);
    time = 0
  }
});

const labelKbItem = (scope: any) => {
  showBatchAiLabel.value = true
  currentRowIds.value = [scope.row.id]
  batchType.value = 1
}
const handleVoiceKbItem = (scope: any) => {
  showBatchAiLabel.value = true
  batchType.value = 2
  currentRowIds.value = [scope.row.id]
}

const handleColorKbItem = (scope: any) => {
  showBatchAiLabel.value = true
  batchType.value = 3
  currentRowIds.value = [scope.row.id]
}



const labelBatchKbItem = () => {
  showBatchAiLabel.value = true
  currentRowIds.value = multipleSelection.value.map(item => item.id)
  batchType.value = 1
}

const colorBatchKbItem = () => {
  showBatchAiLabel.value = true
  currentRowIds.value = multipleSelection.value.map(item => item.id)
  batchType.value = 3
}

const handleBatchVoiceKbItem = () => {
  if(multipleSelection.value.filter(item => item.voiceInfo).length === 0) {
    proxy?.$modal.msgError('未检测到音频内容');
    return
  }
  showBatchAiLabel.value = true
  currentRowIds.value = multipleSelection.value.map(item => item.id)
  batchType.value = 2
}

const deleteBatchItem = () => {
  currentRowIds.value = multipleSelection.value.map(item => item.id)
  ElMessageBox.confirm(
    '此操作不可恢复',
    '确定要删除吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      // dangerouslyUseHTMLString: true,
      type: 'warning',
    }
  )
    .then(async () => {
      batchRemoveKnowledgeBaseItem(currentRowIds.value).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('删除成功')
          multipleSelection.value = []
          getList()
        }
      })

    })
    .catch(() => {
    })




}




const colorKbItem = (ids: string[]) => {
  let text = '<div style="text-align: left;">\
  <li>润色将去除标注中的重复、冗余、口语化内容</li>\
  <li>如果标注内容为空将不执行润色。</li>\
  <li>润色后的结果将覆盖原标注内容，请谨慎操作。</li>\
  </div>'
  ElMessageBox.confirm(
    text,
    '确认AI润色吗',
    {
      confirmButtonText: '开始润色',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true,
      type: 'none',
    }
  )
    .then(async () => {

    })
    .catch(() => {
    })
}

/**表格多选 */
const handleSelectionChange = (val: KnowledgeBaseItemVo[]) => {
  multipleSelection.value = val
}
// const orderMap = new Map([
//   ['name', 'max_score'],
//   ['name', 'max_score'],

// ])

// 排序
const sortChange = (column: any) => {
  queryParams.value.orderByColumn = column.prop || '';
  queryParams.value.isAsc = column.order ? column.order === 'ascending' ? 'asc' : 'desc' : 'desc'
  getList()
}

const closeLabelDialog = () => {
  showBatchAiLabel.value = false
  currentMergeMethod.value = 1
}

const handleBatchAiLabelOrVoice = () => {
  batchLoading.value = true
  if (batchType.value === 1) {
    batchAutoLabel({ kbItemIds: currentRowIds.value, mergeMethod: currentMergeMethod.value }).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('操作成功，可稍后查看结果');
        getList()
      }
    }).finally(() => {
      batchLoading.value = false;
      showBatchAiLabel.value = false
      currentMergeMethod.value = 1
    })
  } else if (batchType.value === 2) {
    batchHandleVoice({ kbItemIds: currentRowIds.value, mergeMethod: currentMergeMethod.value }).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('操作成功，可稍后查看结果');
        getList()
      }
    }).finally(() => {
      batchLoading.value = false;
      showBatchAiLabel.value = false
      currentMergeMethod.value = 1
    });
  } else if (batchType.value === 3) {
    batchColor({ kbItemIds: currentRowIds.value }).then((res) => {
      loading.value = false;
      if (res.code === 200) {
        proxy.$modal.msgSuccess('操作成功，可稍后查看结果');
        getList()
      }
    }).finally(() => {
      batchLoading.value = false;
      showBatchAiLabel.value = false
      currentMergeMethod.value = 1
    });;
  }
}








onMounted(() => {
  initFlag.value = props.initFlag
  queryParams.value.kbId = props.id
  getList();
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}



.card-header {
  .el-form-item {
    margin-bottom: 0px;
  }
}

.flex {
  display: flex;
  align-items: center;
  gap: 0 10px;
  justify-content: flex-start;

  .icon {
    width: 20px;
  }
}

.sms {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-30px) translateY(-50%);
}

.success {
  color: var(--el-color-success);
}

.primary {
  color: var(--el-color-primary);
  cursor: pointer;
}

.dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.button {
  :deep(.el-button) {
    margin-left: 0px;
  }
}

:deep(.el-table__row) {
  cursor: pointer;
}
</style>
