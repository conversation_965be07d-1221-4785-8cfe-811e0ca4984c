import request from '@/utils/request';
import { PromptTemplateVo, PromptTemplateDetailVo, PromptPreviewVo } from './types';
import { AxiosPromise } from 'axios';
// 查询列表
export function getPromptTemplate(): AxiosPromise<PromptTemplateVo[]> {
  return request({
    url: '/biz/prompt-template',
    method: 'get'
  });
}
// 上线
export function onlinePrompt(data: { id: string; content: string }): AxiosPromise<string> {
  return request({
    url: '/biz/prompt-template/online',
    method: 'post',
    data
  });
}
// 详情
export function getPromptTemplateDetail(id: string): AxiosPromise<PromptTemplateDetailVo> {
  return request({
    url: '/biz/prompt-template/' + id,
    method: 'get'
  });
}

export function getScriptSelectList(query: { tenantId: string }): AxiosPromise<{ id: string; name: string }[]> {
  return request({
    url: '/biz/script/select-list',
    method: 'get',
    params: query
  });
}

export function getEmanSelectList(query: { tenantId: string }): AxiosPromise<{ id: string; name: string; department: string; title: string }[]> {
  return request({
    url: '/biz/eman/select-list',
    method: 'get',
    params: query
  });
}

export function handlePreview(data: PromptPreviewVo): AxiosPromise<string> {
  return request({
    url: '/biz/prompt-template/preview',
    method: 'post',
    data
  });
}
export function handleSavePromptPreview(data: PromptPreviewVo): AxiosPromise<string> {
  return request({
    url: '/biz/prompt-template',
    method: 'post',
    data
  });
}
