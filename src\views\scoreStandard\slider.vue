<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      title="评分权重"
      v-model="visibleFlag"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="voice">
        <el-slider v-if="voiceSetting" v-model="totalWight" range :min="1" :max="99" :show-tooltip="false" />
        <el-slider v-else v-model="contentWight" :min="1" :max="99" :show-tooltip="false" />
      </div>
      <div class="text-container flex">
        <div class="left">
          内容完整性 <span class="count" v-if="voiceSetting">{{ totalWight[0] }}%</span><span class="count" v-else>{{ contentWight }}%</span>
        </div>
        <div class="slider" v-if="voiceSetting" :style="{ left: totalWight[0] + '%' }">
          <span class="text">讲解技巧</span> <span class="count">{{ totalWight[1] - totalWight[0] }}%</span>
        </div>
        <div class="right" v-if="voiceSetting">
          声音流畅度 <span class="count">{{ 100 - (totalWight[0] + (totalWight[1] - totalWight[0])) }}%</span>
        </div>
        <div class="right" v-else>
          讲解技巧 <span class="count">{{ 100 - contentWight }}%</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visibleFlag = false">取 消</el-button>
          <el-button type="primary" @click="setVoiceWeight">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="user" lang="ts">
const emits = defineEmits(['update:visible', 'sendContentWight'])
const voiceSetting = ref<boolean>(false)
const contentWight = ref(85)
// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  weightArray: {
    type: Array,
    default: () => []
  },
})
const totalWight = ref<number[]>([50, 40])
// 弹窗组件显示隐藏
const visibleFlag = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const setVoiceWeight = () => {
  const array = voiceSetting.value ? totalWight.value : [contentWight.value, 100 - contentWight.value]
  emits('sendContentWight', array)
  visibleFlag.value = false
}

const initWeight = (contentIntegrityWeight: number, skillWeight: number, currentVoiceSetting: boolean) => {
  console.log(contentIntegrityWeight)
  voiceSetting.value = currentVoiceSetting
  console.log(voiceSetting.value)
  if (currentVoiceSetting) {
    totalWight.value = [contentIntegrityWeight, contentIntegrityWeight + skillWeight]
  } else {
   nextTick(() => {
    contentWight.value = contentIntegrityWeight
   })
  }
}
defineExpose({
  initWeight
})
</script>

<style scoped lang="scss">
.voice {
  width: 100%;

  :deep(.el-slider) {
    margin: 30px 0;

    .el-slider__runway {
      height: 50px;
      background-color: #9AAAFF !important;
    }

    .el-slider__bar {
      height: 50px;
    }

    .el-slider__button {
      height: 70px;
      width: 2px;
      background-color: #272C47;
      border: none;
      border-radius: 4px;
    }

    .el-slider__button-wrapper {
      top: -10px !important;
    }
  }


}

.text-container {
  position: relative;
  justify-content: space-between;
  width: 100%;
  color: #999999;

  .slider {
    position: absolute;

    .count {
      left: 10px;
    }
    .text{
      position: absolute;
      top: -100px;
      width: 300px;
      left: 10px;
    }
  }

  .left,
  .right {
    position: relative;
  }

  .count {
    position: absolute;
    top: -55px;
    color: #ffffff;
  }

  .left {
    .count {
      left: 10px;
    }
  }

  .right {
    .count {
      right: 10px;
    }
  }
}
</style>
