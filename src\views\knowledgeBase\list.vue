<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title"> 知识库</span>
          <el-button type="primary" icon="Plus" @click="handleAdd()">创建知识库</el-button>
        </div>
      </template>

      <el-table v-loading="loading" :data="knowledgeBaseList" ref="tableRef" @row-click="handleRowClick">
        <el-table-column label="名称" align="left" prop="name" width="300">
          <template #default="scope">
            <div class="flex">
              <img src="@/assets/icons/png/file.png" v-if="scope.row.type===1" />
              <img src="@/assets/icons/png/fileimg.png" v-if="scope.row.type===2" />
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="文件数量" align="left" prop="fileNum" width="100px" />
        <el-table-column label="类型" align="left" prop="name" width="100px">
          <template #default="scope">
            {{ scope.row.type===1?'文本':'图片' }}
          </template>
        </el-table-column>
        <el-table-column label="描述" align="left" prop="description" min-width="400">
          <template #default="scope">
            {{ scope.row.description || '-' }}
          </template>
        </el-table-column>
        <el-table-column width="300" label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope)" text>查看</el-button>
            <el-button link type="primary" @click.stop="handleEdit(scope)" text>编辑</el-button>
            <el-button link type="primary" @click.stop="handleDelete(scope)" text>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <Edit v-model:visible="showEdit" :id="comAddId" @success="getList" ref="editRef"></Edit>
    <ChooseType
      v-model:visible="showChooseType"
      @change-choose-type="setChooseType"
      title="请选择知识库类型"
      :choose-type-array="chooseTypeArray"
    ></ChooseType>
  </div>
</template>

<script setup lang="ts">
import { getKnowledgeBaseList, removeKnowledgeBase } from '@/api/knowledgeBase';
import { knowledgeListQuery, KnowledgeBaseVo } from '@/api/knowledgeBase/types';
import ChooseType from "@/components/ChooseType/index.vue";
import kb1 from "@/assets/images/kb1.png";
import kb2 from "@/assets/images/kb2.png";

import Edit from "./edit.vue";
// import router from '@/router';
// import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const knowledgeBaseList = ref<KnowledgeBaseVo[]>([]);
const loading = ref(true);
const showChooseType = ref(false)
const total = ref(0);
const showEdit = ref(false)
import { ElMessageBox } from 'element-plus'
const comAddId = ref() // 事件id
const editRef = ref()
const emits = defineEmits(['change-type']);
const chooseTypeArray = [{
  text: '文本类型',
  description: '支持PDF，Word，TXT，PPT四种格式 的文件',
  img: kb1,
  style:{width:'146px',height:'121px'},
  type: 1,
}, {
  text: '图片类型',
  description: '支持JPG，JPEG，PNG格式的图片',
  img:kb2,
  style:{width:'146px',height:'121px'},
  type: 2,
}]

const data = reactive<PageQueryData<knowledgeListQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});
const { queryParams } = toRefs(data);

const handleRowClick = (row, event, column) => {
  emits('change-type', { flagType: 'item', id: row.id, rowName: row.name })
}


/** 查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await getKnowledgeBaseList(queryParams.value);
  knowledgeBaseList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
/**删除 */
const handleDelete = async (scope: any) => {
  ElMessageBox.confirm(
    '<li>此操作无法恢复，请谨慎操作。</li><li>如果有E人关联了此知识库，可能会影响E人回复的准确性。</li>',
    '确定要删除吗？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true,
      type: 'none',
    }
  )
    .then(async () => {
      loading.value = true;
      await removeKnowledgeBase(scope.row.id).then((res) => {
        loading.value = false;
        if (res.code === 200) {
          proxy.$modal.msgSuccess('删除成功');
          queryParams.value.pageNum = 1;
          getList();
        }
      });
    })
    .catch(() => {
    })

}


/**详情 */
const handleDetail = (scope: any) => {
  emits('change-type', { flagType: 'item', id: scope.row.id, rowName: scope.row.name })
}

/**编辑 */
const handleEdit = (scope: any) => {
  editRef.value.initData({ id: scope.row.id, name: scope.row.name, description: scope.row.description })
  showEdit.value = true
  comAddId.value = scope.row.id
}

const handleAdd = () => {
  showChooseType.value = true
  // emits('change-type', { flagType: 'add' })
}
const setChooseType = (type: number) => {
  emits('change-type', { flagType: type === 1 ? 'add' : 'picAdd' })
}


onMounted(() => {
  getList();
})
</script>
<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none
  }
}



.card-header {
  .el-form-item {
    margin-bottom: 0px;
  }
}

.flex {
  display: flex;
  align-items: center;
  gap: 0 10px;
  justify-content: flex-start;

  img {
    width: 20px;
  }
}

.sms {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-30px) translateY(-50%);
}

.success {
  color: var(--el-color-success);
}

.primary {
  color: var(--el-color-primary);
  cursor: pointer;
}

:deep(.el-table__row) {
  cursor: pointer;
}
</style>
