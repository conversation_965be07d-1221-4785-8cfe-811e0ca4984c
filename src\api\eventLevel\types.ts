import { ScriptVo } from '@/api/script/types';
export interface scriptQuery extends PageQuery {
  keyword: string;
  status: string | number;
  type: null | number;
}
// 问答qa
export interface qaVo {
  question: string;
  answer: string;
}
/**
 * ScriptDetailVo
 */
export interface ScriptDetailVo {
  /**
   * 北京
   */
  backdrop: string;
  randomType?: number;
  applicableObject?: string | null;

  /**
   * 目标
   */
  randomNum: number | null;
  goal: string;
  randomFlag: number | null;
  /**
   * 主键
   */
  id: number | string;
  /**
   * 地点
   */
  location: string;
  deptIds: string | null;
  /**
   * 脚本名称
   */
  name: string;
  /**
   * 产品
   */
  product: string;
  /**
   * 问答集合
   */
  qaList: qaVo[];
  /**
   * 评分标准id
   */
  scoringStandardId: number;
  /**
   * 评分标准名称
   */
  scoringStandardName: string;
  /**
   * 状态，1-待上线，2-已上线，3-已作废
   */
  status: number;
  /**
   * 时限(分钟)
   */
  timeLimit: number;
  /**
   * 脚本类型，1-技巧类，2-答题类
   */
  type: number;
  visitor: string;
  department: string;
  keyIssue: string;
  keyInfo: string;
  position: string;
}

/**
 * LevelBo
 */
export interface LevelFormBo {
  /**
   * 主键
   */
  id?: number;
  /**
   * 关卡组
   */
  levelStages: { index: number; detail: LevelStageBo[]; orderNum: number; openTime: string }[];
  errorFlag?: boolean;
  /**
   * 名称
   */
  name: string;
  conditionText?: string;
  [property: string]: any;
}

/**
 * LevelStageBo
 */
export interface LevelStageBo {
  /**
   * 解锁条件组
   */
  condition: LevelStageUnlockConditionBo[] | null;
  /**
   * 主键
   */
  id?: number;
  /**
   * 脚本id
   */
  scriptId: string;
  [property: string]: any;
}

/**
 * LevelStageUnlockConditionBo
 */
export interface LevelStageUnlockConditionBo {
  /**
   * 主键
   */
  id?: number;
  /**
   * 关卡id
   */
  levelStageId?: number;
  /**
   * 得分
   */
  score: number | null;
  /**
   * 脚本id
   */
  scriptId: string;
  scriptName: string;
  /**
   * 次数
   */
  time: number | null;
  [property: string]: any;
}

/**
 * LevelVo
 */
export interface LevelVo {
  /**
   * 主键
   */
  id: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 相关脚本列表
   */
  scriptList: ScriptVo[];
  /**
   * 状态，1-待上线，2-已上线
   */
  status: number;
  [property: string]: any;
}
/**
 * EventLevelStageUserVo
 */
export interface EventLevelStageUserVo {
  deptId: number;
  deptName: string;
  name: string;
  num: number;
  status: number;
  unlockTime: Date;
  [property: string]: any;
}
/**
 * EventLevelStageDetailVo
 */
export interface EventLevelStageDetailVo {
  completionCount: number;
  id: number;
  orderNum: number;
  conditionText?: string;
  unlockConditions: UnlockCondition[];
  [property: string]: any;
}

/**
 * UnlockCondition
 */
export interface UnlockCondition {
  score: number;
  scriptName: string;
  time: number;
  [property: string]: any;
}
/**
 * UserChatListVo
 */
export interface UserChatListVo {
  createTime: Date;
  finishTime: Date;
  reportId: number;
  score: number;
  scriptId: number;
  scriptName: string;
  status: number;
  /**
   * 脚本类型，1-技巧类，2-答题类
   */
  type: number;
  [property: string]: any;
}
