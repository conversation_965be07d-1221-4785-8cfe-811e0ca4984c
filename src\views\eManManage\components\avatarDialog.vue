<template>
  <div>
    <el-dialog
      ref="formDialogRef"
      :title="title"
      v-model="visibleAvatar"
      width="750px"
      append-to-body
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="box-container" v-loading="loading">
        <div class="top-sex-select">
          <div class="gender" :class="currentGender !== 2 ? 'select' : ''" @click="changeTab(1)">
            <img src="@/assets/icons/png/male.png" class="gender-icon" />
          </div>
          <div class="gender" :class="currentGender === 2 ? 'select' : ''" @click="changeTab(2)">
            <img src="@/assets/icons/png/female.png" class="gender-icon" />
          </div>
        </div>
        <div class="avatar-container">
          <div class="avatar-img" v-for="(item, index) in avatarList" :key="index" @click="setCurrentAvatar(item)">
            <el-image :src="item.avatar" :class="avatar.avatar === item.avatar ? 'select-avatar' : ''" />
            <el-tooltip content="点击预览3D效果" placement="top">
              <img
                class="tips-icon"
                src="@/assets/icons/svg/3d.svg"
                @click.stop="showVoice = true, currentVideoUrl = item.videoUrl"
                v-show="item.zipFileUrl"
              />
            </el-tooltip>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex footer" :class="avatar.zipFileUrl ? '' : 'flex-d'">
          <el-form-item v-if="avatar.zipFileUrl" label="3D动态效果" prop="toneName">
            <template #label>
              <span class="label-text">3D动态效果</span>
              <el-tooltip
                content="3D动态效果是利用数字人技术实现的互动效果，可以让对话更逼真更有沉浸感。支持3D动态效果的形象有3D标志，默认开启。"
                placement="top"
                popper-class="popper"
              >
                <el-icon class="label-tip">
                  <img src="@/assets/icons/svg/help1.svg" style=" width: 16px;" />
                </el-icon>
              </el-tooltip>
            </template>
            <el-switch v-model="show3dFlag" />
          </el-form-item>
          <div class="dialog-footer">
            <el-button @click="closeDialog()">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <videoDialog v-model:visible="showVoice" :video-url="currentVideoUrl"></videoDialog>
  </div>
</template>

<script setup name="user" lang="ts">
import { ElMessageBox, ElLoading } from 'element-plus'
import { getMaleAvatarList, getFemaleAvatarList } from "@/api/eman/common";
import videoDialog from './videoDialog.vue';
// import { tr } from 'element-plus/es/locale';
const emits = defineEmits(['update:visible', 'update:gender', 'avatarSuccess'])

export interface avatarObjVo {
    avatar: string, background: string; zipFileUrl: string; videoUrl: string;show3dFlag: boolean;
}
const show3dFlag = ref(true)
const loading = ref(false)
const currentGender = ref(0)
const avatar = ref<avatarObjVo>({} as avatarObjVo)
const avatarList = ref<avatarObjVo[]>([] as avatarObjVo[])
const showVoice = ref(false)
const currentVideoUrl = ref('')
// props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新增'
    },
    gender: {
        type: Number,
        default: 0
    },
    tone: {
        type: Object,
        default: () => { }
    },
    currentAvatar:{
        type:Object,
        default:()=>{}
    }
})

watch(() => props.visible, val => {
    if (val) {
        currentGender.value = props.gender
        _getAvatarList()
    }
})

watch(() => props.currentAvatar, val => {
    avatar.value = val||{}
    show3dFlag.value = val.show3dFlag
})

//头像
const _getAvatarList = async () => {
   loading.value = true
    const fn = currentGender.value === 2 ? getFemaleAvatarList() : getMaleAvatarList()
    const res: any = await fn;
    nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
        loading.value = false
    })
    avatarList.value = res.data
}
/**切换*/
const changeTab = (gender: number) => {
    avatar.value = {} as avatarObjVo
    currentGender.value = gender
    _getAvatarList()
}
/**选中头像 */
const setCurrentAvatar = (item: avatarObjVo) => {
    avatar.value = item
}

// 弹窗组件显示隐藏
const visibleAvatar = computed({
    get() {
        return props.visible
    },
    set(value) {
        emits('update:visible', value)
    }
})
// 修改父组件性别
const genderFlag = computed({
    get() {
        return props.gender
    },
    set(value) {
        emits('update:gender', value)
    }
})

const closeDialog = () => {
    visibleAvatar.value = false
}

const handleSubmit = () => {
    if (!avatar.value?.avatar) {
        ElMessage.error('请选择头像');
        return
    }
    avatar.value.show3dFlag = show3dFlag.value
    if (props.gender !== 0) {
        if (props.gender !== currentGender.value) {
            if (props.tone?.name) {
                ElMessageBox.confirm(
                    '继续修改将重置声音，确定吗', '头像与声音的性别冲突',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }
                ).then(() => {
                    genderFlag.value = currentGender.value === 2 ? 2 : 1
                    emits('avatarSuccess', avatar.value, true)
                    closeDialog()
                }).catch(() => {
                })
            } else {
                genderFlag.value = currentGender.value === 2 ? 2 : 1
                emits('avatarSuccess', avatar.value, false)
                closeDialog()
            }

        } else {
            emits('avatarSuccess', avatar.value)
            closeDialog()
        }
    } else {
        genderFlag.value = currentGender.value === 2 ? 2 : 1
        emits('avatarSuccess', avatar.value, false)
        closeDialog()
    }
}
</script>

<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right-button {
    margin-right: 100px
}

.card-title {
    font-size: 24px;
    font-weight: bold;
}

.footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;



    .top-sex-select {
        width: 300px;
        margin: 0 auto;
        height: 32px;
        background-color: rgba(118, 118, 128, 0.12);
        display: flex;
        border-radius: 6px;
        padding: 2px;
        box-sizing: border-box;

        .gender {
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            cursor: pointer;

            &-icon {
                width: 24px;
            }
        }

        .select {
            background-color: #fff;
            border-radius: 6px;
        }
    }

    .avatar-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        // max-height: 800px;
        // overflow: auto;
        width: 570px;
        margin: 0 auto;
        padding-left: 15px;
        margin-top: 24px;
        max-height: 50vh;
        overflow: auto;

        .avatar-img {
            position: relative;

            .el-image {
                width: 129px;
                height: 129px;
                cursor: pointer;
                border: 3px solid #ffffff;
                border-radius: 16px;
            }

            .tips-icon {
                position: absolute;
                bottom: 15px;
                right: 10px;
                cursor: pointer;
                width: 24px;
                z-index: 1;
                height: 24px;
            }

        }

        .select-avatar {
            border: 3px solid var(--el-color-primary) !important;
        }
    }
}


:deep(.el-card) {
    height: calc(100vh - 84px - 32px);

    .el-card__body {
        height: calc(100vh - 84px - 32px - 162px);
        overflow-y: auto;
    }
}
.flex-d{
    justify-content: flex-end;
}
.el-form-item--default{
    margin-bottom: 0px;
}
.label-tip{
    margin-left: 4px;
    color: #999999;
    font-size: 14px;
    line-height: 20px;
    cursor: pointer;
    align-self: center;
    height: 20px;
    width: 20px;
}
</style>
