<template>
  <div class="p-2">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <img src="@/assets/icons/png/back.png" class="back-icon" @click="router.back()" />{{ route.query.userName }}</span
          >
        </div>
      </template>
      <div class="box-container">
        <el-table v-loading="loading" :data="dataList" @row-click="gotoReportDetail">
          <el-table-column label="序号" align="center" type="index" width="150" />
          <el-table-column label="脚本" align="center" prop="scriptName" />

          <el-table-column label="类型" align="center" prop="type">
            <template #default="scope">
              <span v-if="scope.row.type === 1">技巧类</span>
              <span v-else-if="scope.row.type === 2">答题类</span>
              <span v-else-if="scope.row.type === 3">幻灯片演练1</span>
            </template>
          </el-table-column>
          <el-table-column label="时长" align="center" prop="timeLong">
            <template #default="scope">
              {{ scope.row.durationText || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="得分" align="center" prop="score">
            <template #default="scope">
              {{ scope.row.status === 2 ? scope.row.score : statusMap.get(scope.row.status) }}
            </template>
          </el-table-column>
          <el-table-column label="时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button link type="primary" text @click="gotoReportDetail(scope.row)">查看报告</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { eventLevelStageUserChatDetail } from "@/api/eventLevel";
import { UserChatListVo } from '@/api/eventLevel/types';
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const route = useRoute();
const router = useRouter();
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
});
const statusMap = new Map([
    [1, '无报告'],
    [2, '已生成'],
    [3, '生成错误'],
    [4, '生成中'],
])
const loading = ref(false);
const total = ref(0);
const { queryParams } = toRefs(data);

const dataList = ref<UserChatListVo[]>([])


const gotoReportDetail = (row: any) => {
    router.push({
    name: 'Report-detail', query: {
      id: row.reportId
    }
  });
}


const getList = async () => {
    loading.value = true;
    const res = await eventLevelStageUserChatDetail(route.query.id as string, route.query.userId as string, queryParams.value);
    res.rows.forEach((item: any) => {
        const finishTime = dayjs(item.finishTime)
        const createTime = dayjs(item.createTime)
        const durationTime = finishTime.diff(createTime, 'seconds');
        item.durationText = ''
        if (durationTime) {
            const hours = Math.floor(Number(durationTime) / 3600)
            const min = Math.floor((Number(durationTime) % 3600) / 60)
            const seconds = Number(durationTime) % 60; // 计算剩余秒数
            item.durationText = hours > 0 ? hours + '小时' + min + '分' + seconds + '秒' : min > 0 ? min + '分' + seconds + '秒' : seconds + '秒'
        } else {
            item.durationText = '0秒'
        }
    })
    dataList.value = res.rows;
    total.value = res.total;
    loading.value = false;
    proxy?.$modal.closeLoading();
}

onUnmounted(() => {
    // cache.session.remove('pointQuery')
})
onMounted(() => {
    proxy?.$modal.loading("正在加载");
    getList()
})
</script>
<style scoped lang="scss">
.card-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 0 12px;
    font-size: 24px;
    font-weight: bold;
}

.flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 18px 0;
}

.export {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.box-container {
    display: flex;
    flex-direction: column;
    gap: 12px 0;
    align-items: flex-end;
    margin: 0 auto;
    // padding: 0 30px 30px 30px;
    width: 100%;

}

.box-title {
    justify-content: space-between;
    width: 100%;
    margin-bottom: 18px;
}
</style>
