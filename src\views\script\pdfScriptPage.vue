<template>
  <div class="pdf-script-page">
    <PDFUpload
      v-model:visible="showAddPDF"
      :title="uploadTitle"
      :limit="pptUploadLimit"
      :file-size="500"
      :file-type="['jpg', 'jpeg', 'png', 'pdf','ppt','pptx']"
      @success="onAddSuccess"
    />
    <PDFStatusDialog v-model:visible="showStatus" :parseFileId="parseFileId" @onSuccess="reset" />
  </div>
</template>
<script setup lang="ts">
// import {defineExpose} from "vue";
import { pptParse } from '@/api/script';
import PDFUpload from './components/PDFUpload.vue';
import PDFStatusDialog from './components/PDFStatusDialog.vue';
const emits = defineEmits(['onRest', 'sendPDF'])
const parseFileId = ref('')
const showAddPDF = ref(false)
const pptUploadLimit = ref(1)
const uploadTitle = ref('创建幻灯片演练')
const showStatus = ref(false)
const onAddSuccess = (fileList: any,product:string,aiPPTParseFlag:boolean) => {
  // console.log('onAddSuccess', fileLiet)
  // 解析幻灯片，加载loading
  const fileExt=fileList[0].url.split('.').pop()
  if (fileExt === 'pdf'||fileExt === 'ppt'||fileExt === 'pptx') {
   const pageLoading = ElLoading.service({
      lock: true,
      text: '正在解析',
      // background: 'rgba(0, 0, 0, 0.7)',
    })
    pptParse({ parseFileOssId: fileList[0].ossId,product,aiPPTParseFlag }).then((res) => {

      if (res.code === 200) {
        showStatus.value = true
        showAddPDF.value = false
        parseFileId.value = res.data
      } else {
        showAddPDF.value = false
      }
    }).finally(() => {
      pageLoading.close()
    })
  } else {
    // 添加pdf表单
    emits('sendPDF', fileList,product)
  }

}
const setShowPDFUpload = (val: boolean) => {
  showAddPDF.value = val
}
const reset = () => {
  parseFileId.value = ''
  showStatus.value = false
  console.log("成功2")
  emits('onRest')
}
defineExpose({
  setShowPDFUpload
})
</script>
<style scoped lang="scss"></style>
