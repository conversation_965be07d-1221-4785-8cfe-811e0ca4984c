import { userResult } from '../user/types';

export interface scriptQuery extends PageQuery {
  keyword: string;
  status: string | number;
  type: null | number;
  queryDeptIds: string[] | null;
  products: [];
  queryDeptTemIds: string[];
}
// 问答qa
export interface qaVo {
  question: string;
  answer: string;
  timeLimit: number;
}
/**
 * ScriptDetailVo
 */
export interface ScriptDetailVo {
  limitSingleQaType: number;
  singleQaTimeLimit: number;
  timeLimitType: number;
  pptScoringType?: string;
  /**
   * 北京
   */
  parseStatus: number;
  backdrop: string;
  onlyEnableInEventLevel: boolean;
  randomType?: number;
  applicableObject?: string | null;

  /**
   * 目标
   */
  randomNum: number | null;
  goal: string;
  randomFlag: number | null;
  /**
   * 主键
   */
  id: number | string;
  /**
   * 地点
   */
  location: string;
  deptIds: string | null;
  /**
   * 脚本名称
   */
  name: string;
  /**
   * 产品
   */
  product: string;
  /**
   * 问答集合
   */
  qaList: qaVo[];
  /**
   * ppt
   */
  pptList: PPTBo[];
  /**
   * 评分标准id
   */
  scoringStandardId: number;
  /**
   * 评分标准名称
   */
  scoringStandardName: string;
  /**
   * 状态，1-待上线，2-已上线，3-已作废
   */
  status: number;
  /**
   * 时限(分钟)
   */
  timeLimit: number;
  /**
   * 脚本类型，1-技巧类，2-答题类
   */
  type: number;
  visitor: string;
  department: string;
  keyIssue: string;
  keyInfo: string;
  position: string;
  visitObject: string;
  generateSuccess?: boolean;
}
export interface scriptListResult {
  id: number | string | undefined;
  name: string;
  parseStatus: number;
  deptIds: any;
  deptNames: string;
  status: string | number;
  submitTime: string;
  type: number;
  scoringStandardId: number | string | undefined;
  scoringStandardName: string;
  createTime: string;
  qaList: qaVo[];
}

/**
 * ChatReportScoringStandardVo
 */
export interface ChatReportScoringStandardVo {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 主键
   */
  id?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 状态，1-启用，0-禁用
   */
  status?: number;
  /**
   * 系统默认标准
   */
  system?: boolean;
  /**
   * 类型，1-技巧类，2-答题类
   */
  type?: number;
}

export interface PPTBo {
  id?: string;
  orderNum?: number;
  imageId: string;
  keywords?: string | null;
  imageUrl: string;
  requirement?: string;
  name: string;
  aiCreateFlag?: boolean;
}

/**
 * ScriptBo
 */
export interface scriptFormVo {
  limitSingleQaType: number;
  singleQaTimeLimit: number;
  timeLimitType: number;
  id?: number;
  goal?: string;
  keyInfo?: string;
  pptScoringType?: string;
  deptIds?: string | null;
  randomFlag?: boolean | number | number | null;
  name: string;
  applicableObject?: string | null;
  backdrop?: string;
  randomType?: number | null;
  randomNum?: number | null;
  /**
   * 产品
   */
  product?: string;
  /**
   * 问答集合
   */
  qaList?: ScriptQABo[];
  /**
   * 评分标准id
   */
  scoringStandardId: number | null | string;
  /**
   * 状态，1-待上线，2-已上线，3-已作废
   */
  status?: string;
  /**
   * 时限(分钟)
   */
  timeLimit: number | null;
  /**
   * 脚本类型，1-技巧类，2-答题类
   */
  type: number | null;
  keyIssue?: string[];
  visitObject?: string;
  department?: string | string[] | null;
  location?: string;
  scriptGenerateInfoId?: string;
  pptList?: PPTBo[];
}

/**
 * ScriptQABo
 */
export interface ScriptQABo {
  /**
   * 答案
   */
  answer?: string;
  /**
   * 问题
   */
  question?: string;
  orderNum?: number;
  timeLimit?: number;
}
/**
 * CozeChatDto
 */
export interface CozeChatDto {
  /**
   * 要进行会话聊天的 Bot ID。
   */
  botId: string;
  /**
   * 会话 ID，即会话的唯一标识。
   */
  conversationId: string;
  createdAt: number;
  /**
   * 对话 ID，即对话的唯一标识。
   */
  id: string;
  lastError: LastError;
  status: string;
}

/**
 * LastError
 */
export interface LastError {
  code: number;
  msg: string;
}
/**
 * CozeChatRetrieveDto
 */
export interface CozeChatRetrieveDto {
  /**
   * 要进行会话聊天的 Bot ID。
   */
  botId: string;
  completedAt: number;
  /**
   * 会话 ID，即会话的唯一标识。
   */
  conversationId: string;
  createdAt: number;
  /**
   * 对话 ID，即对话的唯一标识。
   */
  id: string;
  /**
   * 会话的运行状态。取值为：
   * created：会话已创建。
   * in_progress：Bot 正在处理中。
   * completed：Bot 已完成处理，本次会话结束。
   * failed：会话失败。
   * requires_action：会话中断，需要进一步处理。
   */
  status: string;
  usage: Usage;
  [property: string]: any;
}

/**
 * Usage
 */
export interface Usage {
  inputCount: number;
  outputCount: number;
  tokenCount: number;
  [property: string]: any;
}
/**
 * ScriptNotifyBo
 */
export interface ScriptNotifyBo {
  /**
   * 任务描述
   */
  description: string;
  /**
   * 任务名称
   */
  name: string;
  /**
   * 脚本id
   */
  scriptId: string;
  /**
   * 用户id
   */
  userIdList: (string | number)[];
  type?: number;
  userResultList: userResult[];
  [property: string]: any;
}
/**
 * LevelVo
 */
export interface LevelVo {
  /**
   * 主键
   */
  id: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 相关脚本列表
   */
  scriptList: ScriptVo[];
  /**
   * 状态，1-待上线，2-已上线
   */
  status: number;
  [property: string]: any;
}

/**
 * ScriptVo
 */
export interface ScriptVo {
  /**
   * 创建时间
   */
  createTime: Date;
  /**
   * 关联部门id
   */
  deptIds: string;
  /**
   * 关联部门名称
   */
  deptNames: string;
  /**
   * 主键
   */
  id: number;
  /**
   * 脚本名称
   */
  name: string;
  /**
   * 是否通知
   */
  notifyFlag: boolean;
  /**
   * 产品
   */
  product: string;
  /**
   * 问答集合
   */
  qaList: ScriptQAVo[];
  /**
   * 评分标准id
   */
  scoringStandardId: number;
  /**
   * 评分标准名称
   */
  scoringStandardName: string;
  /**
   * 状态，1-待上线，2-已上线，3-已作废
   */
  status: number;
  /**
   * 脚本类型，1-技巧类，2-答题类
   */
  type: number;
  [property: string]: any;
}

/**
 * ScriptQAVo
 */
export interface ScriptQAVo {
  /**
   * 答案
   */
  answer: string;
  id: number;
  orderNum: number;
  /**
   * 问题
   */
  question: string;
  [property: string]: any;
}
/**
 * LevelBo
 */
export interface LevelFormBo {
  /**
   * 主键
   */
  id?: number;
  /**
   * 关卡组
   */
  levelStages: LevelStageBo[];
  errorFlag?: boolean;
  /**
   * 名称
   */
  name: string;
  conditionText?: string;
  timeArray?: string[];
  startDate: string;
  endDate: string;
  deptIds?: string[];
  deptNames?: string[];
  [property: string]: any;
}

/**
 * LevelStageBo
 */
export interface LevelStageBo {
  /**
   * 解锁条件组
   */
  condition: LevelStageUnlockConditionBo[] | null;
  /**
   * 主键
   */
  id?: number;
  /**
   * 脚本id
   */
  scriptId: string;
  [property: string]: any;
}

/**
 * LevelStageUnlockConditionBo
 */
export interface LevelStageUnlockConditionBo {
  /**
   * 主键
   */
  id?: number;
  /**
   * 关卡id
   */
  levelStageId?: number;
  /**
   * 得分
   */
  score: number | null;
  /**
   * 脚本id
   */
  scriptId: string;
  scriptName: string;
  /**
   * 次数
   */
  time: number | null;
  [property: string]: any;
}

export interface ScriptGenerateParams {
  id?: string;
  scriptId?: string;
  visitObject: string;
  visitStage: string;
  keyInfo: string;
}
