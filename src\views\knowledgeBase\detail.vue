<template>
  <div class="p-2">
    <el-dialog ref="formDialogRef" :title="title" v-model="visibleAdd" width="700px" append-to-body :close-on-click-modal="false" :show-close="false">
      <div class="box-container">
        <el-form ref="formParamsRef" :model="formParams" :rules="rules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="18"> </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="back()">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="knowledge" lang="ts">
import { ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emits = defineEmits(['update:visible', 'success'])
const formParamsRef = ref()
const btnLoading = ref(false)
const initForm = {
  deptId: ''
}
const formParams = ref(initForm)
import type { FormRules } from 'element-plus'
interface RuleForm {
//   name: string
//   phoneNumber: string,
//   deptId: string
}

const rules = reactive<FormRules<RuleForm>>({

})

watch(() => props.visible, val => {
  if (val) {

  }
})
// props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增'
  },
  userIdList: {
    type: Array,
    default: () => []
  },

})


// 弹窗组件显示隐藏
const visibleAdd = computed({
  get() {
    return props.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})




const back = () => {
  let bool = false
  Object.keys(formParams.value).forEach((key) => {
    console.log(JSON.stringify(formParams.value[key]))
    if (
      JSON.stringify(formParams.value[key]) !== JSON.stringify('') &&
      JSON.stringify(formParams.value[key]) !== 'null' &&
      JSON.stringify(formParams.value[key]) !== '[]'
    ) {
      bool = true;
      return;
    }
  });
  if (bool) {
    ElMessageBox.confirm(
      '未保存的内容将丢失',
      '确定要返回吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        closeDialog()
      })
      .catch(() => {
      })
  } else {
    closeDialog()
  }
}

const closeDialog = () => {
  formParamsRef.value.resetFields()
  formParams.value = initForm
  visibleAdd.value = false
}

const handleSubmit = () => {
  proxy.$refs['formParamsRef'].validate(async (valid: any) => {
    if (valid) {
      const params = {
        ...formParams.value,
        userIdList: props.userIdList.map((item: userResult) => item.id)
      };
      btnLoading.value=true
      await batchUpdateDeptId(params).then((res) => {
        proxy.$modal.msgSuccess('保存成功');
        closeDialog()
        emits('success')
      }).finally(() => {
        btnLoading.value = false;
      });
    } else {
      console.log('error submit!!');
      return false;
    }
  });
}
</script>

<style scoped lang="scss">
.card-header {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-button {
  margin-right: 100px
}

.card-title {
  font-size: 24px;
  font-weight: bold;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.box-container {
  display: flex;
  flex-direction: column;
  gap: 12px 0;

  .box-title {
    color: #272C47;
    font-size: 20px;
    margin: 18px 0;
    font-weight: bold;

  }
}



:deep(.el-card) {
  height: calc(100vh - 84px - 32px);

  .el-card__body {
    height: calc(100vh - 84px - 32px - 162px);
    overflow-y: auto;
  }
}
</style>
