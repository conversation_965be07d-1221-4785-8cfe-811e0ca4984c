<template>
  <component
    :is="componentList.get(currentCom)"
    :id="currentId"
    :is-edit="isEdit"
    :eman-type="emanType"
    @reload="isReload?getList:''"
    v-model:flag="currentType"
    :listType="listType"
    @change-type="handleChangeType"
  />
</template>

<script setup>
import List from './list.vue'
import Add from './add.vue'
import Detail from './detail.vue'

const componentList = new Map([
  ['list',List],
  ['add', Add],
  ['detail', Detail],
])

const currentCom = ref('list') // 当前组件
const currentId = ref() // 当前行数据id
const currentType = ref()
const isReload = ref()
const listType = ref(null)
const isEdit=ref(false)
const emanType = ref()

// 监听组件切换事件-改变当前显示
const handleChangeType = payload => {
  currentId.value = payload.id
  currentType.value = payload.flagType
  currentCom.value= payload?.flagType
  isReload.value=payload?.isReload
  isEdit.value=payload?.isEdit
  listType.value=payload?.listType
  emanType.value=payload?.emanType
}
</script>

<style lang="scss" scoped></style>
